import { Event } from '@prisma/client';

interface EventStructuredDataProps {
  event: Event & {
    user: { name: string };
    tickets?: { price: number; isAvailable: boolean }[];
  };
}

export default function EventStructuredData({ event }: EventStructuredDataProps) {
  const availableTickets = event.tickets?.filter(t => t.isAvailable) || [];
  const lowestPrice = availableTickets.length > 0 
    ? Math.min(...availableTickets.map(t => t.price))
    : 0;

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Event",
    "name": event.title,
    "description": event.description,
    "startDate": new Date(event.startDate).toISOString(),
    "endDate": new Date(event.endDate).toISOString(),
    "eventStatus": "https://schema.org/EventScheduled",
    "eventAttendanceMode": "https://schema.org/OfflineEventAttendanceMode",
    "location": {
      "@type": "Place",
      "name": event.venue,
      "address": {
        "@type": "PostalAddress",
        "addressLocality": event.location
      }
    },
    "organizer": {
      "@type": "Organization",
      "name": event.user.name
    },
    "offers": availableTickets.length > 0 ? {
      "@type": "Offer",
      "price": lowestPrice,
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock",
      "url": typeof window !== 'undefined' ? window.location.href : ''
    } : undefined,
    "image": event.imagePath ? [event.imagePath] : undefined,
    "category": event.category
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}
