# Getting Started with the QuickTime Events API - Video Tutorial Script

## Introduction (0:00 - 1:00)

Hello and welcome to this tutorial on getting started with the QuickTime Events API. In this video, we'll cover:

1. Setting up your API keys
2. Making your first API request
3. Understanding authentication and permissions
4. Handling rate limits
5. Best practices for using the API

By the end of this tutorial, you'll have everything you need to start integrating with our platform.

## Setting Up API Keys (1:00 - 3:30)

### Creating an Account

Before you can use the API, you'll need to create an account on our platform. If you already have an account, you can skip this step.

1. Go to [yourdomain.com/auth/register](https://yourdomain.com/auth/register)
2. Fill in your details and create your account
3. Verify your email address

### Generating an API Key

Once you have an account, you can generate an API key:

1. Log in to your account
2. Navigate to Dashboard > API Keys
3. Click "Create API Key"
4. Enter a name for your API key (e.g., "Development Key")
5. Select the permissions you need (we'll cover these in more detail later)
6. Set a rate limit (requests per minute)
7. Click "Create"

You'll now see your API key. **Important**: This is the only time you'll see the full key, so make sure to copy it and store it securely.

## Making Your First API Request (3:30 - 6:00)

Now that you have an API key, let's make our first request to the API. We'll start with a simple request to get a list of published events.

### Using cURL

```bash
curl -X GET "https://yourdomain.com/api/events/published" -H "X-API-Key: your-api-key"
```

### Using JavaScript (Fetch API)

```javascript
fetch('https://yourdomain.com/api/events/published', {
  headers: {
    'X-API-Key': 'your-api-key'
  }
})
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));
```

### Using Python (Requests)

```python
import requests

headers = {
    'X-API-Key': 'your-api-key'
}

response = requests.get('https://yourdomain.com/api/events/published', headers=headers)
data = response.json()
print(data)
```

Let's run this code and see what we get back. The API returns a JSON array of events, each with properties like `id`, `title`, `description`, `startDate`, etc.

## Understanding Authentication and Permissions (6:00 - 9:00)

### Authentication Methods

The QuickTime Events API supports API key authentication. You include your API key in the `X-API-Key` header with every request.

### Permissions

When you create an API key, you can specify which permissions to grant. The available permissions are:

- `read:events`: View events
- `write:events`: Create and update events
- `delete:events`: Delete events
- `read:tickets`: View tickets
- `write:tickets`: Create and update tickets
- `delete:tickets`: Delete tickets
- `read:orders`: View orders
- `write:orders`: Create and update orders
- `delete:orders`: Delete orders
- `read:users`: View user information
- `write:users`: Update user information
- `read:analytics`: View analytics data

Each API endpoint requires specific permissions. For example, to get a list of published events, you don't need any special permissions. But to create a new event, you need the `write:events` permission.

Let's try to create a new event:

```javascript
fetch('https://yourdomain.com/api/events/create', {
  method: 'POST',
  headers: {
    'X-API-Key': 'your-api-key',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    title: 'My Event',
    description: 'Event Description',
    startDate: '2023-12-31T18:00:00.000Z',
    endDate: '2024-01-01T02:00:00.000Z',
    location: 'Event Location',
    venue: 'Event Venue',
    category: 'Event Category',
    eventType: 'Event Type'
  })
})
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));
```

If your API key doesn't have the `write:events` permission, you'll get a `403 Forbidden` response.

## Handling Rate Limits (9:00 - 12:00)

To ensure fair usage and system stability, all API endpoints are subject to rate limiting. Rate limits are applied on a per-API key basis.

### Rate Limit Headers

The API includes the following headers in responses to help you track your rate limit usage:

- `X-RateLimit-Limit`: The maximum number of requests allowed per minute
- `X-RateLimit-Remaining`: The number of requests remaining in the current rate limit window
- `X-RateLimit-Reset`: The time at which the current rate limit window resets (in Unix time)

### Handling Rate Limit Errors

If you exceed your rate limit, you'll get a `429 Too Many Requests` response. The response will include a `Retry-After` header indicating how many seconds to wait before retrying.

Here's how to handle rate limits in JavaScript:

```javascript
async function fetchWithRetry(url, options, maxRetries = 5) {
  let retries = 0;
  
  while (retries < maxRetries) {
    try {
      const response = await fetch(url, options);
      
      if (response.status === 429) {
        // Rate limit exceeded
        const resetTime = response.headers.get('X-RateLimit-Reset');
        const waitTime = resetTime ? (parseInt(resetTime) * 1000) - Date.now() : Math.pow(2, retries) * 1000;
        
        console.log(`Rate limit exceeded. Waiting ${waitTime}ms before retrying...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
        retries++;
        continue;
      }
      
      return response;
    } catch (error) {
      retries++;
      if (retries === maxRetries) throw error;
      
      const waitTime = Math.pow(2, retries) * 1000;
      console.log(`Request failed. Waiting ${waitTime}ms before retrying...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
}
```

## Best Practices (12:00 - 14:30)

Here are some best practices for using the QuickTime Events API:

### Security

- Keep your API keys secure and never expose them in client-side code
- Use the principle of least privilege: only grant the permissions your application needs
- Rotate your API keys periodically
- Set appropriate rate limits for your application

### Performance

- Use pagination for endpoints that return lists of items
- Only request the data you need
- Cache responses when appropriate
- Implement exponential backoff for rate limiting

### Monitoring

- Monitor your API usage using the analytics dashboard
- Set up alerts for unusual activity
- Keep track of your rate limit usage

## Conclusion (14:30 - 15:00)

In this tutorial, we've covered:

1. Setting up your API keys
2. Making your first API request
3. Understanding authentication and permissions
4. Handling rate limits
5. Best practices for using the API

You're now ready to start integrating with the QuickTime Events API. For more information, check out our [API documentation](https://yourdomain.com/docs/api-documentation.md) and [developer guide](https://yourdomain.com/docs/developer-guide.md).

If you have any questions or need help, please contact our support <NAME_EMAIL>.

Thanks for watching, and happy coding!
