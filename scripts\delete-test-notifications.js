const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function deleteAllNotifications() {
  try {
    // Delete all notifications
    const result = await prisma.notification.deleteMany({});
    
    console.log(`Successfully deleted ${result.count} notifications`);
  } catch (error) {
    console.error('Error deleting notifications:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
deleteAllNotifications()
  .then(() => console.log('Done!'))
  .catch((error) => console.error('<PERSON><PERSON><PERSON> failed:', error));
