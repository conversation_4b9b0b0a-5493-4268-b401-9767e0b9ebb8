'use client';

import React, { useState } from 'react';
import { Calendar, Clock, MapPin, Video, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { MeetingSchedulerProps } from '@/types/elite-communication';

export default function MeetingScheduler({ 
  eventId, 
  currentUser, 
  recipientId 
}: MeetingSchedulerProps) {
  const [meetingData, setMeetingData] = useState({
    title: '',
    description: '',
    date: '',
    startTime: '',
    endTime: '',
    type: 'VIRTUAL',
    location: '',
    meetingUrl: ''
  });

  const handleScheduleMeeting = () => {
    // TODO: Implement meeting scheduling logic
    console.log('Scheduling meeting:', meetingData);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Schedule a Meeting
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="title">Meeting Title</Label>
            <Input
              id="title"
              placeholder="Enter meeting title"
              value={meetingData.title}
              onChange={(e) => setMeetingData(prev => ({ ...prev, title: e.target.value }))}
            />
          </div>

          <div>
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              placeholder="What would you like to discuss?"
              value={meetingData.description}
              onChange={(e) => setMeetingData(prev => ({ ...prev, description: e.target.value }))}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="date">Date</Label>
              <Input
                id="date"
                type="date"
                value={meetingData.date}
                onChange={(e) => setMeetingData(prev => ({ ...prev, date: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="startTime">Start Time</Label>
              <Input
                id="startTime"
                type="time"
                value={meetingData.startTime}
                onChange={(e) => setMeetingData(prev => ({ ...prev, startTime: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="endTime">End Time</Label>
              <Input
                id="endTime"
                type="time"
                value={meetingData.endTime}
                onChange={(e) => setMeetingData(prev => ({ ...prev, endTime: e.target.value }))}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="type">Meeting Type</Label>
            <Select 
              value={meetingData.type} 
              onValueChange={(value) => setMeetingData(prev => ({ ...prev, type: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select meeting type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="VIRTUAL">
                  <div className="flex items-center">
                    <Video className="h-4 w-4 mr-2" />
                    Virtual Meeting
                  </div>
                </SelectItem>
                <SelectItem value="IN_PERSON">
                  <div className="flex items-center">
                    <User className="h-4 w-4 mr-2" />
                    In-Person Meeting
                  </div>
                </SelectItem>
                <SelectItem value="HYBRID">
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2" />
                    Hybrid Meeting
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {meetingData.type === 'VIRTUAL' && (
            <div>
              <Label htmlFor="meetingUrl">Meeting URL (Optional)</Label>
              <Input
                id="meetingUrl"
                placeholder="https://zoom.us/j/..."
                value={meetingData.meetingUrl}
                onChange={(e) => setMeetingData(prev => ({ ...prev, meetingUrl: e.target.value }))}
              />
            </div>
          )}

          {(meetingData.type === 'IN_PERSON' || meetingData.type === 'HYBRID') && (
            <div>
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                placeholder="Enter meeting location"
                value={meetingData.location}
                onChange={(e) => setMeetingData(prev => ({ ...prev, location: e.target.value }))}
              />
            </div>
          )}

          <Button 
            onClick={handleScheduleMeeting} 
            className="w-full"
            disabled={!meetingData.title || !meetingData.date || !meetingData.startTime}
          >
            Send Meeting Request
          </Button>
        </CardContent>
      </Card>

      {/* Upcoming Meetings */}
      <Card>
        <CardHeader>
          <CardTitle>Your Meetings</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No Meetings Scheduled
            </h3>
            <p className="text-gray-600">
              Your scheduled meetings will appear here.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
