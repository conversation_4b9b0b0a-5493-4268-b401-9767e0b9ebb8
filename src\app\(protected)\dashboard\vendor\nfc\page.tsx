'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle, 
  CardFooter 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { 
  Loader2, 
  CreditCard, 
  History, 
  BarChart3, 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  Calendar 
} from 'lucide-react';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface VendorProfile {
  id: string;
  businessName: string;
  verificationStatus: string;
  totalNfcTransactions: number;
  nfcTransactionRevenue: number;
}

interface Event {
  id: string;
  title: string;
  startDate: string;
  endDate: string;
  participationStatus: string;
}

export default function NFCPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [profile, setProfile] = useState<VendorProfile | null>(null);
  const [activeEvents, setActiveEvents] = useState<Event[]>([]);
  
  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        
        // Fetch vendor profile
        const profileResponse = await fetch('/api/vendors/profile');
        
        if (!profileResponse.ok) {
          if (profileResponse.status === 404) {
            router.push('/dashboard/vendor/create-profile');
            return;
          }
          throw new Error('Failed to fetch vendor profile');
        }
        
        const profileData = await profileResponse.json();
        setProfile(profileData);
        
        // Check if vendor is verified
        if (profileData.verificationStatus !== 'APPROVED') {
          setError('Your vendor account is not verified. Please complete the verification process to access NFC features.');
          setLoading(false);
          return;
        }
        
        // Fetch vendor events
        const eventsResponse = await fetch('/api/vendors/events');
        
        if (eventsResponse.ok) {
          const eventsData = await eventsResponse.json();
          
          // Filter active events (approved participation and event is ongoing or upcoming)
          const now = new Date();
          const active = eventsData.filter((event: Event) => 
            event.participationStatus === 'APPROVED' && 
            new Date(event.endDate) >= now
          );
          setActiveEvents(active);
        }
        
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
        toast({
          title: 'Error',
          description: 'Failed to load required data',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }
    
    fetchData();
  }, [router]);
  
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading...</span>
      </div>
    );
  }
  
  if (error || !profile) {
    return (
      <Card className="w-full max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center text-red-500">
            <AlertCircle className="h-5 w-5 mr-2" />
            Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p>{error || 'Failed to load vendor profile'}</p>
          <Button 
            variant="outline" 
            className="mt-4"
            onClick={() => router.push('/dashboard/vendor')}
          >
            Return to Dashboard
          </Button>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">NFC Transactions</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>NFC Terminal</CardTitle>
            <CardDescription>
              Process customer purchases using NFC cards at events
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center py-6">
              <div className="bg-blue-100 p-6 rounded-full">
                <CreditCard className="h-12 w-12 text-blue-600" />
              </div>
            </div>
            
            {activeEvents.length > 0 ? (
              <div className="bg-blue-50 p-4 rounded-md mb-4">
                <div className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-blue-700">Active Events Available</h4>
                    <p className="text-blue-600 text-sm mt-1">
                      You have {activeEvents.length} active event{activeEvents.length > 1 ? 's' : ''} where you can process NFC transactions
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-yellow-50 p-4 rounded-md mb-4">
                <div className="flex items-start">
                  <Clock className="h-5 w-5 text-yellow-500 mr-2 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-yellow-700">No Active Events</h4>
                    <p className="text-yellow-600 text-sm mt-1">
                      You need to participate in an event to process NFC transactions
                    </p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button 
              className="w-full"
              onClick={() => router.push('/dashboard/vendor/nfc/terminal')}
              disabled={activeEvents.length === 0}
            >
              <CreditCard className="mr-2 h-4 w-4" />
              Open NFC Terminal
            </Button>
          </CardFooter>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Transaction History</CardTitle>
            <CardDescription>
              View and manage your NFC transaction history
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center py-6">
              <div className="bg-purple-100 p-6 rounded-full">
                <History className="h-12 w-12 text-purple-600" />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-gray-100 p-4 rounded-md">
                <p className="text-sm text-gray-500">Total Transactions</p>
                <h3 className="text-xl font-bold">{profile.totalNfcTransactions || 0}</h3>
              </div>
              
              <div className="bg-gray-100 p-4 rounded-md">
                <p className="text-sm text-gray-500">Total Revenue</p>
                <h3 className="text-xl font-bold">K{(profile.nfcTransactionRevenue || 0).toLocaleString()}</h3>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button 
              variant="outline"
              className="w-full"
              onClick={() => router.push('/dashboard/vendor/nfc/transactions')}
            >
              <History className="mr-2 h-4 w-4" />
              View Transaction History
            </Button>
          </CardFooter>
        </Card>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>NFC Analytics</CardTitle>
          <CardDescription>
            Insights and statistics for your NFC transactions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="bg-gray-100 p-6 rounded-full mx-auto mb-4">
                <BarChart3 className="h-12 w-12 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium mb-2">Analytics Coming Soon</h3>
              <p className="text-gray-500 max-w-md mx-auto">
                Detailed analytics for your NFC transactions will be available soon. 
                Check back later for insights on your sales performance.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
