
import Image from "next/image";
import teenga from "../../../../public/images/m.jpg";
import woman from "../../../../public/images/gym.jpg";
import girls from "../../../../public/images/football.jpg";
import { DateTimeComponent } from "../time/Time";

const HeroSection = () => (
  <section
    className="relative flex flex-col items-start justify-center p-6 mb-5 mx-4 md:mx-10 min-h-[60vh] md:min-h-[68vh] rounded-3xl overflow-hidden shadow-2xl"
    style={{
      backgroundImage: `url(${girls.src})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
    }}
  >
    {/* Dark overlay for better text visibility */}
    <div className="absolute inset-0 bg-black/50 z-0"></div>

    {/* Gradient overlay for additional visual appeal */}
    <div className="absolute inset-0 bg-gradient-to-r from-black/30 via-transparent to-black/20 z-0"></div>

    <div className="max-w-full md:max-w-5xl z-10 text-left relative">
      <div className="backdrop-blur-sm bg-black/20 p-6 rounded-2xl border border-white/10">
        <p className="text-white text-sm md:text-lg mb-2 font-medium"><DateTimeComponent /></p>
        <h1 className="text-3xl md:text-5xl font-bold text-white mb-4 drop-shadow-lg">
          EVENTS, MEETUPS <br className="hidden md:block" /> & CONFERENCES
        </h1>
        <div className="flex flex-col md:flex-row justify-start items-center text-white mb-6">
          <div className="mx-2 md:mx-4 flex items-center bg-white/10 px-3 py-2 rounded-xl backdrop-blur-sm">
            <span className="material-icons-outlined"></span>
            <span className="ml-2 text-sm md:text-base font-medium">30,000 Seats</span>
          </div>
          <div className="mx-2 md:mx-4 flex items-center bg-white/10 px-3 py-2 rounded-xl backdrop-blur-sm mt-2 md:mt-0">
            <span className="material-icons-outlined"></span>
            <span className="ml-2 text-sm md:text-base font-medium">10 Speakers</span>
          </div>
        </div>
        <p className="text-white text-sm md:text-lg mb-6 font-medium drop-shadow-md">Mulungushi Conference, Lusaka</p>
        <div className="flex flex-col md:flex-row justify-start space-y-2 md:space-y-0 md:space-x-4">
          <a href="/book" className="px-6 py-3 md:px-8 md:py-4 bg-blue-600 hover:bg-blue-700 text-white rounded-2xl text-center font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">Book Now</a>
          <a href="/details" className="px-6 py-3 md:px-8 md:py-4 bg-orange-500 hover:bg-orange-600 text-white rounded-2xl text-center font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">View Details</a>
        </div>
      </div>
    </div>
  </section>
);

export default HeroSection;

