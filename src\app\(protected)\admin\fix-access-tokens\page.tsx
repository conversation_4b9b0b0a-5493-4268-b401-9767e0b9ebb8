'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ensureAllUsersHaveAccessTokens } from '@/actions/generate-access-token';
import { AlertCircle, CheckCircle, ShieldAlert } from 'lucide-react';

export default function FixAccessTokensPage() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFixAccessTokens = async () => {
    setIsProcessing(true);
    setError(null);
    setResult(null);

    try {
      const response = await ensureAllUsersHaveAccessTokens();
      setResult(response);
      
      if (response.error) {
        setError(response.error);
      }
    } catch (err) {
      console.error('Error fixing access tokens:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="container mx-auto py-8 max-w-4xl">
      <Card className="border-2 border-gray-200 shadow-md">
        <CardHeader className="bg-gray-50">
          <CardTitle className="text-2xl font-bold text-center">Fix User Access Tokens</CardTitle>
          <CardDescription className="text-center">
            This utility will ensure all users have access tokens
          </CardDescription>
        </CardHeader>

        <CardContent className="pt-6">
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {result && result.success && (
            <Alert variant="default" className="mb-6 bg-green-50 border-green-200">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertTitle className="text-green-800">Success</AlertTitle>
              <AlertDescription className="text-green-700">
                {result.message}
                {result.count !== undefined && (
                  <p className="mt-2">
                    {result.count === 0 
                      ? 'All users already have access tokens.' 
                      : `Updated ${result.count} users with new access tokens.`}
                  </p>
                )}
              </AlertDescription>
            </Alert>
          )}

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <ShieldAlert className="h-5 w-5 text-blue-500 mt-0.5 mr-2" />
              <div>
                <h4 className="font-medium text-blue-800">About Access Tokens</h4>
                <p className="text-sm text-blue-700 mt-1">
                  Access tokens are required for API access and certain features of the application.
                  Users who register through custom forms may not have access tokens assigned automatically.
                  This utility will fix that issue by generating tokens for all users who don't have one.
                </p>
              </div>
            </div>
          </div>

          <Button 
            onClick={handleFixAccessTokens}
            disabled={isProcessing}
            className="w-full"
          >
            {isProcessing ? (
              <>
                <span className="mr-2">Processing...</span>
                <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              </>
            ) : (
              'Fix Access Tokens for All Users'
            )}
          </Button>

          {result && (
            <div className="mt-6 p-4 bg-gray-100 rounded-md">
              <h3 className="text-sm font-medium mb-2">Result Details:</h3>
              <pre className="text-xs overflow-auto max-h-60">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>

        <CardFooter className="bg-gray-50 border-t flex flex-col items-center text-center p-4">
          <p className="text-sm text-gray-600">
            This utility should only be run by administrators.
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}
