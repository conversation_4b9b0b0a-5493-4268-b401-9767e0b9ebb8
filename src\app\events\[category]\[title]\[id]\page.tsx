import { notFound, redirect } from 'next/navigation';
import { db } from '@/lib/prisma';
import { parseEventUrl, getCanonicalEventUrl } from '@/lib/utils/events';
import EventDetails from './client';
import EventStructuredData from '@/components/seo/EventStructuredData';
import { Metadata } from 'next';

interface PageProps {
  params: Promise<{
    category: string;
    title: string;
    id: string;
  }>;
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const { id } = resolvedParams;

  try {
    const event = await db.event.findUnique({
      where: { 
        id,
        status: 'Published'
      },
      include: {
        user: {
          select: {
            name: true,
          }
        },
        seoSettings: true,
      }
    });

    if (!event) {
      return {
        title: 'Event Not Found',
        description: 'The event you are looking for could not be found.'
      };
    }

    const canonicalUrl = getCanonicalEventUrl({
      id: event.id,
      title: event.title,
      category: event.category
    });

    const title = event.seoSettings?.title || `${event.title} | Event Details`;
    const description = event.seoSettings?.description ||
      event.description.substring(0, 160) + (event.description.length > 160 ? '...' : '');

    // Enhanced keywords for better SEO
    const keywords = event.seoSettings?.keywords || [
      event.category.toLowerCase().replace(/_/g, ' '),
      'event',
      'tickets',
      event.venue,
      event.location,
      event.eventType.toLowerCase(),
      'booking',
      'entertainment'
    ];

    return {
      title,
      description,
      keywords,
      authors: [{ name: event.user.name }],
      creator: event.user.name,
      publisher: 'Quick Time Events',
      formatDetection: {
        email: false,
        address: false,
        telephone: false,
      },
      openGraph: {
        title,
        description,
        url: canonicalUrl,
        type: 'website',
        images: event.imagePath ? [
          {
            url: event.imagePath,
            width: 1200,
            height: 630,
            alt: event.title,
          }
        ] : [],
        siteName: 'Quick Time Events',
        locale: 'en_US',
      },
      twitter: {
        card: 'summary_large_image',
        title,
        description,
        images: event.imagePath ? [event.imagePath] : [],
        creator: '@quicktimeevents',
      },
      alternates: {
        canonical: canonicalUrl,
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Event Details',
      description: 'View event details and purchase tickets.'
    };
  }
}

export default async function EventPage({ params }: PageProps) {
  const resolvedParams = await params;
  const { category, title, id } = resolvedParams;

  try {
    // Fetch the event to validate the URL structure
    const event = await db.event.findUnique({
      where: { 
        id,
        status: 'Published'
      },
      include: {
        user: {
          select: {
            name: true,
            image: true
          }
        },
        ageRestriction: true,
        ParkingManagement: true,
        tickets: true,
        sponsors: true,
        eventPartners: {
          where: {
            isActive: true,
          },
          include: {
            partner: {
              select: {
                id: true,
                businessName: true,
                partnerType: true,
                logo: true,
                city: true,
                rating: true,
                totalReviews: true,
                acceptsNfcPayments: true,
              },
            },
          },
        },
        seoSettings: true,
        socialSettings: true,
      },
    });

    if (!event) {
      notFound();
    }

    // Generate the canonical URL for this event
    const canonicalUrl = getCanonicalEventUrl({
      id: event.id,
      title: event.title,
      category: event.category
    });

    // Check if the current URL matches the canonical URL structure
    const currentPath = `/events/${category}/${title}/${id}`;
    if (currentPath !== canonicalUrl) {
      // Redirect to the canonical URL if the current URL doesn't match
      redirect(canonicalUrl);
    }

    // Pass the event data to the client component
    return (
      <>
        <EventStructuredData event={event} />
        <EventDetails initialEvent={event} />
      </>
    );

  } catch (error) {
    console.error('Error fetching event:', error);
    notFound();
  }
}

// Generate static params for popular events (optional optimization)
export async function generateStaticParams() {
  try {
    // Generate static params for the most popular events
    const events = await db.event.findMany({
      where: {
        status: 'Published',
      },
      select: {
        id: true,
        title: true,
        category: true,
      },
      take: 100, // Limit to top 100 events for static generation
      orderBy: {
        createdAt: 'desc'
      }
    });

    return events.map((event) => {
      const canonicalUrl = getCanonicalEventUrl(event);
      const parts = canonicalUrl.split('/').filter(Boolean);
      
      if (parts.length === 4) {
        return {
          category: parts[1],
          title: parts[2],
          id: parts[3],
        };
      }
      
      return null;
    }).filter(Boolean);
  } catch (error) {
    console.error('Error generating static params:', error);
    return [];
  }
}
