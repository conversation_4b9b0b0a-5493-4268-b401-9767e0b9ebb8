const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkVendorVerification() {
  try {
    // Check if the specific verification exists
    const verification = await prisma.vendorVerification.findUnique({
      where: { id: 'c7d2bb8f-8ad8-461b-9ed7-849e56214fd3' },
      include: {
        user: {
          select: {
            name: true,
            email: true,
            vendorProfile: true
          }
        }
      }
    });

    if (!verification) {
      console.log('Verification with ID c7d2bb8f-8ad8-461b-9ed7-849e56214fd3 not found');
      return;
    }

    console.log('Verification found:');
    console.log('Status:', verification.status);
    console.log('User:', verification.user.name, verification.user.email);
    console.log('Created at:', verification.createdAt);
    
    // Check if there are any pending vendor verifications
    const pendingVerifications = await prisma.vendorVerification.findMany({
      where: { status: 'PENDING' },
      include: {
        user: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    console.log('\nTotal pending vendor verifications:', pendingVerifications.length);
    
    if (pendingVerifications.length > 0) {
      console.log('\nPending verifications:');
      pendingVerifications.forEach((v, i) => {
        console.log(`${i + 1}. ID: ${v.id}`);
        console.log(`   User: ${v.user.name} (${v.user.email})`);
        console.log(`   Created: ${v.createdAt}`);
        console.log('---');
      });
    }
    
    // If the verification is not pending, update it to pending
    if (verification.status !== 'PENDING') {
      console.log('\nUpdating verification status to PENDING...');
      
      const updated = await prisma.vendorVerification.update({
        where: { id: 'c7d2bb8f-8ad8-461b-9ed7-849e56214fd3' },
        data: { status: 'PENDING' }
      });
      
      console.log('Verification updated successfully. New status:', updated.status);
    }
  } catch (error) {
    console.error('Error checking vendor verification:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkVendorVerification()
  .then(() => console.log('Done!'))
  .catch((error) => console.error('Script failed:', error));
