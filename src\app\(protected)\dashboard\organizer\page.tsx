'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useCurrentRole } from '@/hooks/use-current-role';
import { RoleGate } from '@/components/auth/role-gate';
import { VerificationStatusAlert } from '@/components/organizer/verification-status-alert';
import {
  CalendarDays,
  Users,
  Ticket,
  TrendingUp,
  DollarSign,
  Clock,
  PlusCircle,
  ArrowRight,
  ArrowUpRight,
  BarChart3,
  Calendar,
  CheckCircle,
  AlertCircle,
  ChevronRight,
  Mail,
  MessageSquare,
  Crown,
} from 'lucide-react';
import { TierComparisonWidget } from '@/components/subscription/tier-comparison-widget';
import { CommissionDisplay } from '@/components/subscription/commission-display';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';

// Default empty data structures (will be populated with real data)
const defaultStats = {
  totalEvents: 0,
  activeEvents: 0,
  totalAttendees: 0,
  totalRevenue: 0,
  ticketsSold: 0,
  averageRating: 0,
};

const defaultRecentEvents: any[] = [];
const defaultUpcomingEvents: any[] = [];
const defaultRecentActivity: any[] = [];

export default function OrganizerDashboard() {
  const router = useRouter();
  const user = useCurrentUser();
  const role = useCurrentRole();
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState(defaultStats);
  const [recentEvents, setRecentEvents] = useState(defaultRecentEvents);
  const [upcomingEvents, setUpcomingEvents] = useState(defaultUpcomingEvents);
  const [recentActivity, setRecentActivity] = useState(defaultRecentActivity);
  const [error, setError] = useState<string | null>(null);

  // Fetch real data from the API
  useEffect(() => {
    const fetchDashboardData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch stats
        const statsResponse = await fetch('/api/dashboard/organizer/stats');
        if (!statsResponse.ok) throw new Error('Failed to fetch dashboard statistics');
        const statsData = await statsResponse.json();
        setStats(statsData);

        // Fetch recent events
        const recentEventsResponse = await fetch('/api/dashboard/organizer/recent-events');
        if (!recentEventsResponse.ok) throw new Error('Failed to fetch recent events');
        const recentEventsData = await recentEventsResponse.json();
        setRecentEvents(recentEventsData);

        // Fetch upcoming events
        const upcomingEventsResponse = await fetch('/api/dashboard/organizer/upcoming-events');
        if (!upcomingEventsResponse.ok) throw new Error('Failed to fetch upcoming events');
        const upcomingEventsData = await upcomingEventsResponse.json();
        setUpcomingEvents(upcomingEventsData);

        // Fetch recent activity
        const recentActivityResponse = await fetch('/api/dashboard/organizer/recent-activity');
        if (!recentActivityResponse.ok) throw new Error('Failed to fetch recent activity');
        const recentActivityData = await recentActivityResponse.json();
        setRecentActivity(recentActivityData);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred while fetching dashboard data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Format date
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'ZMW',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center p-6 bg-red-50 rounded-lg border border-red-200 max-w-md">
          <AlertCircle className="h-10 w-10 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-red-800 mb-2">Error Loading Dashboard</h3>
          <p className="text-red-600">{error}</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <RoleGate allowedRole="ORGANIZER">
      <div className="p-4 sm:p-6 max-w-7xl mx-auto">
        {/* Verification Status Alert */}
        <VerificationStatusAlert />

        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Welcome back, {user?.name?.split(' ')[0] || 'Organizer'}</h1>
          <p className="text-gray-500 mt-1">Here&apos;s what&apos;s happening with your events today.</p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-gray-500">Total Events</p>
                  <h3 className="text-3xl font-bold mt-1">{stats.totalEvents}</h3>
                  <p className="text-sm text-green-600 mt-1 flex items-center">
                    <ArrowUpRight className="h-4 w-4 mr-1" />
                    <span>+{stats.growth?.eventsThisMonth || 0} this month</span>
                  </p>
                </div>
                <div className="bg-blue-100 p-3 rounded-full">
                  <CalendarDays className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-gray-500">Total Attendees</p>
                  <h3 className="text-3xl font-bold mt-1">{stats.totalAttendees.toLocaleString()}</h3>
                  <p className="text-sm text-green-600 mt-1 flex items-center">
                    <ArrowUpRight className="h-4 w-4 mr-1" />
                    <span>+{stats.growth?.attendeesThisMonth || 0} this month</span>
                  </p>
                </div>
                <div className="bg-purple-100 p-3 rounded-full">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                  <h3 className="text-3xl font-bold mt-1">{formatCurrency(stats.totalRevenue)}</h3>
                  <p className={`text-sm ${stats.growth?.revenueGrowthPercentage >= 0 ? 'text-green-600' : 'text-red-600'} mt-1 flex items-center`}>
                    {stats.growth?.revenueGrowthPercentage >= 0 ? (
                      <ArrowUpRight className="h-4 w-4 mr-1" />
                    ) : (
                      <TrendingUp className="h-4 w-4 mr-1 transform rotate-90" />
                    )}
                    <span>{stats.growth?.revenueGrowthPercentage >= 0 ? '+' : ''}{stats.growth?.revenueGrowthPercentage || 0}% vs last month</span>
                  </p>
                </div>
                <div className="bg-green-100 p-3 rounded-full">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="w-full sm:w-auto grid grid-cols-3 sm:inline-flex">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="events">Events</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Quick Actions */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
                <CardContent className="p-6">
                  <div className="flex flex-col h-full justify-between">
                    <div>
                      <PlusCircle className="h-8 w-8 mb-4" />
                      <h3 className="text-xl font-bold mb-2">Create New Event</h3>
                      <p className="text-blue-100 mb-4">Start setting up your next amazing event</p>
                    </div>
                    <Button variant="secondary" className="mt-2 bg-white text-blue-600 hover:bg-blue-50" asChild>
                      <Link href="/dashboard/organizer/events/createevent" className="w-full">
                        Get Started
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex flex-col h-full justify-between">
                    <div>
                      <Ticket className="h-6 w-6 text-orange-500 mb-4" />
                      <h3 className="text-lg font-bold mb-2">Manage Tickets</h3>
                      <p className="text-gray-500 mb-4">View and manage your event tickets</p>
                    </div>
                    <Button variant="outline" className="mt-2" asChild>
                      <Link href="/dashboard/organizer/orders">
                        View Tickets
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex flex-col h-full justify-between">
                    <div>
                      <BarChart3 className="h-6 w-6 text-purple-500 mb-4" />
                      <h3 className="text-lg font-bold mb-2">View Analytics</h3>
                      <p className="text-gray-500 mb-4">Get insights about your events performance</p>
                    </div>
                    <Button variant="outline" className="mt-2" asChild>
                      <Link href="/dashboard/organizer/analytics">
                        View Analytics
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-orange-500 to-orange-600 text-white">
                <CardContent className="p-6">
                  <div className="flex flex-col h-full justify-between">
                    <div>
                      <Crown className="h-6 w-6 mb-4" />
                      <h3 className="text-lg font-bold mb-2">Upgrade Account</h3>
                      <p className="text-orange-100 mb-4">Unlock premium features for your events</p>
                    </div>
                    <Button variant="secondary" className="mt-2 bg-white text-orange-600 hover:bg-orange-50" asChild>
                      <Link href="/dashboard/organizer/subscription">
                        View Plans
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Events and Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card className="lg:col-span-2">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-center">
                    <CardTitle>Recent Events</CardTitle>
                    <Link href="/dashboard/organizer/events/myEvents" className="text-sm text-blue-600 hover:underline flex items-center">
                      View All
                      <ChevronRight className="h-4 w-4" />
                    </Link>
                  </div>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="divide-y">
                    {recentEvents.map((event) => (
                      <div key={event.id} className="flex flex-col sm:flex-row items-start sm:items-center p-4 hover:bg-gray-50 gap-4">
                        <div className="w-12 h-12 bg-gray-200 rounded-md flex-shrink-0 flex items-center justify-center overflow-hidden">
                          {event.imageUrl ? (
                            <div className="relative w-full h-full">
                              <Image src={event.imageUrl} alt={event.title} fill sizes="48px" style={{ objectFit: 'cover' }} />
                            </div>
                          ) : (
                            <Calendar className="h-6 w-6 text-gray-400" />
                          )}
                        </div>
                        <div className="sm:ml-4 flex-1 min-w-0 mt-2 sm:mt-0">
                          <p className="text-sm font-medium text-gray-900 truncate">{event.title}</p>
                          <p className="text-sm text-gray-500">{formatDate(event.date)}</p>
                        </div>
                        <div className="text-left sm:text-right mt-2 sm:mt-0">
                          <p className="text-sm font-medium text-gray-900">{formatCurrency(event.revenue)}</p>
                          <p className="text-sm text-gray-500">{event.attendees} attendees</p>
                        </div>
                        <Badge
                          className={`sm:ml-4 mt-2 sm:mt-0 ${
                            event.status === 'active'
                              ? 'bg-green-100 text-green-800 border-green-200'
                              : 'bg-gray-100 text-gray-800 border-gray-200'
                          }`}
                        >
                          {event.status === 'active' ? 'Active' : 'Completed'}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <div className="space-y-6">
                {/* Subscription Tier Widget */}
                <TierComparisonWidget />

                {/* Commission Display */}
                <CommissionDisplay />

                {/* Recent Activity */}
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Activity</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {recentActivity.map((activity) => (
                        <div key={activity.id} className="flex items-start">
                          <div className="mr-4">
                            <div className="flex h-9 w-9 items-center justify-center rounded-full bg-blue-50">
                              {activity.type === 'ticket_purchase' && <Ticket className="h-5 w-5 text-blue-500" />}
                              {activity.type === 'new_review' && <MessageSquare className="h-5 w-5 text-green-500" />}
                              {activity.type === 'event_update' && <CalendarDays className="h-5 w-5 text-orange-500" />}
                            </div>
                          </div>
                          <div>
                            <p className="text-sm">{activity.message}</p>
                            <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Upcoming Events */}
            <Card>
              <CardHeader>
                <CardTitle>Upcoming Events</CardTitle>
                <CardDescription>Monitor ticket sales for your upcoming events</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {upcomingEvents.map((event) => (
                    <div key={event.id} className="space-y-2">
                      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
                        <div>
                          <h4 className="font-medium">{event.title}</h4>
                          <p className="text-sm text-gray-500">{formatDate(event.date)}</p>
                        </div>
                        <Badge variant="outline" className="font-normal">
                          {event.ticketsSold} / {event.capacity} tickets sold
                        </Badge>
                      </div>
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs">
                          <span>{Math.round((event.ticketsSold / event.capacity) * 100)}% Sold</span>
                          <span>{event.capacity - event.ticketsSold} tickets left</span>
                        </div>
                        <Progress value={(event.ticketsSold / event.capacity) * 100} className="h-2" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/dashboard/organizer/events/myEvents">
                    View All Events
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Events Tab */}
          <TabsContent value="events" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Your Events</CardTitle>
                <CardDescription>Manage and monitor all your events</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <div className="space-x-2">
                      <Badge variant="outline">All</Badge>
                      <Badge variant="secondary">Active</Badge>
                      <Badge variant="outline">Draft</Badge>
                      <Badge variant="outline">Completed</Badge>
                      <Badge variant="outline">Cancelled</Badge>
                    </div>
                    <Button asChild>
                      <Link href="/dashboard/organizer/events/createevent">
                        <PlusCircle className="h-4 w-4 mr-2" />
                        Create Event
                      </Link>
                    </Button>
                  </div>

                  <Separator />

                  <div className="divide-y">
                    {[...recentEvents, ...upcomingEvents].map((event) => (
                      <div key={event.id} className="py-4 flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="w-12 h-12 bg-gray-200 rounded-md flex-shrink-0 flex items-center justify-center overflow-hidden">
                            {event.imageUrl ? (
                              <div className="relative w-full h-full">
                                <Image src={event.imageUrl} alt={event.title} fill sizes="48px" style={{ objectFit: 'cover' }} />
                              </div>
                            ) : (
                              <Calendar className="h-6 w-6 text-gray-400" />
                            )}
                          </div>
                          <div className="ml-4">
                            <p className="font-medium">{event.title}</p>
                            <p className="text-sm text-gray-500">{formatDate(event.date)}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge
                            className={
                              event.status === 'active'
                                ? 'bg-green-100 text-green-800 border-green-200'
                                : 'bg-gray-100 text-gray-800 border-gray-200'
                            }
                          >
                            {event.status === 'active' ? 'Active' : 'Completed'}
                          </Badge>
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/dashboard/organizer/events/details/${event.id}`}>
                              View
                            </Link>
                          </Button>
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/dashboard/organizer/events/edit/${event.id}`}>
                              Edit
                            </Link>
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/dashboard/organizer/events/myEvents">
                    View All Events
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Performance Overview</CardTitle>
                <CardDescription>Key metrics for your events</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                    <p className="text-3xl font-bold">{formatCurrency(stats.totalRevenue)}</p>
                    <p className={`text-sm ${stats.growth?.revenueGrowthPercentage >= 0 ? 'text-green-600' : 'text-red-600'} flex items-center`}>
                      {stats.growth?.revenueGrowthPercentage >= 0 ? (
                        <ArrowUpRight className="h-4 w-4 mr-1" />
                      ) : (
                        <TrendingUp className="h-4 w-4 mr-1 transform rotate-90" />
                      )}
                      <span>{stats.growth?.revenueGrowthPercentage >= 0 ? '+' : ''}{stats.growth?.revenueGrowthPercentage || 0}% vs last month</span>
                    </p>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-500">Tickets Sold</p>
                    <p className="text-3xl font-bold">{stats.ticketsSold}</p>
                    <p className="text-sm text-green-600 flex items-center">
                      <ArrowUpRight className="h-4 w-4 mr-1" />
                      <span>+8% vs last month</span>
                    </p>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-500">Average Rating</p>
                    <p className="text-3xl font-bold">{stats.averageRating}/5</p>
                    <div className="flex items-center text-yellow-500">
                      {Array(5).fill(0).map((_, i) => (
                        <svg key={i} className="w-4 h-4 fill-current" viewBox="0 0 24 24">
                          <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                        </svg>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="mt-8">
                  <h4 className="text-sm font-medium text-gray-500 mb-4">Revenue by Event</h4>
                  <div className="space-y-4">
                    {recentEvents.map((event) => (
                      <div key={event.id} className="flex items-center">
                        <div className="w-8 h-8 bg-gray-200 rounded-md flex-shrink-0 flex items-center justify-center overflow-hidden">
                          {event.imageUrl ? (
                            <div className="relative w-full h-full">
                              <Image src={event.imageUrl} alt={event.title} fill sizes="32px" style={{ objectFit: 'cover' }} />
                            </div>
                          ) : (
                            <Calendar className="h-4 w-4 text-gray-400" />
                          )}
                        </div>
                        <div className="ml-3 flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">{event.title}</p>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium">{formatCurrency(event.revenue)}</p>
                        </div>
                        <div className="ml-6 w-1/3">
                          <Progress value={(event.revenue / stats.totalRevenue) * 100} className="h-2" />
                        </div>
                        <div className="ml-3 w-12 text-right">
                          <p className="text-sm text-gray-500">{Math.round((event.revenue / stats.totalRevenue) * 100)}%</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/dashboard/organizer/analytics">
                    View Detailed Analytics
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </RoleGate>
  );
}
