/**
 * Advanced Attendee Matching Algorithm
 * Analyzes attendee profiles to suggest relevant connections based on compatibility scores
 */

import { db } from '@/lib/prisma';
import { AttendeeProfile } from '@/types/elite-communication';
import { EliteTier } from '@prisma/client';

export interface MatchingCriteria {
  industryWeight: number;
  interestsWeight: number;
  roleWeight: number;
  companyWeight: number;
  networkingGoalsWeight: number;
  geographicWeight: number;
  tierWeight: number;
}

export interface AttendeeMatch {
  attendee: AttendeeProfile;
  compatibilityScore: number;
  matchReasons: string[];
  commonInterests: string[];
  industryMatch: boolean;
  roleCompatibility: string;
  networkingAlignment: string;
  suggestedIcebreakers: string[];
}

export interface NetworkingSuggestion {
  type: 'direct_message' | 'meeting_request' | 'group_introduction' | 'skill_exchange';
  title: string;
  description: string;
  actionText: string;
  priority: 'high' | 'medium' | 'low';
}

// Default matching criteria weights
const DEFAULT_CRITERIA: MatchingCriteria = {
  industryWeight: 0.25,
  interestsWeight: 0.30,
  roleWeight: 0.20,
  companyWeight: 0.10,
  networkingGoalsWeight: 0.10,
  geographicWeight: 0.03,
  tierWeight: 0.02
};

// Industry compatibility matrix
const INDUSTRY_COMPATIBILITY: Record<string, string[]> = {
  'Technology': ['Software', 'AI/ML', 'Cybersecurity', 'Fintech', 'Healthcare Tech', 'EdTech'],
  'Finance': ['Banking', 'Investment', 'Insurance', 'Fintech', 'Real Estate', 'Accounting'],
  'Healthcare': ['Medical', 'Pharmaceuticals', 'Biotech', 'Healthcare Tech', 'Mental Health'],
  'Education': ['EdTech', 'Training', 'Academic Research', 'Online Learning', 'Corporate Training'],
  'Marketing': ['Digital Marketing', 'Advertising', 'PR', 'Content Creation', 'Social Media'],
  'Consulting': ['Management Consulting', 'Strategy', 'Operations', 'HR Consulting', 'IT Consulting'],
  'Retail': ['E-commerce', 'Fashion', 'Consumer Goods', 'Supply Chain', 'Customer Experience'],
  'Manufacturing': ['Industrial', 'Automotive', 'Aerospace', 'Electronics', 'Supply Chain'],
  'Media': ['Journalism', 'Broadcasting', 'Content Creation', 'Entertainment', 'Publishing'],
  'Non-profit': ['Social Impact', 'Charity', 'Community Development', 'Environmental', 'Education']
};

// Role compatibility for networking
const ROLE_COMPATIBILITY: Record<string, string[]> = {
  'CEO': ['CTO', 'CFO', 'VP', 'Director', 'Founder', 'Investor'],
  'CTO': ['CEO', 'VP Engineering', 'Tech Lead', 'Product Manager', 'Developer'],
  'VP': ['CEO', 'Director', 'Manager', 'C-Level'],
  'Director': ['VP', 'Manager', 'Senior Manager', 'Team Lead'],
  'Manager': ['Director', 'Senior Manager', 'Team Lead', 'Specialist'],
  'Developer': ['CTO', 'Tech Lead', 'Product Manager', 'Designer', 'QA'],
  'Designer': ['Product Manager', 'Developer', 'Marketing', 'UX Researcher'],
  'Sales': ['Marketing', 'Business Development', 'Account Manager', 'Customer Success'],
  'Marketing': ['Sales', 'Product Manager', 'Designer', 'Content Creator'],
  'Consultant': ['Manager', 'Director', 'Analyst', 'Specialist'],
  'Founder': ['CEO', 'Investor', 'Advisor', 'Entrepreneur'],
  'Investor': ['Founder', 'CEO', 'Entrepreneur', 'Advisor']
};

export class AttendeeMatchingService {
  private criteria: MatchingCriteria;

  constructor(criteria: MatchingCriteria = DEFAULT_CRITERIA) {
    this.criteria = criteria;
  }

  /**
   * Find compatible attendees for a given user
   */
  async findMatches(
    userId: string, 
    eventId: string, 
    limit: number = 10
  ): Promise<AttendeeMatch[]> {
    // Get the user's profile
    const userProfile = await db.attendeeProfile.findFirst({
      where: { userId, eventId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
            email: true
          }
        }
      }
    });

    if (!userProfile) {
      throw new Error('User profile not found');
    }

    // Get all other discoverable attendees
    const otherAttendees = await db.attendeeProfile.findMany({
      where: {
        eventId,
        userId: { not: userId },
        isDiscoverable: true,
        privacyLevel: { not: 'HIDDEN' }
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
            email: true
          }
        }
      }
    });

    // Calculate compatibility scores
    const matches: AttendeeMatch[] = [];

    for (const attendee of otherAttendees) {
      const compatibility = this.calculateCompatibility(userProfile, attendee);
      
      if (compatibility.compatibilityScore > 0.3) { // Minimum threshold
        matches.push(compatibility);
      }
    }

    // Sort by compatibility score and return top matches
    return matches
      .sort((a, b) => b.compatibilityScore - a.compatibilityScore)
      .slice(0, limit);
  }

  /**
   * Calculate compatibility score between two attendees
   */
  private calculateCompatibility(
    userProfile: AttendeeProfile & { user?: any }, 
    targetProfile: AttendeeProfile & { user?: any }
  ): AttendeeMatch {
    let totalScore = 0;
    const matchReasons: string[] = [];
    const commonInterests: string[] = [];
    let industryMatch = false;
    let roleCompatibility = '';
    let networkingAlignment = '';

    // 1. Industry compatibility
    const industryScore = this.calculateIndustryScore(userProfile, targetProfile);
    totalScore += industryScore * this.criteria.industryWeight;
    
    if (industryScore > 0.7) {
      industryMatch = true;
      matchReasons.push(`Both work in ${userProfile.industry || 'related'} industry`);
    }

    // 2. Interests overlap
    const interestsScore = this.calculateInterestsScore(userProfile, targetProfile);
    totalScore += interestsScore * this.criteria.interestsWeight;
    
    commonInterests.push(...this.findCommonInterests(userProfile, targetProfile));
    if (commonInterests.length > 0) {
      matchReasons.push(`Share ${commonInterests.length} common interest${commonInterests.length > 1 ? 's' : ''}`);
    }

    // 3. Role compatibility
    const roleScore = this.calculateRoleScore(userProfile, targetProfile);
    totalScore += roleScore * this.criteria.roleWeight;
    
    roleCompatibility = this.getRoleCompatibilityReason(userProfile, targetProfile);
    if (roleScore > 0.5) {
      matchReasons.push(roleCompatibility);
    }

    // 4. Company diversity (higher score for different companies)
    const companyScore = this.calculateCompanyScore(userProfile, targetProfile);
    totalScore += companyScore * this.criteria.companyWeight;

    // 5. Networking goals alignment
    const networkingScore = this.calculateNetworkingScore(userProfile, targetProfile);
    totalScore += networkingScore * this.criteria.networkingGoalsWeight;
    
    networkingAlignment = this.getNetworkingAlignment(userProfile, targetProfile);
    if (networkingScore > 0.6) {
      matchReasons.push(networkingAlignment);
    }

    // 6. Geographic proximity (if timezone data available)
    const geoScore = this.calculateGeographicScore(userProfile, targetProfile);
    totalScore += geoScore * this.criteria.geographicWeight;

    // 7. Tier compatibility
    const tierScore = this.calculateTierScore(userProfile, targetProfile);
    totalScore += tierScore * this.criteria.tierWeight;

    // Generate icebreakers
    const suggestedIcebreakers = this.generateIcebreakers(userProfile, targetProfile, {
      commonInterests,
      industryMatch,
      roleCompatibility
    });

    return {
      attendee: targetProfile,
      compatibilityScore: Math.min(totalScore, 1.0), // Cap at 1.0
      matchReasons,
      commonInterests,
      industryMatch,
      roleCompatibility,
      networkingAlignment,
      suggestedIcebreakers
    };
  }

  private calculateIndustryScore(user: AttendeeProfile, target: AttendeeProfile): number {
    if (!user.industry || !target.industry) return 0;
    
    // Exact match
    if (user.industry === target.industry) return 1.0;
    
    // Compatible industries
    const userCompatible = INDUSTRY_COMPATIBILITY[user.industry] || [];
    const targetCompatible = INDUSTRY_COMPATIBILITY[target.industry] || [];
    
    if (userCompatible.includes(target.industry) || targetCompatible.includes(user.industry)) {
      return 0.7;
    }
    
    return 0.1; // Different but still some value for diversity
  }

  private calculateInterestsScore(user: AttendeeProfile, target: AttendeeProfile): number {
    const userInterests = user.interests || [];
    const targetInterests = target.interests || [];
    
    if (userInterests.length === 0 || targetInterests.length === 0) return 0;
    
    const commonCount = userInterests.filter(interest => 
      targetInterests.includes(interest)
    ).length;
    
    const totalUnique = new Set([...userInterests, ...targetInterests]).size;
    
    return commonCount / Math.max(userInterests.length, targetInterests.length);
  }

  private findCommonInterests(user: AttendeeProfile, target: AttendeeProfile): string[] {
    const userInterests = user.interests || [];
    const targetInterests = target.interests || [];
    
    return userInterests.filter(interest => targetInterests.includes(interest));
  }

  private calculateRoleScore(user: AttendeeProfile, target: AttendeeProfile): number {
    if (!user.role || !target.role) return 0;
    
    // Same role (might be good for peer learning)
    if (user.role === target.role) return 0.6;
    
    // Compatible roles
    const userCompatible = ROLE_COMPATIBILITY[user.role] || [];
    if (userCompatible.includes(target.role)) return 0.8;
    
    const targetCompatible = ROLE_COMPATIBILITY[target.role] || [];
    if (targetCompatible.includes(user.role)) return 0.8;
    
    return 0.2; // Different roles can still be valuable
  }

  private getRoleCompatibilityReason(user: AttendeeProfile, target: AttendeeProfile): string {
    if (!user.role || !target.role) return '';
    
    if (user.role === target.role) {
      return `Both are ${user.role}s - great for peer insights`;
    }
    
    const userCompatible = ROLE_COMPATIBILITY[user.role] || [];
    if (userCompatible.includes(target.role)) {
      return `${user.role} and ${target.role} often collaborate`;
    }
    
    return `Cross-functional networking opportunity`;
  }

  private calculateCompanyScore(user: AttendeeProfile, target: AttendeeProfile): number {
    if (!user.company || !target.company) return 0.5;
    
    // Different companies are better for networking
    return user.company === target.company ? 0.3 : 0.8;
  }

  private calculateNetworkingScore(user: AttendeeProfile, target: AttendeeProfile): number {
    if (!user.networkingGoals || !target.networkingGoals) return 0.5;
    
    const userGoals = user.networkingGoals.toLowerCase();
    const targetGoals = target.networkingGoals.toLowerCase();
    
    // Look for complementary goals
    const keywords = ['mentor', 'learn', 'collaborate', 'partner', 'invest', 'hire', 'job'];
    let score = 0;
    
    for (const keyword of keywords) {
      if (userGoals.includes(keyword) && targetGoals.includes(keyword)) {
        score += 0.3;
      }
    }
    
    return Math.min(score, 1.0);
  }

  private getNetworkingAlignment(user: AttendeeProfile, target: AttendeeProfile): string {
    if (!user.networkingGoals || !target.networkingGoals) return '';
    
    const userGoals = user.networkingGoals.toLowerCase();
    const targetGoals = target.networkingGoals.toLowerCase();
    
    if (userGoals.includes('mentor') && targetGoals.includes('learn')) {
      return 'Potential mentoring opportunity';
    }
    
    if (userGoals.includes('collaborate') && targetGoals.includes('partner')) {
      return 'Both seeking collaboration';
    }
    
    if (userGoals.includes('invest') && targetGoals.includes('funding')) {
      return 'Investor-entrepreneur match';
    }
    
    return 'Aligned networking objectives';
  }

  private calculateGeographicScore(user: AttendeeProfile, target: AttendeeProfile): number {
    if (!user.timezone || !target.timezone) return 0.5;
    
    // Same timezone gets higher score for easier meeting scheduling
    return user.timezone === target.timezone ? 1.0 : 0.3;
  }

  private calculateTierScore(user: any, target: any): number {
    // This would need to be enhanced to get actual tier information
    // For now, assume similar tiers are better for networking
    return 0.5;
  }

  private generateIcebreakers(
    user: AttendeeProfile, 
    target: AttendeeProfile, 
    context: any
  ): string[] {
    const icebreakers: string[] = [];
    
    if (context.commonInterests.length > 0) {
      icebreakers.push(`I noticed we both share an interest in ${context.commonInterests[0]}. What got you started in that area?`);
    }
    
    if (context.industryMatch) {
      icebreakers.push(`Fellow ${user.industry} professional! What trends are you seeing in our industry?`);
    }
    
    if (user.company && target.company && user.company !== target.company) {
      icebreakers.push(`I'd love to learn more about how ${target.company} approaches [relevant topic].`);
    }
    
    if (user.role && target.role) {
      icebreakers.push(`As a ${user.role}, I'm curious about your perspective as a ${target.role}.`);
    }
    
    // Default icebreakers
    icebreakers.push(
      "What brings you to this event?",
      "What's the most interesting project you're working on right now?",
      "Any key takeaways from the event so far?"
    );
    
    return icebreakers.slice(0, 3); // Return top 3
  }

  /**
   * Generate networking suggestions based on matches
   */
  generateNetworkingSuggestions(matches: AttendeeMatch[]): NetworkingSuggestion[] {
    const suggestions: NetworkingSuggestion[] = [];
    
    // High compatibility matches
    const highMatches = matches.filter(m => m.compatibilityScore > 0.8);
    if (highMatches.length > 0) {
      suggestions.push({
        type: 'direct_message',
        title: 'Reach out to your top matches',
        description: `You have ${highMatches.length} highly compatible attendee${highMatches.length > 1 ? 's' : ''} to connect with.`,
        actionText: 'Send message',
        priority: 'high'
      });
    }
    
    // Industry matches
    const industryMatches = matches.filter(m => m.industryMatch);
    if (industryMatches.length > 2) {
      suggestions.push({
        type: 'group_introduction',
        title: 'Join industry peers',
        description: `Connect with ${industryMatches.length} professionals in your industry.`,
        actionText: 'Create group chat',
        priority: 'medium'
      });
    }
    
    // Skill exchange opportunities
    const skillMatches = matches.filter(m => 
      m.roleCompatibility.includes('collaborate') || 
      m.networkingAlignment.includes('learn')
    );
    if (skillMatches.length > 0) {
      suggestions.push({
        type: 'skill_exchange',
        title: 'Skill exchange opportunities',
        description: 'Several attendees are looking for knowledge sharing.',
        actionText: 'Propose exchange',
        priority: 'medium'
      });
    }
    
    return suggestions;
  }
}
