# Event Management API Documentation

This document provides information about the Event Management API endpoints available for external applications.

## Base URL

All API endpoints are relative to your application's base URL.

## Authentication

Some API endpoints require authentication. For public endpoints, no authentication is required.

## Public API Endpoints

### Test API

Simple endpoint to test if the API is working.

**URL**: `/api/test`

**Method**: `GET`

**Response**:
```json
{
  "message": "Test API is working"
}
```

**Method**: `POST`

**Request Body**:
```json
{
  "test": "data"
}
```

**Response**:
```json
{
  "message": "Test API POST is working",
  "receivedData": {
    "test": "data"
  }
}
```

### Get Published Events

Retrieve a list of published events.

**URL**: `/api/events/published`

**Method**: `GET`

**Query Parameters**:
- `category` (optional): Filter events by category
- `type` (optional): Filter events by event type
- `page` (optional): Page number for pagination (default: 1)
- `limit` (optional): Number of events per page (default: 10)

**Response**:
```json
{
  "events": [
    {
      "id": "event-id",
      "title": "Event Title",
      "description": "Event Description",
      "startDate": "2023-12-31T18:00:00.000Z",
      "endDate": "2024-01-01T02:00:00.000Z",
      "location": "Event Location",
      "venue": "Event Venue",
      "category": "Event Category",
      "eventType": "Event Type",
      "status": "Published",
      "user": {
        "name": "Organizer Name",
        "image": "Organizer Image URL"
      }
    }
  ],
  "totalEvents": 100,
  "totalPages": 10,
  "currentPage": 1
}
```

### Get Event Details

Retrieve details for a specific event.

**URL**: `/api/eventdetails/:id`

**Method**: `GET`

**URL Parameters**:
- `id`: The ID of the event to retrieve

**Response**:
```json
{
  "id": "event-id",
  "title": "Event Title",
  "description": "Event Description",
  "startDate": "2023-12-31T18:00:00.000Z",
  "endDate": "2024-01-01T02:00:00.000Z",
  "location": "Event Location",
  "venue": "Event Venue",
  "category": "Event Category",
  "eventType": "Event Type",
  "status": "Published",
  "user": {
    "name": "Organizer Name",
    "image": "Organizer Image URL"
  },
  "ageRestriction": {
    "minimumAge": 18,
    "maximumAge": null
  },
  "ParkingManagement": {
    "parkingAvailable": true,
    "parkingDetails": "Parking details"
  }
}
```

## Authentication

There are two ways to authenticate with the API:

1. **Session-based Authentication**: Used for web applications where the user is logged in.
2. **API Key Authentication**: Used for external applications or services.

### API Key Authentication

To use API key authentication, include the API key in the `X-API-Key` header:

```
X-API-Key: your-api-key
```

You can generate API keys in your account dashboard or by using the API key management endpoints.

### Permissions

API keys have granular permissions that control what actions they can perform. When creating an API key, you can specify which permissions to grant. The available permissions are:

- `read:events`: View events
- `write:events`: Create and update events
- `delete:events`: Delete events
- `read:tickets`: View tickets
- `write:tickets`: Create and update tickets
- `delete:tickets`: Delete tickets
- `read:orders`: View orders
- `write:orders`: Create and update orders
- `delete:orders`: Delete orders
- `read:users`: View user information
- `write:users`: Update user information
- `read:analytics`: View analytics data

### Permission Errors

If you attempt to access an endpoint without the required permissions, you will receive a `403 Forbidden` response with the following body:

```json
{
  "error": "Insufficient permissions for this endpoint"
}
```

## Protected API Endpoints

These endpoints require authentication. You need to include either a valid session cookie or an API key.

### Create Event (Protected)

Create a new event.

**URL**: `/api/events`

**Method**: `POST`

**Headers**:
- `Authorization`: Bearer {token}

**Request Body**:
```json
{
  "title": "Event Title",
  "description": "Event Description",
  "startDate": "2023-12-31T18:00:00.000Z",
  "endDate": "2024-01-01T02:00:00.000Z",
  "location": "Event Location",
  "venue": "Event Venue",
  "category": "Event Category",
  "eventType": "Event Type"
}
```

**Response**:
```json
{
  "id": "event-id",
  "title": "Event Title",
  "description": "Event Description",
  "startDate": "2023-12-31T18:00:00.000Z",
  "endDate": "2024-01-01T02:00:00.000Z",
  "location": "Event Location",
  "venue": "Event Venue",
  "category": "Event Category",
  "eventType": "Event Type",
  "status": "Draft",
  "userId": "user-id",
  "createdAt": "2023-12-01T12:00:00.000Z",
  "updatedAt": "2023-12-01T12:00:00.000Z"
}
```

## Error Responses

All endpoints may return the following error responses:

- `400 Bad Request`: The request was invalid or cannot be served
- `401 Unauthorized`: Authentication is required or failed
- `403 Forbidden`: The authenticated user does not have permission to access the requested resource
- `404 Not Found`: The requested resource could not be found
- `500 Internal Server Error`: An error occurred on the server

## Cross-Origin Resource Sharing (CORS)

This API supports Cross-Origin Resource Sharing (CORS) for all origins. The following headers are included in all responses:

- `Access-Control-Allow-Origin: *`
- `Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS`
- `Access-Control-Allow-Headers: Content-Type, Authorization`

## Rate Limiting

All API endpoints are subject to rate limiting to ensure fair usage and system stability. Rate limits are applied on a per-API key basis.

### Rate Limit Headers

The following headers are included in API responses to help you track your rate limit usage:

- `X-RateLimit-Limit`: The maximum number of requests allowed per minute
- `X-RateLimit-Remaining`: The number of requests remaining in the current rate limit window
- `X-RateLimit-Reset`: The time at which the current rate limit window resets (in Unix time)

### Rate Limit Configuration

When creating an API key, you can configure the rate limit for that key. The default rate limit is 100 requests per minute.

### Rate Limit Exceeded

If you exceed your rate limit, you will receive a `429 Too Many Requests` response with the following body:

```json
{
  "error": "Rate limit exceeded. Try again later."
}
```

### Best Practices

- Monitor your rate limit usage using the rate limit headers
- Implement exponential backoff when you receive a 429 response
- Consider using multiple API keys for different applications or services

## API Key Management

The following endpoints are available for managing API keys:

### Get API Keys

Retrieve all API keys for the authenticated user.

**URL**: `/api/api-keys`

**Method**: `GET`

**Authentication**: Required (Session)

**Response**:
```json
[
  {
    "id": "key-id",
    "name": "My API Key",
    "key": "api-key-value",
    "permissions": ["read:events", "write:events"],
    "lastUsed": "2023-12-01T12:00:00.000Z",
    "expiresAt": "2024-12-01T12:00:00.000Z",
    "createdAt": "2023-12-01T12:00:00.000Z"
  }
]
```

### Create API Key

Create a new API key.

**URL**: `/api/api-keys`

**Method**: `POST`

**Authentication**: Required (Session)

**Request Body**:
```json
{
  "name": "My API Key",
  "permissions": ["read:events", "write:events"],
  "expiresAt": "2024-12-01T12:00:00.000Z"
}
```

**Response**:
```json
{
  "id": "key-id",
  "name": "My API Key",
  "key": "api-key-value",
  "permissions": ["read:events", "write:events"],
  "expiresAt": "2024-12-01T12:00:00.000Z",
  "createdAt": "2023-12-01T12:00:00.000Z"
}
```

### Delete API Key

Delete an API key.

**URL**: `/api/api-keys?id=key-id`

**Method**: `DELETE`

**Authentication**: Required (Session)

**Response**:
```json
{
  "success": true
}
```

## Example Usage

### JavaScript Fetch Example

```javascript
// Example: Get published events
fetch('https://your-domain.com/api/events/published')
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));

// Example: Get event details
fetch('https://your-domain.com/api/eventdetails/event-id')
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));
```

### JavaScript Fetch with API Key Authentication Example

```javascript
// Example: Get events with API key authentication
fetch('https://your-domain.com/api/external/events', {
  headers: {
    'X-API-Key': 'your-api-key'
  }
})
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));

// Example: Create a new event (requires authentication)
fetch('https://your-domain.com/api/events', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'your-api-key'
  },
  body: JSON.stringify({
    title: 'My Event',
    description: 'Event Description',
    startDate: '2023-12-31T18:00:00.000Z',
    endDate: '2024-01-01T02:00:00.000Z',
    location: 'Event Location',
    venue: 'Event Venue',
    category: 'Event Category',
    eventType: 'Event Type'
  })
})
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));
```
