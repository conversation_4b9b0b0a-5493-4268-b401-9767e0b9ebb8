'use client';

import { useState, useEffect } from 'react';
import {
  Save,
  RefreshCw,
  AlertCircle,
  Info,
  Globe,
  Shield,
  Wifi,
  Zap,
  HelpCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { toast } from '@/components/ui/use-toast';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export default function AdminNFCSettingsPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // General settings
  const [systemName, setSystemName] = useState('Global NFC Payment System');
  const [defaultCurrencySymbol, setDefaultCurrencySymbol] = useState('$');
  const [defaultLanguage, setDefaultLanguage] = useState('en');
  const [systemEnabled, setSystemEnabled] = useState(true);

  // Security settings
  const [maxTransactionAmount, setMaxTransactionAmount] = useState(1000);
  const [requirePinForHighValue, setRequirePinForHighValue] = useState(true);
  const [highValueThreshold, setHighValueThreshold] = useState(200);
  const [cardLockoutThreshold, setCardLockoutThreshold] = useState(5);
  const [enforceCardLimits, setEnforceCardLimits] = useState(true);
  const [securityLevel, setSecurityLevel] = useState('standard');

  // Offline mode settings
  const [allowOfflineMode, setAllowOfflineMode] = useState(true);
  const [maxOfflineTransactions, setMaxOfflineTransactions] = useState(100);
  const [offlineTransactionLimit, setOfflineTransactionLimit] = useState(50);
  const [syncInterval, setSyncInterval] = useState(15);
  const [requireSyncAfter, setRequireSyncAfter] = useState(24);

  // Performance settings
  const [cacheTimeout, setCacheTimeout] = useState(30);
  const [batchSize, setBatchSize] = useState(100);
  const [logLevel, setLogLevel] = useState('info');
  const [analyticsEnabled, setAnalyticsEnabled] = useState(true);
  const [receiptEnabled, setReceiptEnabled] = useState(true);

  // Load settings
  useEffect(() => {
    const fetchSettings = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Call the API to get NFC settings
        const response = await fetch('/api/admin/nfc/settings');

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json();

        // Update state with fetched settings
        setSystemName(data.systemName);
        setDefaultCurrencySymbol(data.defaultCurrencySymbol);
        setDefaultLanguage(data.defaultLanguage);
        setSystemEnabled(data.systemEnabled);

        // Security settings
        setMaxTransactionAmount(data.maxTransactionAmount);
        setRequirePinForHighValue(data.requirePinForHighValue);
        setHighValueThreshold(data.highValueThreshold || 200);
        setCardLockoutThreshold(data.cardLockoutThreshold);
        setEnforceCardLimits(data.enforceCardLimits);
        setSecurityLevel(data.securityLevel);

        // Offline mode settings
        setAllowOfflineMode(data.allowOfflineMode);
        setMaxOfflineTransactions(data.maxOfflineTransactions || 100);
        setOfflineTransactionLimit(data.offlineTransactionLimit || 50);
        setSyncInterval(data.syncInterval || 15);
        setRequireSyncAfter(data.requireSyncAfter || 24);

        // Performance settings
        setCacheTimeout(data.cacheTimeout);
        setBatchSize(data.batchSize);
        setLogLevel(data.logLevel);
        setAnalyticsEnabled(data.analyticsEnabled);
        setReceiptEnabled(data.receiptEnabled);
      } catch (err) {
        console.error('Error fetching NFC settings:', err);
        setError('Failed to load NFC system settings. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  // Save settings
  const saveSettings = async () => {
    setIsSaving(true);
    setError(null);

    try {
      // Prepare settings object
      const settings = {
        // General
        systemName,
        defaultCurrencySymbol,
        defaultLanguage,
        systemEnabled,

        // Security
        maxTransactionAmount,
        requirePinForHighValue,
        highValueThreshold,
        cardLockoutThreshold,
        enforceCardLimits,
        securityLevel,

        // Offline
        allowOfflineMode,
        maxOfflineTransactions,
        offlineTransactionLimit,
        syncInterval,
        requireSyncAfter,

        // Performance
        cacheTimeout,
        batchSize,
        logLevel,
        analyticsEnabled,
        receiptEnabled
      };

      // Call the API to save settings
      const response = await fetch('/api/admin/nfc/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save settings');
      }

      const data = await response.json();

      toast({
        title: 'Settings Saved',
        description: 'Global NFC system settings have been updated successfully.',
      });
    } catch (err) {
      console.error('Error saving NFC settings:', err);
      setError('Failed to save NFC system settings. Please try again.');

      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to save settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Global NFC Settings</h1>
          <p className="text-gray-600 mt-1">
            Configure system-wide settings for the NFC payment system
          </p>
        </div>
        <Button onClick={saveSettings} disabled={isLoading || isSaving}>
          {isSaving ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Settings
            </>
          )}
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="general" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="offline">Offline Mode</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        {/* General Settings Tab */}
        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Globe className="mr-2 h-5 w-5 text-blue-500" />
                General Settings
              </CardTitle>
              <CardDescription>
                Configure basic system-wide settings for the NFC payment system
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="systemName">System Name</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-gray-400" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-80">The name of the NFC payment system that will be displayed to users.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="systemName"
                  value={systemName}
                  onChange={(e) => setSystemName(e.target.value)}
                  placeholder="Global NFC Payment System"
                  disabled={isLoading}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="defaultCurrencySymbol">Default Currency Symbol</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-gray-400" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-80">The default currency symbol used throughout the system. Organizers can override this for their events.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="defaultCurrencySymbol"
                  value={defaultCurrencySymbol}
                  onChange={(e) => setDefaultCurrencySymbol(e.target.value)}
                  placeholder="$"
                  disabled={isLoading}
                  className="w-20"
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="defaultLanguage">Default Language</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-gray-400" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-80">The default language for the NFC system. Users can override this in their settings.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Select
                  value={defaultLanguage}
                  onValueChange={setDefaultLanguage}
                  disabled={isLoading}
                >
                  <SelectTrigger id="defaultLanguage" className="w-full">
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="es">Spanish</SelectItem>
                    <SelectItem value="fr">French</SelectItem>
                    <SelectItem value="de">German</SelectItem>
                    <SelectItem value="zh">Chinese</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-between pt-2">
                <div className="space-y-0.5">
                  <Label htmlFor="systemEnabled">System Enabled</Label>
                  <p className="text-sm text-gray-500">
                    Enable or disable the entire NFC payment system
                  </p>
                </div>
                <Switch
                  id="systemEnabled"
                  checked={systemEnabled}
                  onCheckedChange={setSystemEnabled}
                  disabled={isLoading}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Settings Tab */}
        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="mr-2 h-5 w-5 text-red-500" />
                Security Settings
              </CardTitle>
              <CardDescription>
                Configure security settings for the NFC payment system
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="maxTransactionAmount">Maximum Transaction Amount</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-gray-400" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-80">The maximum amount allowed for a single NFC transaction.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-gray-500">{defaultCurrencySymbol}</span>
                  <Input
                    id="maxTransactionAmount"
                    type="number"
                    value={maxTransactionAmount}
                    onChange={(e) => setMaxTransactionAmount(Number(e.target.value))}
                    placeholder="1000"
                    disabled={isLoading}
                  />
                </div>
              </div>

              <div className="flex items-center justify-between pt-2">
                <div className="space-y-0.5">
                  <Label htmlFor="requirePinForHighValue">Require PIN for High-Value Transactions</Label>
                  <p className="text-sm text-gray-500">
                    Require a PIN for transactions above the high-value threshold
                  </p>
                </div>
                <Switch
                  id="requirePinForHighValue"
                  checked={requirePinForHighValue}
                  onCheckedChange={setRequirePinForHighValue}
                  disabled={isLoading}
                />
              </div>

              {requirePinForHighValue && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="highValueThreshold">High-Value Threshold</Label>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <HelpCircle className="h-4 w-4 text-gray-400" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="w-80">Transactions above this amount will require a PIN.</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-500">{defaultCurrencySymbol}</span>
                    <Input
                      id="highValueThreshold"
                      type="number"
                      value={highValueThreshold}
                      onChange={(e) => setHighValueThreshold(Number(e.target.value))}
                      placeholder="200"
                      disabled={isLoading}
                    />
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="cardLockoutThreshold">Card Lockout Threshold</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-gray-400" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-80">Number of failed attempts before a card is temporarily locked.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="cardLockoutThreshold"
                  type="number"
                  value={cardLockoutThreshold}
                  onChange={(e) => setCardLockoutThreshold(Number(e.target.value))}
                  placeholder="5"
                  disabled={isLoading}
                />
              </div>

              <div className="flex items-center justify-between pt-2">
                <div className="space-y-0.5">
                  <Label htmlFor="enforceCardLimits">Enforce Card Spending Limits</Label>
                  <p className="text-sm text-gray-500">
                    Enforce daily and per-transaction spending limits on cards
                  </p>
                </div>
                <Switch
                  id="enforceCardLimits"
                  checked={enforceCardLimits}
                  onCheckedChange={setEnforceCardLimits}
                  disabled={isLoading}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="securityLevel">Security Level</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-gray-400" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-80">The overall security level for the NFC system.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Select
                  value={securityLevel}
                  onValueChange={setSecurityLevel}
                  disabled={isLoading}
                >
                  <SelectTrigger id="securityLevel" className="w-full">
                    <SelectValue placeholder="Select security level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="basic">Basic</SelectItem>
                    <SelectItem value="standard">Standard</SelectItem>
                    <SelectItem value="enhanced">Enhanced</SelectItem>
                    <SelectItem value="maximum">Maximum</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Offline Mode Tab */}
        <TabsContent value="offline">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Wifi className="mr-2 h-5 w-5 text-purple-500" />
                Offline Mode Settings
              </CardTitle>
              <CardDescription>
                Configure settings for offline operation of the NFC payment system
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between pt-2">
                <div className="space-y-0.5">
                  <Label htmlFor="allowOfflineMode">Allow Offline Mode</Label>
                  <p className="text-sm text-gray-500">
                    Allow vendors to process transactions when offline
                  </p>
                </div>
                <Switch
                  id="allowOfflineMode"
                  checked={allowOfflineMode}
                  onCheckedChange={setAllowOfflineMode}
                  disabled={isLoading}
                />
              </div>

              {allowOfflineMode && (
                <>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="maxOfflineTransactions">Maximum Offline Transactions</Label>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            <HelpCircle className="h-4 w-4 text-gray-400" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="w-80">Maximum number of transactions that can be processed offline before requiring synchronization.</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Input
                      id="maxOfflineTransactions"
                      type="number"
                      value={maxOfflineTransactions}
                      onChange={(e) => setMaxOfflineTransactions(Number(e.target.value))}
                      placeholder="100"
                      disabled={isLoading}
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="offlineTransactionLimit">Offline Transaction Limit</Label>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            <HelpCircle className="h-4 w-4 text-gray-400" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="w-80">Maximum amount allowed for a single offline transaction.</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-500">{defaultCurrencySymbol}</span>
                      <Input
                        id="offlineTransactionLimit"
                        type="number"
                        value={offlineTransactionLimit}
                        onChange={(e) => setOfflineTransactionLimit(Number(e.target.value))}
                        placeholder="50"
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="syncInterval">Sync Interval (minutes)</Label>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            <HelpCircle className="h-4 w-4 text-gray-400" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="w-80">How often the system should attempt to synchronize offline transactions when connectivity is restored.</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Input
                      id="syncInterval"
                      type="number"
                      value={syncInterval}
                      onChange={(e) => setSyncInterval(Number(e.target.value))}
                      placeholder="15"
                      disabled={isLoading}
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="requireSyncAfter">Require Sync After (hours)</Label>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            <HelpCircle className="h-4 w-4 text-gray-400" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="w-80">Maximum time allowed in offline mode before synchronization is required.</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Input
                      id="requireSyncAfter"
                      type="number"
                      value={requireSyncAfter}
                      onChange={(e) => setRequireSyncAfter(Number(e.target.value))}
                      placeholder="24"
                      disabled={isLoading}
                    />
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Zap className="mr-2 h-5 w-5 text-amber-500" />
                Performance Settings
              </CardTitle>
              <CardDescription>
                Configure performance and analytics settings for the NFC payment system
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="cacheTimeout">Cache Timeout (minutes)</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-gray-400" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-80">How long to cache data before refreshing from the database.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="cacheTimeout"
                  type="number"
                  value={cacheTimeout}
                  onChange={(e) => setCacheTimeout(Number(e.target.value))}
                  placeholder="30"
                  disabled={isLoading}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="batchSize">Batch Processing Size</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-gray-400" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-80">Number of transactions to process in a single batch.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="batchSize"
                  type="number"
                  value={batchSize}
                  onChange={(e) => setBatchSize(Number(e.target.value))}
                  placeholder="100"
                  disabled={isLoading}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="logLevel">Log Level</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-gray-400" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-80">The level of detail for system logs.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Select
                  value={logLevel}
                  onValueChange={setLogLevel}
                  disabled={isLoading}
                >
                  <SelectTrigger id="logLevel" className="w-full">
                    <SelectValue placeholder="Select log level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="error">Error</SelectItem>
                    <SelectItem value="warn">Warning</SelectItem>
                    <SelectItem value="info">Info</SelectItem>
                    <SelectItem value="debug">Debug</SelectItem>
                    <SelectItem value="trace">Trace</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-between pt-2">
                <div className="space-y-0.5">
                  <Label htmlFor="analyticsEnabled">Enable Analytics</Label>
                  <p className="text-sm text-gray-500">
                    Collect and analyze transaction data for reporting
                  </p>
                </div>
                <Switch
                  id="analyticsEnabled"
                  checked={analyticsEnabled}
                  onCheckedChange={setAnalyticsEnabled}
                  disabled={isLoading}
                />
              </div>

              <div className="flex items-center justify-between pt-2">
                <div className="space-y-0.5">
                  <Label htmlFor="receiptEnabled">Enable Receipts</Label>
                  <p className="text-sm text-gray-500">
                    Allow vendors to generate receipts for transactions
                  </p>
                </div>
                <Switch
                  id="receiptEnabled"
                  checked={receiptEnabled}
                  onCheckedChange={setReceiptEnabled}
                  disabled={isLoading}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end">
        <Button onClick={saveSettings} disabled={isLoading || isSaving}>
          {isSaving ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save All Settings
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
