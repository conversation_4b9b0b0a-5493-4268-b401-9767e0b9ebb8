import { MetadataRoute } from 'next';
import { db } from '@/lib/prisma';
import { generateEventUrl } from '@/lib/utils/events';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

  // Static pages
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/events`,
      lastModified: new Date(),
      changeFrequency: 'hourly' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.5,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.5,
    },
  ];

  try {
    // Get all published events
    const events = await db.event.findMany({
      where: {
        status: 'Published',
      },
      select: {
        id: true,
        title: true,
        category: true,
        updatedAt: true,
        startDate: true,
      },
      orderBy: {
        startDate: 'desc',
      },
    });

    // Generate event pages
    const eventPages = events.map((event) => ({
      url: `${baseUrl}${generateEventUrl(event)}`,
      lastModified: event.updatedAt,
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    }));

    // Get all categories for category pages
    const categories = await db.event.groupBy({
      by: ['category'],
      where: {
        status: 'Published',
      },
    });

    const categoryPages = categories.map((cat) => ({
      url: `${baseUrl}/events/category/${cat.category.toLowerCase()}`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.7,
    }));

    return [...staticPages, ...eventPages, ...categoryPages];
  } catch (error) {
    console.error('Error generating sitemap:', error);
    return staticPages;
  }
}
