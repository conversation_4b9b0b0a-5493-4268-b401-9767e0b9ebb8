'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { RoleGate } from '@/components/auth/role-gate';
import Link from 'next/link';
import { Calendar, CreditCard, Key, LinkIcon, Share2 } from 'lucide-react';

export default function IntegrationsPage() {
  const integrations = [
    {
      id: 'webhooks',
      title: 'Webhooks',
      description: 'Receive real-time notifications about events in your account',
      icon: <LinkIcon className="h-8 w-8 text-blue-500" />,
      href: '/dashboard/organizer/integrations/webhooks',
      status: 'Available'
    },
    {
      id: 'payment',
      title: 'Payment Gateways',
      description: 'Connect payment providers to accept payments for your events',
      icon: <CreditCard className="h-8 w-8 text-green-500" />,
      href: '/dashboard/organizer/integrations/payment',
      status: 'Available'
    },
    {
      id: 'calendar',
      title: 'Calendar Sync',
      description: 'Sync your events with external calendar services',
      icon: <Calendar className="h-8 w-8 text-purple-500" />,
      href: '/dashboard/organizer/integrations/calendar',
      status: 'Available'
    },
    {
      id: 'social',
      title: 'Social Media',
      description: 'Connect and share your events on social media platforms',
      icon: <Share2 className="h-8 w-8 text-pink-500" />,
      href: '/dashboard/organizer/integrations/social',
      status: 'Available'
    },
    {
      id: 'api',
      title: 'Developer API',
      description: 'Access our API to build custom integrations',
      icon: <Key className="h-8 w-8 text-indigo-500" />,
      href: '/dashboard/developer',
      status: 'Available',
      badge: 'API'
    }
  ];

  return (
    <RoleGate allowedRole="ORGANIZER">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Integrations</h1>
          <p className="text-gray-500 mt-1">Connect your events with external services and platforms</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {integrations.map((integration) => (
            <Card key={integration.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">{integration.title}</CardTitle>
                  <div className="px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full">
                    {integration.status}
                  </div>
                </div>
                <CardDescription>{integration.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center py-4">
                  <div className="p-4 bg-gray-50 rounded-full">
                    {integration.icon}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="bg-gray-50 border-t p-4">
                <Link href={integration.href} className="w-full">
                  <Button variant="default" className="w-full">
                    Configure
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </RoleGate>
  );
}
