/**
 * Real WebSocket Service Implementation
 *
 * This file provides a production-ready implementation of the WebSocket service
 * that connects to a Socket.IO server with authentication and filtering.
 */

import { getSession } from 'next-auth/react';

/**
 * Enhanced WebSocket service for real-time messaging and communication
 * Supports messaging, typing indicators, read receipts, and user presence
 */
export class RealWebSocketService {
  private socket: WebSocket | null = null;
  private listeners: Map<string, ((data: any) => void)[]> = new Map();
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private connected = false;
  private userId: string | null = null;
  private eventId: string | null = null;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private typingTimeout: NodeJS.Timeout | null = null;
  private metrics = {
    concurrentUsers: 0,
    transactionsPerMinute: 0,
    messagesPerMinute: 0,
    activeRooms: 0
  };

  constructor(url: string) {
    this.url = url;
  }

  // Initialize connection with user context
  async connectWithAuth(userId: string, eventId: string, authToken?: string): Promise<void> {
    this.userId = userId;
    this.eventId = eventId;

    const wsUrl = `${this.url}?userId=${userId}&eventId=${eventId}${authToken ? `&token=${authToken}` : ''}`;
    return this.connect(wsUrl);
  }

  // Check if we're in a browser environment
  private isBrowserEnv(): boolean {
    return typeof window !== 'undefined' && typeof WebSocket !== 'undefined';
  }

  async connect(): Promise<void> {
    return new Promise(async (resolve, reject) => {
      // Check if we're in a browser environment
      if (!this.isBrowserEnv()) {
        console.log('WebSocket not available (server-side rendering)');
        resolve(); // Resolve without error for SSR
        return;
      }

      try {
        // Get authentication token from session
        const session = await getSession();
        if (!session) {
          console.warn('No session found, connecting without authentication');
        }

        // Create WebSocket URL with authentication token
        const wsUrl = new URL(this.url, window.location.origin);
        wsUrl.protocol = wsUrl.protocol.replace('http', 'ws');

        // Add authentication token as query parameter if available
        if (session?.user?.id) {
          wsUrl.searchParams.append('token', session.user.id);
        }

        // Connect to WebSocket server
        this.socket = new WebSocket(wsUrl.toString());

        // Handle connection events
        this.socket.onopen = () => {
          console.log('WebSocket connection established');
          this.connected = true;
          this.reconnectAttempts = 0;
          resolve();
        };

        // Handle incoming messages
        this.socket.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            if (message && message.type) {
              this.handleMessage(message);
            }
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
          }
        };

        // Handle connection errors
        this.socket.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.connected = false;
          reject(error);
        };

        // Handle disconnection
        this.socket.onclose = (event) => {
          console.log(`WebSocket disconnected: ${event.code} ${event.reason}`);
          this.connected = false;

          // Attempt to reconnect if not closed cleanly
          if (event.code !== 1000) {
            this.attemptReconnect();
          }
        };
      } catch (error) {
        console.error('Error creating WebSocket:', error);
        reject(error);
      }
    });
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);

      console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);

      this.reconnectTimeout = setTimeout(() => {
        console.log(`Reconnecting... (attempt ${this.reconnectAttempts})`);
        this.connect().catch(error => {
          console.error('Reconnection failed:', error);
        });
      }, delay);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }

  subscribe(eventType: string, callback: (data: any) => void): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }

    this.listeners.get(eventType)!.push(callback);

    // Return unsubscribe function
    return () => {
      const callbacks = this.listeners.get(eventType);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index !== -1) {
          callbacks.splice(index, 1);
        }
      }
    };
  }

  private notifyListeners(eventType: string, data: any) {
    const callbacks = this.listeners.get(eventType);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in ${eventType} listener:`, error);
        }
      });
    }
  }

  disconnect() {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }

    this.connected = false;
    this.listeners.clear();
  }

  isConnected(): boolean {
    return this.connected && this.socket?.readyState === WebSocket.OPEN;
  }

  // Get current metrics
  getMetrics() {
    return this.metrics;
  }

  // Enhanced message handling
  private handleMessage(message: any) {
    this.notifyListeners(message.type, message.data);

    // Handle specific message types
    switch (message.type) {
      case 'metrics_update':
        this.metrics = { ...this.metrics, ...message.data };
        break;
      case 'auth_success':
        console.log('WebSocket authentication successful');
        break;
      case 'auth_error':
        console.error('WebSocket authentication failed:', message.data);
        break;
      case 'message':
        this.handleIncomingMessage(message.data);
        break;
      case 'typing':
        this.handleTypingIndicator(message.data);
        break;
      case 'read_receipt':
        this.handleReadReceipt(message.data);
        break;
      case 'user_status':
        this.handleUserStatus(message.data);
        break;
    }
  }

  private handleIncomingMessage(data: any) {
    // Update metrics
    this.metrics.messagesPerMinute++;
    this.notifyListeners('new_message', data);
  }

  private handleTypingIndicator(data: any) {
    this.notifyListeners('typing_indicator', data);
  }

  private handleReadReceipt(data: any) {
    this.notifyListeners('read_receipt', data);
  }

  private handleUserStatus(data: any) {
    this.notifyListeners('user_status_change', data);
  }

  // Start heartbeat to keep connection alive
  private startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected()) {
        this.send('ping', { timestamp: Date.now() });
      }
    }, 30000); // Send ping every 30 seconds
  }

  // Stop heartbeat
  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  // Send a message to the server
  send(type: string, data: any) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      try {
        const message = {
          type,
          data,
          userId: this.userId,
          eventId: this.eventId,
          timestamp: new Date().toISOString()
        };
        this.socket.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error('Error sending WebSocket message:', error);
        return false;
      }
    }
    return false;
  }

  // Messaging-specific methods
  sendMessage(recipientId: string, content: string, chatRoomId?: string) {
    return this.send('message', {
      recipientId,
      content,
      chatRoomId,
      messageType: 'text'
    });
  }

  sendTypingIndicator(recipientId: string, isTyping: boolean, chatRoomId?: string) {
    // Clear existing typing timeout
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }

    this.send('typing', {
      recipientId,
      isTyping,
      chatRoomId
    });

    // Auto-stop typing after 3 seconds
    if (isTyping) {
      this.typingTimeout = setTimeout(() => {
        this.sendTypingIndicator(recipientId, false, chatRoomId);
      }, 3000);
    }
  }

  markMessageAsRead(messageId: string, senderId: string) {
    return this.send('read_receipt', {
      messageId,
      senderId,
      readAt: new Date().toISOString()
    });
  }

  joinChatRoom(chatRoomId: string) {
    return this.send('join_room', { chatRoomId });
  }

  leaveChatRoom(chatRoomId: string) {
    return this.send('leave_room', { chatRoomId });
  }

  updateUserStatus(status: 'online' | 'away' | 'busy' | 'offline') {
    return this.send('user_status', { status });
  }
}

// Singleton instance for the application
let realWebsocketService: RealWebSocketService | null = null;

// This function provides the real WebSocket service for production
export const getRealWebSocketService = (url: string = '/api/socket') => {
  if (!realWebsocketService) {
    realWebsocketService = new RealWebSocketService(url);
  }
  return realWebsocketService;
};

export const closeRealWebSocketService = () => {
  if (realWebsocketService) {
    realWebsocketService.disconnect();
    realWebsocketService = null;
  }
};