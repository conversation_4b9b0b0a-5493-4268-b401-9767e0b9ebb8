'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/use-toast';
import { 
  PlusCircle, 
  Edit, 
  Trash2, 
  Smartphone, 
  Calendar, 
  Tag, 
  User, 
  Clock, 
  CheckCircle2, 
  XCircle, 
  AlertCircle 
} from 'lucide-react';
import { format } from 'date-fns';

// Device status options
const deviceStatusOptions = [
  { value: 'AVAILABLE', label: 'Available' },
  { value: 'RENTED', label: 'Rented' },
  { value: 'MAINTENANCE', label: 'Maintenance' },
  { value: 'DAMAGED', label: 'Damaged' },
  { value: 'RETIRED', label: 'Retired' }
];

// Rental status options
const rentalStatusOptions = [
  { value: 'PENDING', label: 'Pending' },
  { value: 'ACTIVE', label: 'Active' },
  { value: 'COMPLETED', label: 'Completed' },
  { value: 'CANCELLED', label: 'Cancelled' }
];

interface POSDevice {
  id: string;
  deviceId: string;
  serialNumber: string;
  model: string;
  manufacturer: string;
  purchaseDate: string;
  status: string;
  lastMaintenance?: string;
  nextMaintenance?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  rentals: POSDeviceRental[];
}

interface POSDeviceRental {
  id: string;
  deviceId: string;
  vendorId: string;
  eventId?: string;
  rentalStartDate: string;
  rentalEndDate?: string;
  status: string;
  rentalFee: number;
  depositAmount?: number;
  isReturned: boolean;
  returnDate?: string;
  condition?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  vendor: {
    id: string;
    businessName: string;
  };
  event?: {
    id: string;
    title: string;
  };
}

export default function POSDevicesPage() {
  const router = useRouter();
  const [devices, setDevices] = useState<POSDevice[]>([]);
  const [rentals, setRentals] = useState<POSDeviceRental[]>([]);
  const [loading, setLoading] = useState(true);
  const [openDeviceDialog, setOpenDeviceDialog] = useState(false);
  const [openRentalDialog, setOpenRentalDialog] = useState(false);
  const [editingDevice, setEditingDevice] = useState<POSDevice | null>(null);
  const [activeTab, setActiveTab] = useState('devices');
  
  // Device form state
  const [deviceId, setDeviceId] = useState('');
  const [serialNumber, setSerialNumber] = useState('');
  const [model, setModel] = useState('');
  const [manufacturer, setManufacturer] = useState('');
  const [purchaseDate, setPurchaseDate] = useState('');
  const [deviceStatus, setDeviceStatus] = useState('AVAILABLE');
  const [notes, setNotes] = useState('');
  
  // Load devices and rentals
  useEffect(() => {
    fetchDevices();
    fetchRentals();
  }, []);
  
  const fetchDevices = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/pos-devices');
      
      if (!response.ok) {
        throw new Error('Failed to fetch devices');
      }
      
      const data = await response.json();
      setDevices(data);
    } catch (error) {
      console.error('Error fetching devices:', error);
      toast({
        title: 'Error',
        description: 'Failed to load POS devices',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };
  
  const fetchRentals = async () => {
    try {
      const response = await fetch('/api/admin/pos-rentals');
      
      if (!response.ok) {
        throw new Error('Failed to fetch rentals');
      }
      
      const data = await response.json();
      setRentals(data);
    } catch (error) {
      console.error('Error fetching rentals:', error);
      toast({
        title: 'Error',
        description: 'Failed to load POS device rentals',
        variant: 'destructive'
      });
    }
  };
  
  const resetDeviceForm = () => {
    setDeviceId('');
    setSerialNumber('');
    setModel('');
    setManufacturer('');
    setPurchaseDate('');
    setDeviceStatus('AVAILABLE');
    setNotes('');
    setEditingDevice(null);
  };
  
  const handleOpenDeviceDialog = (device?: POSDevice) => {
    if (device) {
      // Edit mode
      setEditingDevice(device);
      setDeviceId(device.deviceId);
      setSerialNumber(device.serialNumber);
      setModel(device.model);
      setManufacturer(device.manufacturer);
      setPurchaseDate(device.purchaseDate.split('T')[0]);
      setDeviceStatus(device.status);
      setNotes(device.notes || '');
    } else {
      // Create mode
      resetDeviceForm();
      setPurchaseDate(new Date().toISOString().split('T')[0]);
    }
    
    setOpenDeviceDialog(true);
  };
  
  const handleCloseDeviceDialog = () => {
    setOpenDeviceDialog(false);
    resetDeviceForm();
  };
  
  const handleSubmitDevice = async () => {
    try {
      // Validate form
      if (!deviceId || !serialNumber || !model || !manufacturer || !purchaseDate) {
        toast({
          title: 'Validation Error',
          description: 'Please fill in all required fields',
          variant: 'destructive'
        });
        return;
      }
      
      const deviceData = {
        deviceId,
        serialNumber,
        model,
        manufacturer,
        purchaseDate,
        status: deviceStatus,
        notes
      };
      
      let response;
      
      if (editingDevice) {
        // Update existing device
        response = await fetch(`/api/admin/pos-devices/${editingDevice.id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(deviceData)
        });
      } else {
        // Create new device
        response = await fetch('/api/admin/pos-devices', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(deviceData)
        });
      }
      
      if (!response.ok) {
        throw new Error('Failed to save POS device');
      }
      
      toast({
        title: 'Success',
        description: editingDevice 
          ? 'POS device updated successfully' 
          : 'POS device created successfully'
      });
      
      handleCloseDeviceDialog();
      fetchDevices();
    } catch (error) {
      console.error('Error saving POS device:', error);
      toast({
        title: 'Error',
        description: 'Failed to save POS device',
        variant: 'destructive'
      });
    }
  };
  
  const handleDeleteDevice = async (id: string) => {
    if (!confirm('Are you sure you want to delete this POS device?')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/admin/pos-devices/${id}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete POS device');
      }
      
      toast({
        title: 'Success',
        description: 'POS device deleted successfully'
      });
      
      fetchDevices();
    } catch (error: any) {
      console.error('Error deleting POS device:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete POS device',
        variant: 'destructive'
      });
    }
  };
  
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'AVAILABLE':
        return 'bg-green-100 text-green-800';
      case 'RENTED':
        return 'bg-blue-100 text-blue-800';
      case 'MAINTENANCE':
        return 'bg-yellow-100 text-yellow-800';
      case 'DAMAGED':
        return 'bg-red-100 text-red-800';
      case 'RETIRED':
        return 'bg-gray-100 text-gray-800';
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'COMPLETED':
        return 'bg-blue-100 text-blue-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  const handleViewRentals = (deviceId: string) => {
    setActiveTab('rentals');
    // Could add filtering by device ID here
  };
  
  const handleCancelRental = async (id: string) => {
    if (!confirm('Are you sure you want to cancel this rental?')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/admin/pos-rentals/${id}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        throw new Error('Failed to cancel rental');
      }
      
      toast({
        title: 'Success',
        description: 'Rental cancelled successfully'
      });
      
      fetchRentals();
      fetchDevices();
    } catch (error) {
      console.error('Error cancelling rental:', error);
      toast({
        title: 'Error',
        description: 'Failed to cancel rental',
        variant: 'destructive'
      });
    }
  };
  
  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">POS Device Management</h1>
          <p className="text-gray-500">Manage POS devices and rentals</p>
        </div>
        <Button onClick={() => handleOpenDeviceDialog()}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Add New Device
        </Button>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="devices">Devices</TabsTrigger>
          <TabsTrigger value="rentals">Rentals</TabsTrigger>
        </TabsList>
        
        <TabsContent value="devices">
          <Card>
            <CardHeader>
              <CardTitle>POS Devices</CardTitle>
              <CardDescription>
                Manage your inventory of POS devices
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center items-center h-40">
                  <p>Loading devices...</p>
                </div>
              ) : devices.length === 0 ? (
                <div className="flex flex-col justify-center items-center h-40">
                  <p className="text-gray-500 mb-4">No POS devices found</p>
                  <Button onClick={() => handleOpenDeviceDialog()}>
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Add New Device
                  </Button>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Device ID</TableHead>
                      <TableHead>Model</TableHead>
                      <TableHead>Serial Number</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Purchase Date</TableHead>
                      <TableHead>Current Rental</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {devices.map((device) => (
                      <TableRow key={device.id}>
                        <TableCell>
                          <div className="font-medium">{device.deviceId}</div>
                        </TableCell>
                        <TableCell>
                          <div>{device.manufacturer}</div>
                          <div className="text-sm text-gray-500">{device.model}</div>
                        </TableCell>
                        <TableCell>{device.serialNumber}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs ${getStatusBadgeColor(device.status)}`}>
                            {device.status}
                          </span>
                        </TableCell>
                        <TableCell>
                          {format(new Date(device.purchaseDate), 'MMM d, yyyy')}
                        </TableCell>
                        <TableCell>
                          {device.rentals && device.rentals.length > 0 ? (
                            <div>
                              <div className="font-medium">{device.rentals[0].vendor.businessName}</div>
                              <div className="text-xs text-gray-500">
                                {format(new Date(device.rentals[0].rentalStartDate), 'MMM d, yyyy')}
                                {device.rentals[0].rentalEndDate && (
                                  <> - {format(new Date(device.rentals[0].rentalEndDate), 'MMM d, yyyy')}</>
                                )}
                              </div>
                            </div>
                          ) : (
                            <span className="text-gray-500">Not rented</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleOpenDeviceDialog(device)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleViewRentals(device.id)}
                            >
                              <Calendar className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteDevice(device.id)}
                              disabled={device.status === 'RENTED'}
                            >
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="rentals">
          <Card>
            <CardHeader>
              <CardTitle>POS Device Rentals</CardTitle>
              <CardDescription>
                View and manage device rentals
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center items-center h-40">
                  <p>Loading rentals...</p>
                </div>
              ) : rentals.length === 0 ? (
                <div className="flex justify-center items-center h-40">
                  <p className="text-gray-500">No rentals found</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Vendor</TableHead>
                      <TableHead>Device</TableHead>
                      <TableHead>Event</TableHead>
                      <TableHead>Rental Period</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Rental Fee</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {rentals.map((rental) => (
                      <TableRow key={rental.id}>
                        <TableCell>
                          <div className="font-medium">{rental.vendor.businessName}</div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Smartphone className="h-4 w-4 mr-2 text-gray-500" />
                            <span>{rental.device?.deviceId || 'Unknown'}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          {rental.event ? (
                            <div>{rental.event.title}</div>
                          ) : (
                            <span className="text-gray-500">No event</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="text-xs">
                            From: {format(new Date(rental.rentalStartDate), 'MMM d, yyyy')}
                          </div>
                          {rental.rentalEndDate && (
                            <div className="text-xs">
                              To: {format(new Date(rental.rentalEndDate), 'MMM d, yyyy')}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs ${getStatusBadgeColor(rental.status)}`}>
                            {rental.status}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">
                            ${rental.rentalFee.toFixed(2)}
                          </div>
                          {rental.depositAmount && (
                            <div className="text-xs text-gray-500">
                              Deposit: ${rental.depositAmount.toFixed(2)}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => router.push(`/admin/pos-rentals/${rental.id}`)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            {rental.status === 'ACTIVE' && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => router.push(`/admin/pos-rentals/${rental.id}/complete`)}
                              >
                                <CheckCircle2 className="h-4 w-4 text-green-500" />
                              </Button>
                            )}
                            {(rental.status === 'PENDING' || rental.status === 'ACTIVE') && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleCancelRental(rental.id)}
                              >
                                <XCircle className="h-4 w-4 text-red-500" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      <Dialog open={openDeviceDialog} onOpenChange={setOpenDeviceDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {editingDevice ? 'Edit POS Device' : 'Add New POS Device'}
            </DialogTitle>
            <DialogDescription>
              {editingDevice
                ? 'Update the details of this POS device'
                : 'Add a new POS device to your inventory'}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="deviceId">Device ID</Label>
                <Input
                  id="deviceId"
                  value={deviceId}
                  onChange={(e) => setDeviceId(e.target.value)}
                  placeholder="e.g. POS-001"
                  disabled={!!editingDevice}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="serialNumber">Serial Number</Label>
                <Input
                  id="serialNumber"
                  value={serialNumber}
                  onChange={(e) => setSerialNumber(e.target.value)}
                  placeholder="e.g. SN12345678"
                  disabled={!!editingDevice}
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="manufacturer">Manufacturer</Label>
                <Input
                  id="manufacturer"
                  value={manufacturer}
                  onChange={(e) => setManufacturer(e.target.value)}
                  placeholder="e.g. Verifone"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="model">Model</Label>
                <Input
                  id="model"
                  value={model}
                  onChange={(e) => setModel(e.target.value)}
                  placeholder="e.g. V200c"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="purchaseDate">Purchase Date</Label>
                <Input
                  id="purchaseDate"
                  type="date"
                  value={purchaseDate}
                  onChange={(e) => setPurchaseDate(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select value={deviceStatus} onValueChange={setDeviceStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {deviceStatusOptions.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Input
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Additional notes about this device"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={handleCloseDeviceDialog}>
              Cancel
            </Button>
            <Button onClick={handleSubmitDevice}>
              {editingDevice ? 'Update' : 'Create'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
