<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Events API Documentation</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    header {
      margin-bottom: 40px;
      text-align: center;
      border-bottom: 1px solid #eee;
      padding-bottom: 20px;
    }

    h1 {
      font-size: 2.5rem;
      margin-bottom: 10px;
    }

    .subtitle {
      color: #666;
      font-size: 1.2rem;
      margin-top: 0;
    }

    h2 {
      margin-top: 40px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }

    h3 {
      margin-top: 30px;
      color: #2c5282;
    }

    code {
      font-family: 'Courier New', Courier, monospace;
      background-color: #f5f5f5;
      padding: 2px 5px;
      border-radius: 3px;
      font-size: 0.9em;
    }

    pre {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      overflow-x: auto;
      font-family: 'Courier New', Courier, monospace;
      font-size: 0.9em;
      line-height: 1.4;
    }

    .endpoint {
      margin-bottom: 40px;
      padding: 20px;
      border: 1px solid #eee;
      border-radius: 5px;
      background-color: #fafafa;
    }

    .endpoint-title {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
    }

    .method {
      padding: 5px 10px;
      border-radius: 4px;
      color: white;
      font-weight: bold;
      margin-right: 10px;
      font-size: 0.8em;
    }

    .get {
      background-color: #38a169;
    }

    .post {
      background-color: #3182ce;
    }

    .put {
      background-color: #dd6b20;
    }

    .delete {
      background-color: #e53e3e;
    }

    .options {
      background-color: #718096;
    }

    .url {
      font-family: 'Courier New', Courier, monospace;
      font-weight: bold;
    }

    .params-table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
    }

    .params-table th, .params-table td {
      padding: 10px;
      text-align: left;
      border-bottom: 1px solid #eee;
    }

    .params-table th {
      background-color: #f5f5f5;
      font-weight: bold;
    }

    .response-example {
      margin-top: 20px;
    }

    .nav {
      position: sticky;
      top: 20px;
      float: right;
      width: 250px;
      margin-left: 30px;
      padding: 15px;
      background-color: #f5f5f5;
      border-radius: 5px;
    }

    .nav ul {
      list-style-type: none;
      padding: 0;
      margin: 0;
    }

    .nav li {
      margin-bottom: 10px;
    }

    .nav a {
      color: #2c5282;
      text-decoration: none;
    }

    .nav a:hover {
      text-decoration: underline;
    }

    .main-content {
      margin-right: 280px;
    }

    @media (max-width: 768px) {
      .nav {
        float: none;
        width: auto;
        margin-left: 0;
        margin-bottom: 30px;
        position: static;
      }

      .main-content {
        margin-right: 0;
      }
    }
  </style>
</head>
<body>
  <header>
    <h1>Events API Documentation</h1>
    <p class="subtitle">A comprehensive guide to using the Events API</p>
  </header>

  <nav class="nav">
    <h3>Endpoints</h3>
    <ul>
      <li><a href="#events">GET /events</a></li>
      <li><a href="#event-details">GET /events/:id</a></li>
      <li><a href="#search">GET /events/search</a></li>
      <li><a href="#search-post">POST /events/search</a></li>
      <li><a href="#categories">GET /events/categories</a></li>
      <li><a href="#types">GET /events/types</a></li>
    </ul>

    <h3>Resources</h3>
    <ul>
      <li><a href="#examples">Code Examples</a></li>
      <li><a href="#errors">Error Handling</a></li>
      <li><a href="#cors">CORS Support</a></li>
    </ul>
  </nav>

  <div class="main-content">
    <h2>Introduction</h2>
    <p>
      The Events API provides access to event data, allowing developers to retrieve, search, and filter events.
      All endpoints return data in JSON format and support CORS for cross-origin requests.
    </p>

    <h2>Base URL</h2>
    <p>
      All API requests should be made to:
      <code>http://localhost:3001</code> (for development)
    </p>
    <p>
      For production, use your domain:
      <code>https://your-domain.com</code>
    </p>

    <h2>Authentication</h2>
    <p>
      Currently, the API is open and does not require authentication. Future versions may implement API key authentication.
    </p>

    <h2>Endpoints</h2>

    <div id="events" class="endpoint">
      <div class="endpoint-title">
        <span class="method get">GET</span>
        <span class="url">/api/events/published</span>
      </div>
      <p>Returns a list of published events with pagination support.</p>

      <h4>Query Parameters</h4>
      <table class="params-table">
        <thead>
          <tr>
            <th>Parameter</th>
            <th>Type</th>
            <th>Default</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>page</td>
            <td>integer</td>
            <td>1</td>
            <td>Page number for pagination</td>
          </tr>
          <tr>
            <td>limit</td>
            <td>integer</td>
            <td>10</td>
            <td>Number of events per page</td>
          </tr>
          <tr>
            <td>category</td>
            <td>string</td>
            <td>-</td>
            <td>Filter events by category</td>
          </tr>
          <tr>
            <td>type</td>
            <td>string</td>
            <td>-</td>
            <td>Filter events by type (PHYSICAL, ONLINE, HYBRID)</td>
          </tr>
        </tbody>
      </table>

      <h4>Example Request</h4>
      <pre>GET /api/events/published?page=1&limit=10&category=MUSIC&type=PHYSICAL</pre>

      <h4>Example Response</h4>
      <div class="response-example">
        <pre>{
  "events": [
    {
      "id": "cm8x4f9b4000d1osco82qih2p",
      "title": "Open-Air Concerts",
      "description": "Description: Large concerts held in outdoor spaces...",
      "startDate": "2025-05-27T16:43:00.000Z",
      "endDate": "2025-05-28T13:45:00.000Z",
      "location": "Lusaka, Zambia",
      "venue": "mwandila house",
      "category": "MUSIC",
      "eventType": "PHYSICAL",
      "status": "Published",
      "user": {
        "name": "Blessing Mwandila",
        "image": null
      },
      // Additional fields...
    },
    // More events...
  ],
  "pagination": {
    "total": 4,
    "pages": 1,
    "page": 1,
    "limit": 10
  }
}</pre>
      </div>
    </div>

    <div id="event-details" class="endpoint">
      <div class="endpoint-title">
        <span class="method get">GET</span>
        <span class="url">/api/events/:id</span>
      </div>
      <p>Returns detailed information about a specific event.</p>

      <h4>Path Parameters</h4>
      <table class="params-table">
        <thead>
          <tr>
            <th>Parameter</th>
            <th>Type</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>id</td>
            <td>string</td>
            <td>The unique identifier of the event</td>
          </tr>
        </tbody>
      </table>

      <h4>Example Request</h4>
      <pre>GET /api/events/cm8x4f9b4000d1osco82qih2p</pre>

      <h4>Example Response</h4>
      <div class="response-example">
        <pre>{
  "id": "cm8x4f9b4000d1osco82qih2p",
  "title": "Open-Air Concerts",
  "description": "Description: Large concerts held in outdoor spaces...",
  "startDate": "2025-05-27T16:43:00.000Z",
  "endDate": "2025-05-28T13:45:00.000Z",
  "location": "Lusaka, Zambia",
  "venue": "mwandila house",
  "category": "MUSIC",
  "eventType": "PHYSICAL",
  "status": "Published",
  "user": {
    "name": "Blessing Mwandila",
    "image": null
  },
  "ageRestriction": {
    "id": "d332489f-b0ac-4754-980e-6f58806ca30b",
    "minAge": 15,
    "maxAge": 38,
    "ageGroups": "18-20",
    "description": "no drinks from outside"
  },
  "ParkingManagement": [
    {
      "id": "061368ef-aa3c-4b48-b313-9be1c2dbb8bd",
      "totalSpaces": 50,
      "reservedSpaces": 16,
      "pricePerHour": null,
      "isFree": true,
      "reservationRequired": true,
      "description": "no heavy duty",
      "eventId": "cm8x4f9b4000d1osco82qih2p"
    }
  ]
}</pre>
      </div>
    </div>

    <div id="search" class="endpoint">
      <div class="endpoint-title">
        <span class="method get">GET</span>
        <span class="url">/api/events/search</span>
      </div>
      <p>Search for events using various criteria.</p>

      <h4>Query Parameters</h4>
      <table class="params-table">
        <thead>
          <tr>
            <th>Parameter</th>
            <th>Type</th>
            <th>Default</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>q</td>
            <td>string</td>
            <td>-</td>
            <td>Search query (searches title, description, location, venue)</td>
          </tr>
          <tr>
            <td>location</td>
            <td>string</td>
            <td>-</td>
            <td>Filter by location</td>
          </tr>
          <tr>
            <td>category</td>
            <td>string</td>
            <td>-</td>
            <td>Filter by category</td>
          </tr>
          <tr>
            <td>type</td>
            <td>string</td>
            <td>-</td>
            <td>Filter by event type</td>
          </tr>
          <tr>
            <td>page</td>
            <td>integer</td>
            <td>1</td>
            <td>Page number for pagination</td>
          </tr>
          <tr>
            <td>limit</td>
            <td>integer</td>
            <td>10</td>
            <td>Number of events per page</td>
          </tr>
        </tbody>
      </table>

      <h4>Example Request</h4>
      <pre>GET /api/events/search?q=music&location=Lusaka&page=1&limit=10</pre>

      <h4>Example Response</h4>
      <div class="response-example">
        <pre>{
  "events": [
    // Array of events matching the search criteria
  ],
  "pagination": {
    "total": 3,
    "pages": 1,
    "page": 1,
    "limit": 10
  }
}</pre>
      </div>
    </div>

    <div id="search-post" class="endpoint">
      <div class="endpoint-title">
        <span class="method post">POST</span>
        <span class="url">/api/events/search</span>
      </div>
      <p>Search for events using a more complex query structure.</p>

      <h4>Request Body</h4>
      <table class="params-table">
        <thead>
          <tr>
            <th>Parameter</th>
            <th>Type</th>
            <th>Default</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>query</td>
            <td>string</td>
            <td>-</td>
            <td>Search query (searches title, description, location, venue)</td>
          </tr>
          <tr>
            <td>location</td>
            <td>string</td>
            <td>-</td>
            <td>Filter by location</td>
          </tr>
          <tr>
            <td>category</td>
            <td>string</td>
            <td>-</td>
            <td>Filter by category</td>
          </tr>
          <tr>
            <td>eventType</td>
            <td>string</td>
            <td>-</td>
            <td>Filter by event type</td>
          </tr>
          <tr>
            <td>title</td>
            <td>string</td>
            <td>-</td>
            <td>Filter by title</td>
          </tr>
          <tr>
            <td>page</td>
            <td>integer</td>
            <td>1</td>
            <td>Page number for pagination</td>
          </tr>
          <tr>
            <td>limit</td>
            <td>integer</td>
            <td>10</td>
            <td>Number of events per page</td>
          </tr>
        </tbody>
      </table>

      <h4>Example Request</h4>
      <pre>POST /events/search
Content-Type: application/json

{
  "query": "music",
  "location": "Lusaka",
  "category": "MUSIC",
  "eventType": "PHYSICAL",
  "page": 1,
  "limit": 10
}</pre>

      <h4>Example Response</h4>
      <div class="response-example">
        <pre>{
  "events": [
    // Array of events matching the search criteria
  ],
  "pagination": {
    "total": 2,
    "pages": 1,
    "page": 1,
    "limit": 10
  }
}</pre>
      </div>
    </div>

    <div id="categories" class="endpoint">
      <div class="endpoint-title">
        <span class="method get">GET</span>
        <span class="url">/api/events/categories</span>
      </div>
      <p>Returns a list of all event categories.</p>

      <h4>Example Request</h4>
      <pre>GET /api/events/categories</pre>

      <h4>Example Response</h4>
      <div class="response-example">
        <pre>{
  "categories": [
    {
      "id": "WEDDING",
      "name": "Wedding"
    },
    {
      "id": "MUSIC",
      "name": "Music"
    },
    // More categories...
  ]
}</pre>
      </div>
    </div>

    <div id="types" class="endpoint">
      <div class="endpoint-title">
        <span class="method get">GET</span>
        <span class="url">/api/events/types</span>
      </div>
      <p>Returns a list of all event types.</p>

      <h4>Example Request</h4>
      <pre>GET /api/events/types</pre>

      <h4>Example Response</h4>
      <div class="response-example">
        <pre>{
  "types": [
    {
      "id": "PHYSICAL",
      "name": "Physical"
    },
    {
      "id": "ONLINE",
      "name": "Online"
    },
    {
      "id": "HYBRID",
      "name": "Hybrid"
    }
  ]
}</pre>
      </div>
    </div>

    <h2 id="examples">Code Examples</h2>

    <h3>JavaScript Fetch API</h3>
    <pre>// Get all events
fetch('http://localhost:3001/api/events/published')
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));

// Search for events
fetch('http://localhost:3001/api/events/search?q=music')
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));

// Get event details
fetch('http://localhost:3001/api/events/published/event-id-here')
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));</pre>

    <h3>React Example</h3>
    <pre>// React component example
import React, { useState, useEffect } from 'react';

const EventsList = () => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetch('http://localhost:3001/api/events/published')
      .then(response => response.json())
      .then(data => {
        setEvents(data.events);
        setLoading(false);
      })
      .catch(error => {
        setError(error.message);
        setLoading(false);
      });
  }, []);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h2>Events</h2>
      <ul>
        {events.map(event => (
          <li key={event.id}>{event.title}</li>
        ))}
      </ul>
    </div>
  );
};</pre>

    <h2 id="errors">Error Handling</h2>
    <p>
      The API uses standard HTTP status codes to indicate the success or failure of a request.
      In case of an error, the response will include an error message.
    </p>

    <h3>Common Error Codes</h3>
    <table class="params-table">
      <thead>
        <tr>
          <th>Status Code</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>400</td>
          <td>Bad Request - The request was invalid or cannot be served</td>
        </tr>
        <tr>
          <td>404</td>
          <td>Not Found - The requested resource could not be found</td>
        </tr>
        <tr>
          <td>500</td>
          <td>Internal Server Error - Something went wrong on the server</td>
        </tr>
      </tbody>
    </table>

    <h3>Error Response Format</h3>
    <pre>{
  "error": "Error message here"
}</pre>

    <h2 id="cors">CORS Support</h2>
    <p>
      All API endpoints support Cross-Origin Resource Sharing (CORS), allowing them to be accessed from any origin.
      The API includes the following CORS headers:
    </p>

    <ul>
      <li><code>Access-Control-Allow-Origin: *</code></li>
      <li><code>Access-Control-Allow-Methods: GET, POST, OPTIONS</code></li>
      <li><code>Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key</code></li>
      <li><code>Access-Control-Expose-Headers: X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Reset</code></li>
    </ul>

    <p>
      For preflight requests (OPTIONS), the API also includes:
    </p>

    <ul>
      <li><code>Access-Control-Max-Age: 86400</code> (24 hours)</li>
    </ul>

    <h2>Rate Limiting</h2>
    <p>
      Currently, the API does not implement rate limiting. Future versions may include rate limiting to prevent abuse.
    </p>

    <h2>Support</h2>
    <p>
      If you have any questions or need help with the API, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.
    </p>
  </div>
</body>
</html>
