# Wallet System Testing Guide

This document outlines the testing procedures for the financial management system across different user roles.

## Prerequisites

- Ensure the database has been migrated with the latest schema changes
- Have test accounts ready for each user role (USER, ORGANIZER, VENDOR, ADMIN)

## Test Cases by User Role

### 1. Regular User Testing

#### 1.1 Wallet Access
- [ ] Navigate to `/wallet` from the sidebar
- [ ] Navigate to `/wallet` from the user dropdown menu
- [ ] Verify wallet balance is displayed correctly in the navbar

#### 1.2 Top-up Functionality
- [ ] Access the top-up form
- [ ] Test adding funds with different payment methods
- [ ] Verify balance updates after successful top-up
- [ ] Verify transaction appears in transaction history

#### 1.3 Transfer Functionality
- [ ] Access the transfer form
- [ ] Test sending funds to another user
- [ ] Verify balance decreases after transfer
- [ ] Verify transaction appears in transaction history

#### 1.4 Transaction History
- [ ] View all transactions
- [ ] Test filtering by transaction type
- [ ] Test filtering by date range
- [ ] Test pagination

#### 1.5 Wallet Settings
- [ ] Change default currency
- [ ] Set low balance alert
- [ ] Configure auto top-up settings
- [ ] Verify settings are saved correctly

### 2. Organizer Testing

#### 2.1 Wallet Access
- [ ] Navigate to `/wallet` from the sidebar
- [ ] Verify organizer-specific actions are available

#### 2.2 Revenue Management
- [ ] Verify ticket sales appear in transaction history
- [ ] Verify platform fees are correctly deducted
- [ ] Test filtering transactions by event

#### 2.3 Withdrawal Process
- [ ] Initiate a withdrawal request
- [ ] Verify available balance updates
- [ ] Check withdrawal status tracking

### 3. Vendor Testing

#### 3.1 Wallet Access
- [ ] Navigate to `/wallet` from the sidebar
- [ ] Verify vendor-specific actions are available

#### 3.2 Sales Management
- [ ] Verify product sales appear in transaction history
- [ ] Verify platform fees are correctly deducted

#### 3.3 NFC Transaction Testing
- [ ] Process a test NFC payment
- [ ] Verify funds are received in vendor wallet
- [ ] Verify transaction appears in history

### 4. Admin Testing

#### 4.1 Wallet Access
- [ ] Navigate to `/wallet` from the sidebar
- [ ] Verify admin-specific actions are available

#### 4.2 User Transaction Oversight
- [ ] View transactions across all users
- [ ] Filter transactions by user
- [ ] Verify admin can see detailed transaction information

#### 4.3 Withdrawal Approval
- [ ] View pending withdrawal requests
- [ ] Approve a withdrawal request
- [ ] Reject a withdrawal request
- [ ] Verify status updates correctly

## Edge Case Testing

### Error Handling
- [ ] Test insufficient balance for transfers
- [ ] Test invalid input in amount fields
- [ ] Test network errors during transactions

### Security Testing
- [ ] Verify users cannot access other users' wallet data
- [ ] Test transaction limits
- [ ] Verify authentication requirements for sensitive operations

## Performance Testing
- [ ] Test loading large transaction histories
- [ ] Measure response time for financial operations
- [ ] Test concurrent transactions

## Notes
- Document any bugs or issues found during testing
- Note any UI/UX improvements that could be made
- Record performance metrics for optimization
