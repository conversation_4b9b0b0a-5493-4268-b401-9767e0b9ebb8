'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import {
  Calendar,
  MapPin,
  Clock,
  FileText,
  User,
  UserCheck,
  Award,
  Tag,
  Star,
  Share2,
  Ticket,
  Info,
  Users,
  Car,
  Heart,
  ChevronRight,
  ExternalLink,
  Hotel,
  Utensils,
  Wine,
  Crown,
  MessageCircle
} from 'lucide-react';
import { Event, EliteTier } from '@prisma/client';
import AppLayout from '@/components/ui/layout';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import ButtonTriggeredModal from './checkout/ButtonTriggeredModal';
import VenueMap from '@/components/events/VenueMap';
import AgeRestrictionInfo from '@/components/events/AgeRestrictionInfo';
import ParkingInfo from '@/components/events/ParkingInfo';
import EventMetaTags from '@/components/events/EventMetaTags';
import EventPartnersSection from '@/components/partners/EventPartnersSection';
import EliteCommunicationTab from '@/components/communication/EliteCommunicationTab';
import Image from 'next/image';
import Link from 'next/link';
import { generateEventUrl } from '@/lib/utils/events';

// Update the interface to match what the API returns with all related data
interface EventWithUser extends Event {
  user: {
    name: string;
    image?: string;
  };
  ageRestriction?: {
    minAge?: number;
    maxAge?: number;
    ageGroups?: string;
    description?: string;
  } | null;
  ParkingManagement?: {
    totalSpaces: number;
    reservedSpaces: number;
    pricePerHour?: number;
    isFree: boolean;
    reservationRequired: boolean;
    description?: string;
  }[];
  tickets?: {
    id: string;
    type: string;
    price: number;
    isAvailable: boolean;
    totalSeats: number;
  }[];
  sponsors?: {
    id: string;
    name: string;
    logo?: string;
    tier: string;
  }[];
  eventPartners?: {
    id: string;
    partnerId: string;
    partnerType: string;
    specialOffer?: string;
    partner: {
      id: string;
      businessName: string;
      partnerType: string;
      logo?: string;
      city: string;
    };
  }[];
  seoSettings?: {
    id?: string;
    title?: string;
    description?: string;
    keywords?: string[];
  } | null;
  socialSettings?: {
    id?: string;
    facebookTitle?: string;
    facebookDescription?: string;
    twitterTitle?: string;
    twitterDescription?: string;
    ogImage?: string;
  } | null;
}

interface EventDetailsProps {
  initialEvent?: EventWithUser;
}

const EventDetails = ({ initialEvent }: EventDetailsProps) => {
  const router = useRouter();
  const { id, category, title } = useParams();
  const { data: session } = useSession();

  const [event, setEvent] = useState<EventWithUser | null>(initialEvent || null);
  const [loading, setLoading] = useState<boolean>(!initialEvent);
  const [error, setError] = useState<string | null>(null);
  const [recommendedEvents, setRecommendedEvents] = useState<EventWithUser[]>([]);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [userEliteTier, setUserEliteTier] = useState<EliteTier>(EliteTier.BASIC);

  useEffect(() => {
    // If we already have the event data from the server, skip fetching
    if (initialEvent) {
      setEvent(initialEvent);
      setLoading(false);
      return;
    }

    const fetchEvent = async () => {
      try {
        setLoading(true);
        const eventId = typeof id === 'object' ? id.toString() : id;

        console.log('Fetching event with ID:', eventId);

        // Use a direct server endpoint without authentication
        const res = await fetch(`/api/public/events/${eventId}`, {
          cache: 'no-store',
        });

        if (!res.ok) {
          console.error('API response not OK:', res.status);
          throw new Error(`Failed to fetch event data: ${res.status}`);
        }

        const eventData = await res.json();
        console.log('Event data received:', eventData);
        setEvent(eventData);
      } catch (err) {
        console.error('Error in fetchEvent:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch event details');
      } finally {
        setLoading(false);
      }
    };

    if (id && !initialEvent) {
      fetchEvent();
    }

    // Add a function to fetch recommended events
    const fetchRecommendedEvents = async () => {
      try {
        const res = await fetch(`/api/events/published?limit=3&category=${encodeURIComponent(event?.category || '')}`, {
          cache: 'no-store',
        });

        if (res.ok) {
          const data = await res.json();
          // Filter out the current event
          const filtered = Array.isArray(data)
            ? data.filter(e => e.id !== id).slice(0, 3)
            : [];
          setRecommendedEvents(filtered);
        }
      } catch (err) {
        console.error('Error fetching recommended events:', err);
      }
    };

    if (event?.category) {
      fetchRecommendedEvents();
    }

    // Fetch user's Elite Communication tier
    const fetchEliteTier = async () => {
      if (session?.user?.id && event?.id) {
        try {
          const response = await fetch(`/api/elite-communication/subscription?eventId=${event.id}`);
          const data = await response.json();
          setUserEliteTier(data.tier || EliteTier.BASIC);
        } catch (error) {
          console.error('Error fetching Elite tier:', error);
        }
      }
    };

    if (session?.user?.id && event?.id) {
      fetchEliteTier();
    }
  }, [id, event?.category, initialEvent, session?.user?.id, event?.id]);

  if (loading) {
    return (
      <AppLayout>
        <div className="container mx-auto p-6 space-y-6">
          <div className="h-[60vh] w-full bg-gray-200 animate-pulse rounded-xl"></div>
          <div className="h-10 w-3/4 bg-gray-200 animate-pulse rounded-lg"></div>
          <div className="h-6 w-1/2 bg-gray-200 animate-pulse rounded-lg"></div>
          <div className="h-40 w-full bg-gray-200 animate-pulse rounded-lg"></div>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="min-h-[60vh] flex items-center justify-center">
          <div className="max-w-md p-8 bg-white shadow-lg rounded-xl">
            <div className="text-center">
              <FileText className="mx-auto h-12 w-12 text-red-500 mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Event</h3>
              <p className="text-gray-500">{error}</p>
              <Button
                onClick={() => router.back()}
                className="mt-6 bg-gray-900 hover:bg-gray-800"
              >
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (!event) {
    return (
      <AppLayout>
        <div className="min-h-[60vh] flex items-center justify-center">
          <div className="max-w-md p-8 bg-white shadow-lg rounded-xl text-center">
            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Event Not Found</h3>
            <p className="text-gray-500 mb-6">The event you&apos;re looking for doesn&apos;t exist or has been removed.</p>
            <Button
              onClick={() => router.back()}
              className="bg-gray-900 hover:bg-gray-800"
            >
              Return to Events
            </Button>
          </div>
        </div>
      </AppLayout>
    );
  }

  const formatDate = (date: string | Date) => {
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Get the current URL for meta tags
  const currentUrl = typeof window !== 'undefined' ? window.location.href : '';

  // Format description for display
  const shortDescription = event.description.length > 300
    ? `${event.description.substring(0, 300)}...`
    : event.description;

  const displayDescription = showFullDescription ? event.description : shortDescription;
  const hasLongDescription = event.description.length > 300;

  // Get available ticket types and lowest price
  const availableTickets = event.tickets?.filter(ticket => ticket.isAvailable) || [];
  const lowestPrice = availableTickets.length > 0
    ? Math.min(...availableTickets.map(ticket => ticket.price))
    : 0;

  return (
    <AppLayout>
      {/* Add meta tags for SEO and social sharing with error handling */}
      {event && (
        <EventMetaTags
          title={event.title}
          description={event.description}
          url={currentUrl}
          imageUrl={event.imagePath || ''}
          seoSettings={event.seoSettings}
          socialSettings={event.socialSettings}
        />
      )}

      {/* Hero Section with Event Image */}
      <div className="relative w-full h-[50vh] md:h-[60vh] overflow-hidden">
        {event.imagePath ? (
          <div className="absolute inset-0 bg-black">
            <Image
              src={event.imagePath}
              alt={event.title}
              fill
              className="object-cover opacity-80"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent"></div>
          </div>
        ) : (
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-800"></div>
        )}

        {/* Event Category Badge */}
        <div className="absolute top-6 left-6 z-10">
          <Badge className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 text-sm font-medium rounded-full">
            {event.category}
          </Badge>
        </div>

        {/* Share Button */}
        <div className="absolute top-6 right-6 z-10">
          <Button
            variant="outline"
            size="sm"
            className="bg-white/20 backdrop-blur-sm border-white/30 text-white hover:bg-white/30 rounded-full"
            onClick={() => {
              if (navigator.share) {
                navigator.share({
                  title: event.title,
                  text: shortDescription,
                  url: currentUrl,
                });
              } else {
                navigator.clipboard.writeText(currentUrl);
                alert('Link copied to clipboard!');
              }
            }}
          >
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
        </div>

        {/* Event Title and Basic Info */}
        <div className="absolute bottom-0 left-0 right-0 p-6 md:p-10 text-white z-10">
          <div className="max-w-5xl mx-auto">
            <h1 className="text-3xl md:text-5xl font-bold mb-4">{event.title}</h1>

            <div className="flex flex-wrap gap-4 md:gap-6 text-white/90 mb-6">
              <div className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                <span>{formatDate(event.startDate)}</span>
              </div>
              <div className="flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                <span>{event.startTime} - {event.endTime}</span>
              </div>
              <div className="flex items-center">
                <MapPin className="h-5 w-5 mr-2" />
                <span>{event.venue}, {event.location}</span>
              </div>
            </div>

            <div className="flex items-center">
              <div className="flex items-center mr-6">
                <div className="h-10 w-10 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center mr-3">
                  {event.user.image ? (
                    <Image
                      src={event.user.image}
                      alt={event.user.name || 'Event organizer'}
                      width={40}
                      height={40}
                      className="rounded-full"
                    />
                  ) : (
                    <User className="h-5 w-5 text-white" />
                  )}
                </div>
                <div>
                  <p className="text-sm text-white/70">Organized by</p>
                  <p className="font-medium">{event.user.name}</p>
                </div>
              </div>

              {availableTickets.length > 0 && (
                <div className="ml-auto">
                  <p className="text-sm text-white/70 mb-1">Tickets from</p>
                  <p className="text-xl font-bold">
                    {lowestPrice > 0 ? `$${lowestPrice.toFixed(2)}` : 'Free'}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 py-8 md:py-12 lg:px-8">
          {/* Tab Navigation */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-3 lg:grid-cols-4">
              <TabsTrigger value="overview" className="flex items-center">
                <Info className="h-4 w-4 mr-2" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="tickets" className="flex items-center">
                <Ticket className="h-4 w-4 mr-2" />
                Tickets
              </TabsTrigger>
              {session?.user && (
                <TabsTrigger value="networking" className="flex items-center">
                  <Crown className="h-4 w-4 mr-2" />
                  Elite Networking
                  {userEliteTier !== EliteTier.BASIC && (
                    <Badge className="ml-2 bg-purple-100 text-purple-800 text-xs">
                      {userEliteTier === EliteTier.ELITE ? 'Elite' : 'Pro'}
                    </Badge>
                  )}
                </TabsTrigger>
              )}
              <TabsTrigger value="details" className="flex items-center">
                <FileText className="h-4 w-4 mr-2" />
                Details
              </TabsTrigger>
            </TabsList>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
              {/* Left Column - Tab Content */}
              <div className="lg:col-span-2 space-y-8">
                <TabsContent value="overview" className="space-y-8 mt-0">
              {/* Description Section */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h2 className="text-2xl font-bold mb-4">About This Event</h2>
                <div className="prose max-w-none text-gray-700">
                  <p>{displayDescription}</p>
                  {hasLongDescription && (
                    <button
                      onClick={() => setShowFullDescription(!showFullDescription)}
                      className="text-blue-600 hover:text-blue-800 font-medium mt-2 flex items-center"
                    >
                      {showFullDescription ? 'Show less' : 'Read more'}
                      <ChevronRight className={`h-4 w-4 ml-1 transition-transform ${showFullDescription ? 'rotate-90' : ''}`} />
                    </button>
                  )}
                </div>
              </div>

              {/* Location Section with Map */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h2 className="text-2xl font-bold mb-4">Location</h2>
                <div className="mb-4">
                  <p className="text-gray-700 mb-2">{event.venue}</p>
                  <p className="text-gray-700">{event.location}</p>
                </div>
                <div className="h-[300px] w-full rounded-lg overflow-hidden">
                  <VenueMap location={event.location} venue={event.venue} />
                </div>
              </div>

              {/* Additional Information */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Event Type */}
                  <div className="flex">
                    <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                      <Tag className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 mb-1">Event Type</h3>
                      <p className="text-gray-700">{event.eventType}</p>
                    </div>
                  </div>

                  {/* Event Status */}
                  <div className="flex">
                    <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                      <Info className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 mb-1">Status</h3>
                      <Badge className={`${event.status === 'Published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                        {event.status}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>

              {/* Sponsors Section */}
              {event.sponsors && event.sponsors.length > 0 && (
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h2 className="text-2xl font-bold mb-4">Sponsors</h2>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {event.sponsors.map(sponsor => (
                      <div key={sponsor.id} className="flex flex-col items-center p-4 border border-gray-100 rounded-lg">
                        {sponsor.logo ? (
                          <div className="h-16 w-16 relative mb-3">
                            <Image
                              src={sponsor.logo}
                              alt={sponsor.name}
                              fill
                              className="object-contain"
                            />
                          </div>
                        ) : (
                          <Award className="h-12 w-12 text-gray-400 mb-3" />
                        )}
                        <p className="font-medium text-center">{sponsor.name}</p>
                        <Badge className="mt-2">{sponsor.tier}</Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}

                  {/* Partners Section */}
                  <EventPartnersSection eventId={event.id} />
                </TabsContent>

                {/* Tickets Tab */}
                <TabsContent value="tickets" className="space-y-8 mt-0">
                  <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <h2 className="text-2xl font-bold mb-6">Available Tickets</h2>
                    {availableTickets.length > 0 ? (
                      <div className="space-y-4">
                        {availableTickets.map(ticket => (
                          <div key={ticket.id} className="flex justify-between items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
                            <div>
                              <h3 className="font-semibold text-lg">{ticket.type}</h3>
                              <p className="text-gray-600">{ticket.totalSeats} seats available</p>
                            </div>
                            <div className="text-right">
                              <p className="font-bold text-2xl">
                                {ticket.price > 0 ? `$${ticket.price.toFixed(2)}` : 'Free'}
                              </p>
                            </div>
                          </div>
                        ))}
                        <div className="pt-4">
                          <ButtonTriggeredModal
                            eventId={event.id}
                            eventDetails={{
                              title: event.title,
                              startDate: event.startDate,
                              startTime: event.startTime,
                              endTime: event.endTime,
                              venue: event.venue,
                              imagePath: event.imagePath
                            }}
                          />
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <Ticket className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Tickets Available</h3>
                        <p className="text-gray-600">Tickets are not currently available for this event.</p>
                      </div>
                    )}
                  </div>
                </TabsContent>

                {/* Elite Networking Tab */}
                {session?.user && (
                  <TabsContent value="networking" className="space-y-8 mt-0">
                    <EliteCommunicationTab
                      eventId={event.id}
                      currentUser={{
                        id: session.user.id,
                        name: session.user.name || undefined,
                        image: session.user.image || undefined
                      }}
                      userTier={userEliteTier}
                    />
                  </TabsContent>
                )}

                {/* Details Tab */}
                <TabsContent value="details" className="space-y-8 mt-0">
                  <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <h2 className="text-2xl font-bold mb-6">Event Details</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="flex">
                        <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                          <Tag className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900 mb-1">Event Type</h3>
                          <p className="text-gray-700">{event.eventType}</p>
                        </div>
                      </div>
                      <div className="flex">
                        <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                          <Info className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900 mb-1">Status</h3>
                          <Badge className={`${event.status === 'Published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                            {event.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Age Restrictions */}
                  {event.ageRestriction && (
                    <AgeRestrictionInfo ageRestriction={event.ageRestriction} />
                  )}

                  {/* Parking Information */}
                  {event.ParkingManagement && event.ParkingManagement.length > 0 && (
                    <ParkingInfo parkingInfo={event.ParkingManagement[0]} />
                  )}
                </TabsContent>
              </div>

            {/* Right Column - Ticket Purchase and Related Info */}
            <div className="space-y-6">
              {/* Ticket Purchase Card */}
              <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6 sticky top-24">
                <h2 className="text-xl font-bold mb-4 flex items-center">
                  <Ticket className="h-5 w-5 mr-2 text-blue-600" />
                  Get Tickets
                </h2>

                {availableTickets.length > 0 ? (
                  <div className="space-y-4">
                    <div className="space-y-3">
                      {availableTickets.map(ticket => (
                        <div key={ticket.id} className="flex justify-between items-center p-3 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
                          <div>
                            <p className="font-medium">{ticket.type}</p>
                            <p className="text-sm text-gray-500">{ticket.totalSeats} seats available</p>
                          </div>
                          <p className="font-bold text-lg">
                            {ticket.price > 0 ? `$${ticket.price.toFixed(2)}` : 'Free'}
                          </p>
                        </div>
                      ))}
                    </div>

                    <ButtonTriggeredModal
                      eventId={event.id}
                      eventDetails={{
                        title: event.title,
                        startDate: event.startDate,
                        startTime: event.startTime,
                        endTime: event.endTime,
                        venue: event.venue,
                        imagePath: event.imagePath
                      }}
                    />
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <p className="text-gray-500 mb-4">No tickets are currently available for this event.</p>
                    <Button variant="outline" className="w-full">
                      <Heart className="h-4 w-4 mr-2" />
                      Add to Wishlist
                    </Button>
                  </div>
                )}

                <div className="mt-4 pt-4 border-t border-gray-100">
                  <p className="text-sm text-gray-500 flex items-center">
                    <Info className="h-4 w-4 mr-2 text-gray-400" />
                    Secure checkout powered by QUICK TIME
                  </p>
                </div>
              </div>

              {/* Organizer Info */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h2 className="text-xl font-bold mb-4">About the Organizer</h2>
                <div className="flex items-center mb-4">
                  <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                    {event.user.image ? (
                      <Image
                        src={event.user.image}
                        alt={event.user.name || 'Event organizer'}
                        width={48}
                        height={48}
                        className="rounded-full"
                      />
                    ) : (
                      <User className="h-6 w-6 text-gray-500" />
                    )}
                  </div>
                  <div>
                    <p className="font-medium">{event.user.name}</p>
                    <p className="text-sm text-gray-500">Event Organizer</p>
                  </div>
                </div>
                <Button variant="outline" className="w-full">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Organizer Profile
                </Button>
              </div>

              {/* Recommended Events */}
              {recommendedEvents.length > 0 && (
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h2 className="text-xl font-bold mb-4">You Might Also Like</h2>
                  <div className="space-y-4">
                    {recommendedEvents.map(recEvent => (
                      <Link
                        href={generateEventUrl({ id: recEvent.id, title: recEvent.title, category: recEvent.category })}
                        key={recEvent.id}
                        className="block group"
                      >
                        <div className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                          <div className="relative h-16 w-16 flex-shrink-0 rounded-md overflow-hidden">
                            {recEvent.imagePath ? (
                              <Image
                                src={recEvent.imagePath}
                                alt={recEvent.title}
                                fill
                                className="object-cover"
                              />
                            ) : (
                              <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                <Calendar className="h-6 w-6 text-gray-400" />
                              </div>
                            )}
                          </div>
                          <div>
                            <h3 className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                              {recEvent.title}
                            </h3>
                            <p className="text-sm text-gray-500">
                              {formatDate(recEvent.startDate)}
                            </p>
                          </div>
                        </div>
                      </Link>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </Tabs>
        </div>
      </div>
    </AppLayout>
  );
};

export default EventDetails;
