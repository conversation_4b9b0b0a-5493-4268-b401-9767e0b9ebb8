const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Directories to clean
const dirsToClean = [
  '.next-turbo',
  '.turbo',
  'node_modules/.cache/turbopack',
  'node_modules/.cache/turbo'
];

// Optional directories (only if --full flag is provided)
const fullCleanDirs = [
  '.next'
];

console.log('🧹 Cleaning Turbopack and Next.js cache...');

// Check if --full flag is provided
const isFullClean = process.argv.includes('--full');
if (isFullClean) {
  console.log('⚠️ Full clean mode: Will also clean .next directory');
  dirsToClean.push(...fullCleanDirs);
}

// Clean directories
dirsToClean.forEach(dir => {
  const dirPath = path.join(process.cwd(), dir);
  if (fs.existsSync(dirPath)) {
    console.log(`Removing ${dir}...`);
    try {
      if (process.platform === 'win32') {
        // On Windows, use rimraf for better compatibility
        execSync(`npx rimraf "${dirPath}"`);
      } else {
        // On Unix-like systems, use rm -rf
        execSync(`rm -rf "${dirPath}"`);
      }
      console.log(`✅ Removed ${dir}`);
    } catch (error) {
      console.error(`❌ Failed to remove ${dir}:`, error.message);
    }
  } else {
    console.log(`Directory ${dir} does not exist, skipping.`);
  }
});

console.log('🎉 Clean complete! Ready for a fresh build.');
