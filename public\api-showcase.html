<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Event Platform API - Monetization Showcase</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      padding-top: 2rem;
      padding-bottom: 4rem;
    }
    .header {
      background-color: #f8f9fa;
      padding: 2rem 0;
      margin-bottom: 2rem;
      border-bottom: 1px solid #e9ecef;
    }
    .api-card {
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      transition: all 0.3s ease;
    }
    .api-card:hover {
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      transform: translateY(-2px);
    }
    .tier-badge {
      font-size: 0.8rem;
      padding: 0.3rem 0.6rem;
      border-radius: 4px;
      margin-left: 0.5rem;
    }
    .tier-free {
      background-color: #e9ecef;
      color: #495057;
    }
    .tier-basic {
      background-color: #cff4fc;
      color: #055160;
    }
    .tier-professional {
      background-color: #d1e7dd;
      color: #0f5132;
    }
    .tier-enterprise {
      background-color: #f8d7da;
      color: #842029;
    }
    .method {
      font-weight: bold;
      padding: 0.2rem 0.5rem;
      border-radius: 4px;
      margin-right: 0.5rem;
    }
    .method-get {
      background-color: #d1e7dd;
      color: #0f5132;
    }
    .method-post {
      background-color: #cff4fc;
      color: #055160;
    }
    .endpoint {
      font-family: monospace;
      background-color: #f8f9fa;
      padding: 0.2rem 0.5rem;
      border-radius: 4px;
    }
    .response-container {
      background-color: #f8f9fa;
      border-radius: 8px;
      padding: 1rem;
      max-height: 300px;
      overflow-y: auto;
    }
    .api-key-input {
      font-family: monospace;
    }
    pre {
      background-color: #f8f9fa;
      padding: 1rem;
      border-radius: 4px;
      overflow-x: auto;
    }
    .pricing-card {
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 1.5rem;
      height: 100%;
      transition: all 0.3s ease;
    }
    .pricing-card:hover {
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .pricing-header {
      text-align: center;
      padding-bottom: 1rem;
      border-bottom: 1px solid #e9ecef;
      margin-bottom: 1rem;
    }
    .pricing-price {
      font-size: 2.5rem;
      font-weight: bold;
    }
    .pricing-period {
      font-size: 0.9rem;
      color: #6c757d;
    }
    .feature-list {
      list-style-type: none;
      padding-left: 0;
    }
    .feature-list li {
      padding: 0.5rem 0;
      border-bottom: 1px solid #f8f9fa;
    }
    .feature-list li:last-child {
      border-bottom: none;
    }
    .feature-check {
      color: #198754;
      margin-right: 0.5rem;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="container">
      <h1 class="display-4">Event Platform API</h1>
      <p class="lead">Integrate events data into your applications with our powerful API</p>
    </div>
  </div>

  <div class="container">
    <div class="row mb-5">
      <div class="col-md-8">
        <h2>API Showcase</h2>
        <p>
          Our API provides access to event data, analytics, and ticketing functionality. 
          Use this showcase to explore the available endpoints and see how they work.
        </p>
      </div>
      <div class="col-md-4">
        <div class="mb-3">
          <label for="apiKey" class="form-label">API Key</label>
          <input type="text" class="form-control api-key-input" id="apiKey" placeholder="Enter your API key">
        </div>
      </div>
    </div>

    <div class="row mb-5">
      <div class="col-12">
        <h3>Event Discovery API</h3>
        <p>Access event data with powerful filtering and search capabilities.</p>
      </div>

      <div class="col-md-6">
        <div class="api-card">
          <h4>
            List Events
            <span class="tier-badge tier-free">FREE</span>
          </h4>
          <p>Get a list of published events with pagination and filtering options.</p>
          <div class="d-flex align-items-center mb-3">
            <span class="method method-get">GET</span>
            <span class="endpoint">/api/monetized/events</span>
          </div>
          <div class="mb-3">
            <label class="form-label">Parameters</label>
            <div class="row g-2">
              <div class="col-md-6">
                <input type="number" class="form-control form-control-sm" id="eventsPage" placeholder="page (default: 1)" value="1">
              </div>
              <div class="col-md-6">
                <input type="number" class="form-control form-control-sm" id="eventsLimit" placeholder="limit (default: 10)" value="5">
              </div>
            </div>
          </div>
          <button class="btn btn-primary btn-sm" onclick="fetchEvents()">Try It</button>
          <div class="response-container mt-3" id="eventsResponse">
            <div class="text-center text-muted">Response will appear here</div>
          </div>
        </div>
      </div>

      <div class="col-md-6">
        <div class="api-card">
          <h4>
            Event Details
            <span class="tier-badge tier-free">FREE</span>
          </h4>
          <p>Get detailed information about a specific event.</p>
          <div class="d-flex align-items-center mb-3">
            <span class="method method-get">GET</span>
            <span class="endpoint">/api/monetized/events/{id}</span>
          </div>
          <div class="mb-3">
            <label class="form-label">Parameters</label>
            <input type="text" class="form-control form-control-sm" id="eventId" placeholder="Event ID">
          </div>
          <button class="btn btn-primary btn-sm" onclick="fetchEventDetails()">Try It</button>
          <div class="response-container mt-3" id="eventDetailsResponse">
            <div class="text-center text-muted">Response will appear here</div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mb-5">
      <div class="col-12">
        <h3>Analytics API</h3>
        <p>Access analytics and insights about events and audience demographics.</p>
      </div>

      <div class="col-md-6">
        <div class="api-card">
          <h4>
            Event Trends
            <span class="tier-badge tier-professional">PROFESSIONAL</span>
          </h4>
          <p>Get event popularity trends by category and location.</p>
          <div class="d-flex align-items-center mb-3">
            <span class="method method-get">GET</span>
            <span class="endpoint">/api/monetized/analytics/trends</span>
          </div>
          <div class="mb-3">
            <label class="form-label">Parameters</label>
            <select class="form-select form-select-sm" id="trendsPeriod">
              <option value="day">Day</option>
              <option value="week">Week</option>
              <option value="month" selected>Month</option>
              <option value="year">Year</option>
            </select>
          </div>
          <button class="btn btn-primary btn-sm" onclick="fetchTrends()">Try It</button>
          <div class="response-container mt-3" id="trendsResponse">
            <div class="text-center text-muted">Response will appear here</div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mb-5">
      <div class="col-12">
        <h2>API Documentation</h2>
        <p>
          Our API is fully documented and easy to integrate with. 
          Check out the documentation to learn more about the available endpoints and how to use them.
        </p>
        <div class="d-grid gap-2 d-md-flex justify-content-md-start">
          <button class="btn btn-outline-primary" onclick="fetchApiDocs()">View API Documentation</button>
        </div>
        <div class="response-container mt-3" id="apiDocsResponse" style="display: none;">
          <div class="text-center text-muted">Loading documentation...</div>
        </div>
      </div>
    </div>

    <div class="row mb-5">
      <div class="col-12">
        <h2>Pricing</h2>
        <p>Choose the plan that fits your needs. All plans include access to our API.</p>
      </div>

      <div class="col-md-3">
        <div class="pricing-card">
          <div class="pricing-header">
            <h3>Free</h3>
            <div class="pricing-price">$0</div>
            <div class="pricing-period">per month</div>
          </div>
          <ul class="feature-list">
            <li><span class="feature-check">✓</span> Basic event data</li>
            <li><span class="feature-check">✓</span> 100 requests per day</li>
            <li><span class="feature-check">✓</span> 10 requests per minute</li>
            <li><span class="feature-check">✓</span> Limited search</li>
          </ul>
          <div class="d-grid gap-2 mt-3">
            <button class="btn btn-outline-primary">Get Started</button>
          </div>
        </div>
      </div>

      <div class="col-md-3">
        <div class="pricing-card">
          <div class="pricing-header">
            <h3>Basic</h3>
            <div class="pricing-price">$49</div>
            <div class="pricing-period">per month</div>
          </div>
          <ul class="feature-list">
            <li><span class="feature-check">✓</span> Full event data</li>
            <li><span class="feature-check">✓</span> 5,000 requests per day</li>
            <li><span class="feature-check">✓</span> 60 requests per minute</li>
            <li><span class="feature-check">✓</span> Advanced search</li>
            <li><span class="feature-check">✓</span> Categories and types</li>
          </ul>
          <div class="d-grid gap-2 mt-3">
            <button class="btn btn-primary">Subscribe</button>
          </div>
        </div>
      </div>

      <div class="col-md-3">
        <div class="pricing-card">
          <div class="pricing-header">
            <h3>Professional</h3>
            <div class="pricing-price">$199</div>
            <div class="pricing-period">per month</div>
          </div>
          <ul class="feature-list">
            <li><span class="feature-check">✓</span> Everything in Basic</li>
            <li><span class="feature-check">✓</span> 20,000 requests per day</li>
            <li><span class="feature-check">✓</span> 300 requests per minute</li>
            <li><span class="feature-check">✓</span> Analytics data</li>
            <li><span class="feature-check">✓</span> Ticket availability</li>
          </ul>
          <div class="d-grid gap-2 mt-3">
            <button class="btn btn-primary">Subscribe</button>
          </div>
        </div>
      </div>

      <div class="col-md-3">
        <div class="pricing-card">
          <div class="pricing-header">
            <h3>Enterprise</h3>
            <div class="pricing-price">Custom</div>
            <div class="pricing-period">contact us</div>
          </div>
          <ul class="feature-list">
            <li><span class="feature-check">✓</span> Everything in Professional</li>
            <li><span class="feature-check">✓</span> Unlimited requests per day</li>
            <li><span class="feature-check">✓</span> 1,000 requests per minute</li>
            <li><span class="feature-check">✓</span> Custom endpoints</li>
            <li><span class="feature-check">✓</span> SLA guarantees</li>
            <li><span class="feature-check">✓</span> Dedicated support</li>
          </ul>
          <div class="d-grid gap-2 mt-3">
            <button class="btn btn-outline-primary">Contact Sales</button>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-12">
        <h2>Get Started</h2>
        <p>
          Ready to integrate our API into your application? 
          Sign up for an account to get your API key and start building.
        </p>
        <div class="d-grid gap-2 d-md-flex justify-content-md-start">
          <button class="btn btn-primary">Sign Up</button>
          <button class="btn btn-outline-secondary">Contact Sales</button>
        </div>
      </div>
    </div>
  </div>

  <footer class="bg-light mt-5 py-4">
    <div class="container">
      <div class="row">
        <div class="col-md-6">
          <p>&copy; 2023 Event Platform. All rights reserved.</p>
        </div>
        <div class="col-md-6 text-md-end">
          <a href="#" class="text-decoration-none me-3">Terms of Service</a>
          <a href="#" class="text-decoration-none">Privacy Policy</a>
        </div>
      </div>
    </div>
  </footer>

  <script>
    // Base URL for API requests
    const baseUrl = window.location.origin;

    // Function to get the API key from the input field
    function getApiKey() {
      return document.getElementById('apiKey').value.trim();
    }

    // Function to format JSON response
    function formatResponse(data) {
      return JSON.stringify(data, null, 2);
    }

    // Function to display error message
    function displayError(elementId, message) {
      document.getElementById(elementId).innerHTML = `<div class="alert alert-danger">${message}</div>`;
    }

    // Function to fetch events
    async function fetchEvents() {
      const apiKey = getApiKey();
      const page = document.getElementById('eventsPage').value || 1;
      const limit = document.getElementById('eventsLimit').value || 10;
      const responseElement = document.getElementById('eventsResponse');
      
      responseElement.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
      
      try {
        const response = await fetch(`${baseUrl}/api/monetized/events?page=${page}&limit=${limit}`, {
          headers: {
            'X-API-Key': apiKey
          }
        });
        
        const data = await response.json();
        
        if (response.ok) {
          responseElement.innerHTML = `<pre>${formatResponse(data)}</pre>`;
        } else {
          displayError('eventsResponse', `Error: ${data.error || 'Failed to fetch events'}`);
        }
      } catch (error) {
        displayError('eventsResponse', `Error: ${error.message}`);
      }
    }

    // Function to fetch event details
    async function fetchEventDetails() {
      const apiKey = getApiKey();
      const eventId = document.getElementById('eventId').value.trim();
      const responseElement = document.getElementById('eventDetailsResponse');
      
      if (!eventId) {
        displayError('eventDetailsResponse', 'Error: Event ID is required');
        return;
      }
      
      responseElement.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
      
      try {
        const response = await fetch(`${baseUrl}/api/monetized/events/${eventId}`, {
          headers: {
            'X-API-Key': apiKey
          }
        });
        
        const data = await response.json();
        
        if (response.ok) {
          responseElement.innerHTML = `<pre>${formatResponse(data)}</pre>`;
        } else {
          displayError('eventDetailsResponse', `Error: ${data.error || 'Failed to fetch event details'}`);
        }
      } catch (error) {
        displayError('eventDetailsResponse', `Error: ${error.message}`);
      }
    }

    // Function to fetch trends
    async function fetchTrends() {
      const apiKey = getApiKey();
      const period = document.getElementById('trendsPeriod').value;
      const responseElement = document.getElementById('trendsResponse');
      
      responseElement.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
      
      try {
        const response = await fetch(`${baseUrl}/api/monetized/analytics/trends?period=${period}`, {
          headers: {
            'X-API-Key': apiKey
          }
        });
        
        const data = await response.json();
        
        if (response.ok) {
          responseElement.innerHTML = `<pre>${formatResponse(data)}</pre>`;
        } else {
          displayError('trendsResponse', `Error: ${data.error || 'Failed to fetch trends'}`);
        }
      } catch (error) {
        displayError('trendsResponse', `Error: ${error.message}`);
      }
    }

    // Function to fetch API documentation
    async function fetchApiDocs() {
      const responseElement = document.getElementById('apiDocsResponse');
      responseElement.style.display = 'block';
      responseElement.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
      
      try {
        const response = await fetch(`${baseUrl}/api/monetized/docs`);
        const data = await response.json();
        
        if (response.ok) {
          responseElement.innerHTML = `<pre>${formatResponse(data)}</pre>`;
        } else {
          displayError('apiDocsResponse', `Error: ${data.error || 'Failed to fetch API documentation'}`);
        }
      } catch (error) {
        displayError('apiDocsResponse', `Error: ${error.message}`);
      }
    }

    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
      // You can add initialization code here if needed
    });
  </script>
</body>
</html>
