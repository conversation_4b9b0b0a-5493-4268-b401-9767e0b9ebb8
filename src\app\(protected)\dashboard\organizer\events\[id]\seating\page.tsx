'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Save, Plus, Trash2, ArrowLeft, Grid, Rows, Columns, Check } from 'lucide-react';
import StadiumSeatMap from '@/components/stadium/StadiumSeatMap';
import SectionCreator from '@/components/stadium/SectionCreator';
import EnhancedSectionCreator from '@/components/stadium/EnhancedSectionCreator';
import EnhancedSeatMap from '@/components/stadium/EnhancedSeatMap';

interface Event {
  id: string;
  title: string;
  venue: string;
  startDate: string;
  endDate: string;
  hasStadiumSeating?: boolean;
}

interface Section {
  id: string;
  name: string;
  rows?: number;
  seatsPerRow?: number;
  tables?: number;
  seatsPerTable?: number;
  booths?: number;
  seatsPerBooth?: number;
  category: string;
  price: number;
  color: string;
  layoutType?: 'rows' | 'tables' | 'booths' | 'custom';
}

export default function EventSeatingPage({ params }: { params: Promise<{ id: string }> | { id: string } }) {
  // Unwrap params using React.use() if it's a Promise
  const unwrappedParams = params instanceof Promise ? use(params) : params;
  const router = useRouter();
  const [event, setEvent] = useState<Event | null>(null);
  const [sections, setSections] = useState<Section[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('layout');
  const [error, setError] = useState<string | null>(null);
  const [hasStadiumSeating, setHasStadiumSeating] = useState(false);
  const [venueType, setVenueType] = useState<string>('stadium');

  useEffect(() => {
    async function fetchEventAndSeating() {
      try {
        setLoading(true);
        console.log('Fetching event details for ID:', unwrappedParams.id);

        // Try to fetch from the events API
        const eventResponse = await fetch(`/api/events/${unwrappedParams.id}`);

        if (eventResponse.ok) {
          const eventData = await eventResponse.json();
          console.log('Event data received:', eventData);
          setEvent(eventData);
          setHasStadiumSeating(eventData.hasStadiumSeating || false);
        } else {
          // If that fails, try the dashboard organizer API
          console.log('Failed to fetch from events API, trying dashboard API');
          const dashboardResponse = await fetch(`/api/dashboard/organiser/events/${unwrappedParams.id}`);

          if (dashboardResponse.ok) {
            const dashboardData = await dashboardResponse.json();
            console.log('Event data received from dashboard API:', dashboardData);
            setEvent(dashboardData);
            setHasStadiumSeating(dashboardData.hasStadiumSeating || false);
          } else {
            console.error('Both API calls failed');
            throw new Error('Failed to fetch event details');
          }
        }

        // Fetch seating sections
        const sectionsResponse = await fetch(`/api/events/${unwrappedParams.id}/seating`);

        if (sectionsResponse.ok) {
          const sectionsData = await sectionsResponse.json();
          setSections(sectionsData);
        }

      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
        toast({
          title: 'Error',
          description: 'Failed to load event seating data',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }

    fetchEventAndSeating();
  }, [unwrappedParams.id]);

  const handleAddSection = (newSection: any) => {
    // Support both old and new section formats
    const sectionWithId = {
      ...newSection,
      id: `section-${Date.now()}`, // Temporary ID until saved to the database
    };

    setSections([...sections, sectionWithId]);

    toast({
      title: 'Section added',
      description: `${newSection.name} section has been added to the layout`,
    });
  };

  // Toggle stadium seating
  const handleToggleSeating = async (checked: boolean) => {
    try {
      setLoading(true);

      const response = await fetch(`/api/events/${unwrappedParams.id}/seating/toggle`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ hasStadiumSeating: checked }),
      });

      if (response.ok) {
        setHasStadiumSeating(checked);

        toast({
          title: checked ? 'Stadium Seating Enabled' : 'Stadium Seating Disabled',
          description: checked ? 'You can now create enhanced seating layouts' : 'Stadium seating has been disabled',
        });

        if (checked) {
          // Fetch sections if enabling stadium seating
          const sectionsResponse = await fetch(`/api/events/${unwrappedParams.id}/seating`);
          if (sectionsResponse.ok) {
            const sectionsData = await sectionsResponse.json();
            setSections(sectionsData);
          }
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to toggle stadium seating');
      }
    } catch (err) {
      console.error('Error toggling stadium seating:', err);
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to toggle stadium seating',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteSection = (sectionId: string) => {
    setSections(sections.filter(section => section.id !== sectionId));

    toast({
      title: 'Section removed',
      description: 'The section has been removed from the layout',
    });
  };

  const handleSaveSeating = async () => {
    try {
      setSaving(true);

      // Calculate total seats
      const totalSeats = sections.reduce((total, section) => {
        return total + (section.rows * section.seatsPerRow);
      }, 0);

      // Confirm if there are a lot of seats (might take time to generate)
      if (totalSeats > 1000) {
        const confirm = window.confirm(`You're about to create ${totalSeats} seats. This might take a moment. Continue?`);
        if (!confirm) {
          setSaving(false);
          return;
        }
      }

      // Save the seating layout
      const response = await fetch(`/api/events/${unwrappedParams.id}/seating`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sections }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save seating layout');
      }

      toast({
        title: 'Success',
        description: 'Seating layout saved successfully',
      });

      // Refresh the sections data
      const refreshResponse = await fetch(`/api/events/${unwrappedParams.id}/seating`);
      if (refreshResponse.ok) {
        const refreshedData = await refreshResponse.json();
        setSections(refreshedData);
      }

    } catch (err) {
      console.error('Error saving seating layout:', err);
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to save seating layout',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading seating data...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8 text-red-500">
              <p>{error}</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => router.back()}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Go Back
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Button>
            <h1 className="text-2xl font-bold">{event?.title} - Venue Seating</h1>
          </div>
          <p className="text-gray-500 mt-1">
            {event?.venue} | {new Date(event?.startDate || '').toLocaleDateString()}
          </p>
        </div>
        <Button
          onClick={handleSaveSeating}
          disabled={saving || sections.length === 0}
        >
          {saving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Seating Layout
            </>
          )}
        </Button>
      </div>

      <div className="mb-6 space-y-4">
        <div className="flex items-center space-x-2">
          <Label htmlFor="stadium-seating">Enable Enhanced Seating</Label>
          <Switch
            id="stadium-seating"
            checked={hasStadiumSeating}
            onCheckedChange={handleToggleSeating}
            disabled={loading}
          />
        </div>

        {hasStadiumSeating && (
          <div className="flex items-center space-x-4">
            <Label htmlFor="venue-type">Venue Type:</Label>
            <Select
              value={venueType}
              onValueChange={(value) => setVenueType(value)}
            >
              <SelectTrigger className="w-[200px]" id="venue-type">
                <SelectValue placeholder="Select venue type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="stadium">Stadium</SelectItem>
                <SelectItem value="theater">Theater</SelectItem>
                <SelectItem value="conference">Conference Hall</SelectItem>
                <SelectItem value="dining">Dining Venue</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="layout">
            <Grid className="h-4 w-4 mr-2" />
            Seating Layout
          </TabsTrigger>
          <TabsTrigger value="sections">
            <Rows className="h-4 w-4 mr-2" />
            Manage Sections
          </TabsTrigger>
          <TabsTrigger value="preview">
            <Check className="h-4 w-4 mr-2" />
            Preview
          </TabsTrigger>
          {hasStadiumSeating && (
            <TabsTrigger value="enhanced">
              <Check className="h-4 w-4 mr-2" />
              Enhanced {venueType === 'stadium' ? 'Stadium' : venueType === 'theater' ? 'Theater' : venueType === 'conference' ? 'Conference' : venueType === 'dining' ? 'Dining' : 'Venue'} Layout
            </TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="layout">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>{venueType === 'stadium' ? 'Stadium' : venueType === 'theater' ? 'Theater' : venueType === 'conference' ? 'Conference Hall' : venueType === 'dining' ? 'Dining Venue' : 'Venue'} Layout</CardTitle>
                  <CardDescription>
                    Visual representation of your {venueType} seating arrangement
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <StadiumSeatMap
                    sections={sections}
                    eventId={unwrappedParams.id}
                    venueType={venueType}
                  />
                </CardContent>
              </Card>
            </div>

            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Seating Summary</CardTitle>
                  <CardDescription>
                    Overview of your seating configuration
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {sections.length === 0 ? (
                    <div className="text-center py-6 text-gray-500">
                      <p>No seating sections defined yet</p>
                      <p className="text-sm mt-2">Add sections in the "Manage Sections" tab</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {sections.map(section => {
                        const totalSeats = section.rows * section.seatsPerRow;
                        return (
                          <div
                            key={section.id}
                            className="p-4 rounded-md border"
                            style={{ borderLeftColor: section.color, borderLeftWidth: '4px' }}
                          >
                            <div className="flex justify-between items-start">
                              <div>
                                <h3 className="font-medium">{section.name}</h3>
                                <p className="text-sm text-gray-500">
                                  {section.category} - K{section.price.toLocaleString()}
                                </p>
                              </div>
                              <div className="text-right">
                                <p className="font-medium">{totalSeats} seats</p>
                                <p className="text-sm text-gray-500">
                                  {section.rows} rows × {section.seatsPerRow} seats
                                </p>
                              </div>
                            </div>
                          </div>
                        );
                      })}

                      <div className="pt-4 border-t mt-4">
                        <div className="flex justify-between font-medium">
                          <span>Total Sections:</span>
                          <span>{sections.length}</span>
                        </div>
                        <div className="flex justify-between font-medium mt-2">
                          <span>Total Seats:</span>
                          <span>
                            {sections.reduce((total, section) => {
                              return total + (section.rows * section.seatsPerRow);
                            }, 0)}
                          </span>
                        </div>
                        <div className="flex justify-between font-medium mt-2 text-green-600">
                          <span>Potential Revenue:</span>
                          <span>
                            K{sections.reduce((total, section) => {
                              return total + (section.rows * section.seatsPerRow * section.price);
                            }, 0).toLocaleString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="sections">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Seating Sections</CardTitle>
                  <CardDescription>
                    Define and manage your venue's seating sections
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {sections.length === 0 ? (
                    <div className="text-center py-12 border-2 border-dashed rounded-md">
                      <p className="text-gray-500 mb-4">No seating sections defined yet</p>
                      <Button onClick={() => document.getElementById('add-section-form')?.scrollIntoView({ behavior: 'smooth' })}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Your First Section
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {sections.map(section => (
                        <div
                          key={section.id}
                          className="p-4 rounded-md border flex justify-between items-center"
                          style={{ borderLeftColor: section.color, borderLeftWidth: '4px' }}
                        >
                          <div>
                            <h3 className="font-medium">{section.name}</h3>
                            <div className="flex gap-6 mt-1 text-sm text-gray-600">
                              <span>{section.category}</span>
                              <span>K{section.price.toLocaleString()}</span>
                              <span>{section.rows} rows</span>
                              <span>{section.seatsPerRow} seats/row</span>
                              <span>{section.rows * section.seatsPerRow} total seats</span>
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteSection(section.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            <div id="add-section-form">
              <SectionCreator
                onAddSection={handleAddSection}
                eventId={unwrappedParams.id}
                venueType={venueType}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="preview">
          <Card>
            <CardHeader>
              <CardTitle>Seating Preview</CardTitle>
              <CardDescription>
                Preview how your seating layout will appear to customers
              </CardDescription>
            </CardHeader>
            <CardContent>
              {sections.length === 0 ? (
                <div className="text-center py-12 border-2 border-dashed rounded-md">
                  <p className="text-gray-500 mb-4">No seating sections defined yet</p>
                  <Button onClick={() => setActiveTab('sections')}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Sections First
                  </Button>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <div className="bg-gray-100 p-8 rounded-lg w-full max-w-3xl mx-auto">
                    <div className="bg-gray-300 h-12 w-3/4 mx-auto mb-12 rounded-t-lg flex items-center justify-center text-gray-700 font-medium">
                      STAGE
                    </div>

                    <StadiumSeatMap
                      sections={sections}
                      eventId={unwrappedParams.id}
                      venueType={venueType}
                      isPreview={true}
                    />

                    <div className="mt-8 flex flex-wrap gap-4 justify-center">
                      {sections.map(section => (
                        <div key={section.id} className="flex items-center gap-2">
                          <div
                            className="w-4 h-4 rounded-full"
                            style={{ backgroundColor: section.color }}
                          ></div>
                          <span>{section.name} - K{section.price.toLocaleString()}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="mt-8 text-center">
                    <p className="text-gray-500 mb-4">This is how customers will see your seating layout during checkout</p>
                    <Button onClick={() => setActiveTab('layout')}>
                      Return to Layout Editor
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {hasStadiumSeating && (
          <TabsContent value="enhanced">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Enhanced {venueType === 'stadium' ? 'Stadium' : venueType === 'theater' ? 'Theater' : venueType === 'conference' ? 'Conference Hall' : venueType === 'dining' ? 'Dining Venue' : 'Venue'} Layout</CardTitle>
                    <CardDescription>
                      Create advanced seating layouts for your {venueType} with tables, booths, and accessible seating
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <EnhancedSeatMap
                      eventId={unwrappedParams.id}
                      sections={sections}
                      venueType={venueType}
                      isPreview={false}
                    />
                  </CardContent>
                </Card>
              </div>

              <div>
                <EnhancedSectionCreator
                  onAddSection={handleAddSection}
                  eventId={unwrappedParams.id}
                  venueType={venueType}
                />

                <Card className="mt-6">
                  <CardHeader>
                    <CardTitle>Layout Options</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="p-4 border rounded-md">
                        <h3 className="font-medium">Row-based Seating</h3>
                        <p className="text-sm text-gray-500 mt-1">
                          {venueType === 'stadium' ? 'Traditional stadium seating with rows and numbered seats' :
                           venueType === 'theater' ? 'Theater-style seating with rows and numbered seats' :
                           venueType === 'conference' ? 'Conference hall seating with rows and numbered seats' :
                           venueType === 'dining' ? 'Dining area with row-based seating arrangement' :
                           'Traditional seating with rows and numbered seats'}
                        </p>
                      </div>

                      {(venueType === 'dining' || venueType === 'conference') && (
                        <div className="p-4 border rounded-md">
                          <h3 className="font-medium">Table Seating</h3>
                          <p className="text-sm text-gray-500 mt-1">
                            {venueType === 'dining' ? 'Dining tables with seats arranged in a circle' :
                             venueType === 'conference' ? 'Conference tables with seats for meetings' :
                             'Round tables with seats arranged in a circle'}
                          </p>
                        </div>
                      )}

                      {(venueType === 'dining' || venueType === 'theater' || venueType === 'other') && (
                        <div className="p-4 border rounded-md">
                          <h3 className="font-medium">Booth Seating</h3>
                          <p className="text-sm text-gray-500 mt-1">
                            {venueType === 'dining' ? 'Private dining booths for premium experiences' :
                             venueType === 'theater' ? 'Private theater boxes for premium experiences' :
                             'Private booths for premium experiences'}
                          </p>
                        </div>
                      )}

                      {venueType === 'conference' && (
                        <div className="p-4 border rounded-md">
                          <h3 className="font-medium">Lounge Seating</h3>
                          <p className="text-sm text-gray-500 mt-1">
                            Comfortable lounge areas for networking and breaks
                          </p>
                        </div>
                      )}

                      <div className="p-4 border rounded-md">
                        <h3 className="font-medium">Accessible Seating</h3>
                        <p className="text-sm text-gray-500 mt-1">
                          Designated areas for wheelchair access and accessibility needs
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
