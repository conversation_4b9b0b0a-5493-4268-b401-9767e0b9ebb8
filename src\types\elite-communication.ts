import { 
  EliteTier, 
  EliteSubscriptionType, 
  AttendeePrivacyLevel, 
  AttendeeMessageLevel,
  MessageType,
  ChatRoomType,
  MeetingType,
  MeetingStatus,
  ReportType,
  ReportStatus
} from '@prisma/client';

// Elite Communication Subscription Types
export interface EliteCommunication {
  id: string;
  userId: string;
  eventId: string;
  tier: EliteTier;
  subscriptionType: EliteSubscriptionType;
  isActive: boolean;
  expiresAt?: Date;
  purchasePrice?: number;
  stripeSubscriptionId?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Attendee Profile Types
export interface AttendeeProfile {
  id: string;
  userId: string;
  eventId: string;
  displayName?: string;
  bio?: string;
  company?: string;
  role?: string;
  industry?: string;
  interests: string[];
  networkingGoals?: string;
  profilePhoto?: string;
  linkedinUrl?: string;
  twitterUrl?: string;
  websiteUrl?: string;
  isDiscoverable: boolean;
  privacyLevel: AttendeePrivacyLevel;
  allowMessages: AttendeeMessageLevel;
  allowMeetings: boolean;
  timezone?: string;
  availableHours?: any; // JSON object for availability schedule
  createdAt: Date;
  updatedAt: Date;
  user?: {
    id: string;
    name?: string;
    image?: string;
    email?: string;
  };
}

// Message Types
export interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  eventId: string;
  chatRoomId?: string;
  content: string;
  messageType: MessageType;
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  isRead: boolean;
  readAt?: Date;
  isEdited: boolean;
  editedAt?: Date;
  isDeleted: boolean;
  deletedAt?: Date;
  replyToId?: string;
  reactions?: any; // JSON object for emoji reactions
  createdAt: Date;
  updatedAt: Date;
  sender?: AttendeeProfile;
  receiver?: AttendeeProfile;
  replyTo?: Message;
  replies?: Message[];
}

// Chat Room Types
export interface ChatRoom {
  id: string;
  eventId: string;
  name: string;
  description?: string;
  roomType: ChatRoomType;
  isActive: boolean;
  maxMembers?: number;
  createdById: string;
  createdAt: Date;
  updatedAt: Date;
  members?: ChatRoomMember[];
  messages?: Message[];
  memberCount?: number;
}

export interface ChatRoomMember {
  id: string;
  chatRoomId: string;
  userId: string;
  joinedAt: Date;
  lastReadAt?: Date;
  isModerator: boolean;
  isMuted: boolean;
  user?: {
    id: string;
    name?: string;
    image?: string;
  };
}

// Meeting Request Types
export interface MeetingRequest {
  id: string;
  senderId: string;
  receiverId: string;
  eventId: string;
  title: string;
  description?: string;
  proposedStartTime: Date;
  proposedEndTime: Date;
  timezone: string;
  meetingType: MeetingType;
  meetingUrl?: string;
  location?: string;
  status: MeetingStatus;
  responseMessage?: string;
  respondedAt?: Date;
  reminderSent: boolean;
  createdAt: Date;
  updatedAt: Date;
  sender?: AttendeeProfile;
  receiver?: AttendeeProfile;
}

// Block and Report Types
export interface AttendeeBlock {
  id: string;
  blockerId: string;
  blockedUserId: string;
  eventId: string;
  reason?: string;
  createdAt: Date;
}

export interface CommunicationReport {
  id: string;
  reporterId: string;
  reportedUserId: string;
  eventId: string;
  messageId?: string;
  reportType: ReportType;
  reason: string;
  description?: string;
  status: ReportStatus;
  reviewedById?: string;
  reviewedAt?: Date;
  actionTaken?: string;
  createdAt: Date;
  updatedAt: Date;
}

// API Response Types
export interface EliteCommunicationResponse {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}

export interface AttendeeDirectoryFilters {
  search?: string;
  industry?: string;
  company?: string;
  role?: string;
  interests?: string[];
  page?: number;
  limit?: number;
}

export interface AttendeeDirectoryResponse {
  attendees: AttendeeProfile[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Pricing Configuration
export interface ElitePricingTier {
  tier: EliteTier;
  name: string;
  description: string;
  features: string[];
  pricePerEvent?: number;
  monthlyPrice?: number;
  annualPrice?: number;
  popular?: boolean;
}

// WebSocket Message Types
export interface WebSocketMessage {
  type: 'message' | 'typing' | 'read_receipt' | 'user_status' | 'meeting_request';
  data: any;
  eventId: string;
  userId: string;
  timestamp: Date;
}

// Message Types
export interface Message {
  id: string;
  senderId: string;
  recipientId?: string;
  eventId: string;
  chatRoomId?: string;
  content: string;
  messageType: MessageType;
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  readAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  sender?: {
    id: string;
    name?: string;
    image?: string;
  };
  recipient?: {
    id: string;
    name?: string;
    image?: string;
  };
}

// Component Props Types
export interface EliteCommunicationTabProps {
  eventId: string;
  currentUser: {
    id: string;
    name?: string;
    image?: string;
  };
  userTier: EliteTier;
}

export interface AttendeeDirectoryProps {
  eventId: string;
  currentUser: {
    id: string;
    name?: string;
    image?: string;
  };
  userTier: EliteTier;
  onStartDirectMessage?: (recipientId: string) => void;
}

export interface ChatInterfaceProps {
  eventId: string;
  currentUser: {
    id: string;
    name?: string;
    image?: string;
  };
  recipientId?: string;
  chatRoomId?: string;
}

export interface MeetingSchedulerProps {
  eventId: string;
  currentUser: {
    id: string;
    name?: string;
    image?: string;
  };
  recipientId: string;
}

// Availability Schedule Types
export interface AvailabilitySlot {
  day: string; // 'monday', 'tuesday', etc.
  startTime: string; // '09:00'
  endTime: string; // '17:00'
  timezone: string;
}

export interface AvailabilitySchedule {
  [key: string]: AvailabilitySlot[];
}
