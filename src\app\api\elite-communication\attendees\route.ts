import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/prisma';
import { EliteTier, AttendeePrivacyLevel } from '@prisma/client';
import { hasFeature } from '@/config/elite-pricing';

export const dynamic = 'force-dynamic';

/**
 * GET /api/elite-communication/attendees?eventId=[eventId]&search=[search]&industry=[industry]&page=[page]&limit=[limit]
 * Get attendee directory for an event
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const eventId = searchParams.get('eventId');
    const search = searchParams.get('search') || '';
    const industry = searchParams.get('industry') || '';
    const company = searchParams.get('company') || '';
    const role = searchParams.get('role') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    if (!eventId) {
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 });
    }

    // Check user's Elite Communication tier
    const userSubscription = await db.eliteCommunication.findUnique({
      where: {
        userId_eventId: {
          userId: session.user.id,
          eventId: eventId
        }
      }
    });

    const userTier = userSubscription?.tier || EliteTier.BASIC;

    // Check if user has access to view attendee list
    if (!hasFeature(userTier, 'viewAttendeeList')) {
      return NextResponse.json({ error: 'Upgrade to Elite to view attendee list' }, { status: 403 });
    }

    // Build where clause based on user's tier and privacy levels
    const whereClause: any = {
      eventId,
      isDiscoverable: true,
      userId: {
        not: session.user.id // Exclude current user
      }
    };

    // Apply privacy filtering based on user's tier
    if (userTier === EliteTier.BASIC) {
      whereClause.privacyLevel = AttendeePrivacyLevel.PUBLIC;
    } else if (userTier === EliteTier.ELITE) {
      whereClause.privacyLevel = {
        in: [AttendeePrivacyLevel.PUBLIC, AttendeePrivacyLevel.ELITE_ONLY]
      };
    }
    // ELITE_PRO can see all privacy levels except HIDDEN

    // Apply search filters
    if (search) {
      whereClause.OR = [
        { displayName: { contains: search, mode: 'insensitive' } },
        { bio: { contains: search, mode: 'insensitive' } },
        { company: { contains: search, mode: 'insensitive' } },
        { role: { contains: search, mode: 'insensitive' } },
        { interests: { hasSome: [search] } }
      ];
    }

    if (industry) {
      whereClause.industry = { contains: industry, mode: 'insensitive' };
    }

    if (company) {
      whereClause.company = { contains: company, mode: 'insensitive' };
    }

    if (role) {
      whereClause.role = { contains: role, mode: 'insensitive' };
    }

    // Get total count
    const total = await db.attendeeProfile.count({ where: whereClause });

    // Get attendees with pagination
    const attendees = await db.attendeeProfile.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
            email: hasFeature(userTier, 'accessContactInfo')
          }
        }
      },
      orderBy: [
        { updatedAt: 'desc' },
        { createdAt: 'desc' }
      ],
      skip: (page - 1) * limit,
      take: limit
    });

    // Filter out sensitive information based on user's tier
    const filteredAttendees = attendees.map(attendee => {
      const filtered: any = {
        id: attendee.id,
        userId: attendee.userId,
        displayName: attendee.displayName,
        bio: attendee.bio,
        company: attendee.company,
        role: attendee.role,
        industry: attendee.industry,
        interests: attendee.interests,
        profilePhoto: attendee.profilePhoto,
        user: {
          id: attendee.user.id,
          name: attendee.user.name,
          image: attendee.user.image
        }
      };

      // Add contact information for Elite+ users
      if (hasFeature(userTier, 'accessContactInfo')) {
        filtered.linkedinUrl = attendee.linkedinUrl;
        filtered.twitterUrl = attendee.twitterUrl;
        filtered.websiteUrl = attendee.websiteUrl;
        filtered.user.email = attendee.user.email;
      }

      return filtered;
    });

    return NextResponse.json({
      attendees: filteredAttendees,
      total,
      page,
      limit,
      hasMore: page * limit < total,
      userTier
    });

  } catch (error) {
    console.error('Error fetching attendees:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/elite-communication/attendees
 * Create or update attendee profile
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      eventId,
      displayName,
      bio,
      company,
      role,
      industry,
      interests,
      networkingGoals,
      linkedinUrl,
      twitterUrl,
      websiteUrl,
      isDiscoverable,
      privacyLevel,
      allowMessages,
      allowMeetings,
      timezone,
      availableHours
    } = body;

    if (!eventId) {
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 });
    }

    // Validate event exists
    const event = await db.event.findUnique({
      where: { id: eventId },
      select: { id: true }
    });

    if (!event) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 });
    }

    // Create or update attendee profile
    const profile = await db.attendeeProfile.upsert({
      where: {
        userId_eventId: {
          userId: session.user.id,
          eventId: eventId
        }
      },
      update: {
        displayName,
        bio,
        company,
        role,
        industry,
        interests: interests || [],
        networkingGoals,
        linkedinUrl,
        twitterUrl,
        websiteUrl,
        isDiscoverable: isDiscoverable ?? false,
        privacyLevel: privacyLevel || 'ELITE_ONLY',
        allowMessages: allowMessages || 'ELITE_ONLY',
        allowMeetings: allowMeetings ?? true,
        timezone,
        availableHours,
        updatedAt: new Date()
      },
      create: {
        userId: session.user.id,
        eventId,
        displayName,
        bio,
        company,
        role,
        industry,
        interests: interests || [],
        networkingGoals,
        linkedinUrl,
        twitterUrl,
        websiteUrl,
        isDiscoverable: isDiscoverable ?? false,
        privacyLevel: privacyLevel || 'ELITE_ONLY',
        allowMessages: allowMessages || 'ELITE_ONLY',
        allowMeetings: allowMeetings ?? true,
        timezone,
        availableHours
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      profile,
      message: 'Profile updated successfully'
    });

  } catch (error) {
    console.error('Error updating attendee profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
