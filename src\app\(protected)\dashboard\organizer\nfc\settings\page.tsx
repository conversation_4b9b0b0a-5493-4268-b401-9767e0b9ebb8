'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Settings,
  Shield,
  WifiOff,
  Zap,
  Save,
  RefreshCw,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

// Define interface for NFC settings
interface NFCSettings {
  id?: string;
  systemName: string;
  currencySymbol: string;
  defaultLanguage: string;
  maxTransactionAmount: number;
  requirePinForHighValue: boolean;
  highValueThreshold: number | null;
  cardLockoutThreshold: number;
  offlineModeEnabled: boolean;
  maxOfflineTransactions: number | null;
  offlineTransactionLimit: number | null;
  syncInterval: number | null;
  receiptEnabled: boolean;
  analyticsEnabled: boolean;
  eventId: string;
  createdAt?: string;
  updatedAt?: string;
}

interface Event {
  id: string;
  title: string;
}

export default function NFCSettingsPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [events, setEvents] = useState<Event[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<string>('');

  // General settings
  const [systemName, setSystemName] = useState('Event NFC Payment System');
  const [currencySymbol, setCurrencySymbol] = useState('K');
  const [defaultLanguage, setDefaultLanguage] = useState('en');
  const [receiptEnabled, setReceiptEnabled] = useState(true);
  const [analyticsEnabled, setAnalyticsEnabled] = useState(true);

  // Security settings
  const [maxTransactionAmount, setMaxTransactionAmount] = useState(500);
  const [requirePinForHighValue, setRequirePinForHighValue] = useState(true);
  const [highValueThreshold, setHighValueThreshold] = useState<number | null>(100);
  const [cardLockoutThreshold, setCardLockoutThreshold] = useState(3);

  // Offline mode settings
  const [offlineModeEnabled, setOfflineModeEnabled] = useState(true);
  const [maxOfflineTransactions, setMaxOfflineTransactions] = useState<number | null>(50);
  const [offlineTransactionLimit, setOfflineTransactionLimit] = useState<number | null>(50);
  const [syncInterval, setSyncInterval] = useState<number | null>(15);

  // Performance settings
  const [cacheTimeout, setCacheTimeout] = useState(30);
  const [batchSize, setBatchSize] = useState(100);
  const [logLevel, setLogLevel] = useState('info');

  // Fetch organizer's events
  useEffect(() => {
    const fetchEvents = async () => {
      try {
        const response = await fetch('/api/organizer/events');
        if (!response.ok) {
          throw new Error('Failed to fetch events');
        }

        const data = await response.json();
        setEvents(data.events || []);

        // If there are events, select the first one by default
        if (data.events && data.events.length > 0) {
          setSelectedEvent(data.events[0].id);
          fetchSettings(data.events[0].id);
        } else {
          setIsLoading(false);
        }
      } catch (error) {
        console.error('Error fetching events:', error);
        setError('Failed to load events. Please try again.');
        setIsLoading(false);
      }
    };

    fetchEvents();
  }, []);

  // Fetch settings for the selected event
  const fetchSettings = async (eventId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/organizer/nfc/settings?eventId=${eventId}`);

      if (!response.ok) {
        throw new Error('Failed to fetch settings');
      }

      const settings: NFCSettings = await response.json();

      // Update state with fetched settings
      setSystemName(settings.systemName);
      setCurrencySymbol(settings.currencySymbol);
      setDefaultLanguage(settings.defaultLanguage);
      setReceiptEnabled(settings.receiptEnabled);
      setAnalyticsEnabled(settings.analyticsEnabled);

      setMaxTransactionAmount(settings.maxTransactionAmount);
      setRequirePinForHighValue(settings.requirePinForHighValue);
      setHighValueThreshold(settings.highValueThreshold);
      setCardLockoutThreshold(settings.cardLockoutThreshold);

      setOfflineModeEnabled(settings.offlineModeEnabled);
      setMaxOfflineTransactions(settings.maxOfflineTransactions);
      setOfflineTransactionLimit(settings.offlineTransactionLimit);
      setSyncInterval(settings.syncInterval);

    } catch (error) {
      console.error('Error fetching settings:', error);
      setError('Failed to load settings. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle event change
  const handleEventChange = (eventId: string) => {
    setSelectedEvent(eventId);
    fetchSettings(eventId);
  };

  // Save settings
  const saveSettings = async () => {
    if (!selectedEvent) {
      toast({
        title: 'Error',
        description: 'Please select an event first.',
        variant: 'destructive',
      });
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      const settings: NFCSettings = {
        systemName,
        currencySymbol,
        defaultLanguage,
        maxTransactionAmount,
        requirePinForHighValue,
        highValueThreshold,
        cardLockoutThreshold,
        offlineModeEnabled,
        maxOfflineTransactions,
        offlineTransactionLimit,
        syncInterval,
        receiptEnabled,
        analyticsEnabled,
        eventId: selectedEvent
      };

      const response = await fetch('/api/organizer/nfc/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save settings');
      }

      toast({
        title: 'Settings Saved',
        description: 'Your NFC system settings have been updated.',
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      setError('Failed to save settings. Please try again.');

      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save settings',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
          <span>Loading settings...</span>
        </div>
      ) : (
        <>
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold">NFC System Settings</h1>
              <p className="text-gray-600 mt-1">
                Configure your event's NFC payment system
              </p>
            </div>

            <div className="flex flex-col md:flex-row gap-4 w-full md:w-auto">
              <Select value={selectedEvent} onValueChange={handleEventChange} disabled={events.length === 0 || isSaving}>
                <SelectTrigger className="w-full md:w-[250px]">
                  <SelectValue placeholder="Select event" />
                </SelectTrigger>
                <SelectContent>
                  {events.length > 0 ? (
                    events.map(event => (
                      <SelectItem key={event.id} value={event.id}>
                        {event.title}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="no_events" disabled>
                      No events available
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>

              <Button onClick={saveSettings} disabled={isSaving || !selectedEvent}>
                {isSaving ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Settings
                  </>
                )}
              </Button>
            </div>
          </div>
        </>
      )}

      {!isLoading && (
        <Tabs defaultValue="general" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="general" disabled={!selectedEvent}>
              <Settings className="mr-2 h-4 w-4" />
              General
            </TabsTrigger>
            <TabsTrigger value="security" disabled={!selectedEvent}>
              <Shield className="mr-2 h-4 w-4" />
              Security
            </TabsTrigger>
            <TabsTrigger value="offline" disabled={!selectedEvent}>
              <WifiOff className="mr-2 h-4 w-4" />
              Offline Mode
            </TabsTrigger>
            <TabsTrigger value="performance" disabled={!selectedEvent}>
              <Zap className="mr-2 h-4 w-4" />
              Performance
            </TabsTrigger>
          </TabsList>

          <TabsContent value="general">
            <Card>
              <CardHeader>
                <CardTitle>General Settings</CardTitle>
                <CardDescription>
                  Configure basic settings for your NFC payment system
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="systemName">System Name</Label>
                  <Input
                    id="systemName"
                    value={systemName}
                    onChange={(e) => setSystemName(e.target.value)}
                    disabled={isSaving}
                  />
                  <p className="text-sm text-gray-500">
                    This name will appear on receipts and in the terminal interface
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="currencySymbol">Currency Symbol</Label>
                  <Input
                    id="currencySymbol"
                    value={currencySymbol}
                    onChange={(e) => setCurrencySymbol(e.target.value)}
                    className="w-20"
                    disabled={isSaving}
                  />
                  <p className="text-sm text-gray-500">
                    Symbol to display before amounts (e.g., $, €, £)
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="defaultLanguage">Default Language</Label>
                  <Select
                    value={defaultLanguage}
                    onValueChange={setDefaultLanguage}
                    disabled={isSaving}
                  >
                    <SelectTrigger id="defaultLanguage" className="w-full max-w-xs">
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="fr">French</SelectItem>
                      <SelectItem value="es">Spanish</SelectItem>
                      <SelectItem value="de">German</SelectItem>
                      <SelectItem value="zh">Chinese</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-gray-500">
                    Default language for the terminal interface
                  </p>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="receiptEnabled">Enable Digital Receipts</Label>
                    <p className="text-sm text-gray-500">
                      Send digital receipts to customers via email
                    </p>
                  </div>
                  <Switch
                    id="receiptEnabled"
                    checked={receiptEnabled}
                    onCheckedChange={setReceiptEnabled}
                    disabled={isSaving}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="analyticsEnabled">Enable Analytics</Label>
                    <p className="text-sm text-gray-500">
                      Collect and analyze transaction data
                    </p>
                  </div>
                  <Switch
                    id="analyticsEnabled"
                    checked={analyticsEnabled}
                    onCheckedChange={setAnalyticsEnabled}
                    disabled={isSaving}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security">
            <Card>
              <CardHeader>
                <CardTitle>Security Settings</CardTitle>
                <CardDescription>
                  Configure security settings for your NFC payment system
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="maxTransactionAmount">Maximum Transaction Amount ({currencySymbol})</Label>
                  <Input
                    id="maxTransactionAmount"
                    type="number"
                    value={maxTransactionAmount}
                    onChange={(e) => setMaxTransactionAmount(Number(e.target.value))}
                    className="w-full max-w-xs"
                    disabled={isSaving}
                  />
                  <p className="text-sm text-gray-500">
                    Maximum amount allowed for a single transaction
                  </p>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="requirePinForHighValue">Require PIN for High-Value Transactions</Label>
                    <p className="text-sm text-gray-500">
                      Require a PIN for transactions above the threshold
                    </p>
                  </div>
                  <Switch
                    id="requirePinForHighValue"
                    checked={requirePinForHighValue}
                    onCheckedChange={setRequirePinForHighValue}
                    disabled={isSaving}
                  />
                </div>

                {requirePinForHighValue && (
                  <div className="space-y-2">
                    <Label htmlFor="highValueThreshold">High-Value Threshold ({currencySymbol})</Label>
                    <Input
                      id="highValueThreshold"
                      type="number"
                      value={highValueThreshold || ''}
                      onChange={(e) => setHighValueThreshold(e.target.value ? Number(e.target.value) : null)}
                      className="w-full max-w-xs"
                      disabled={isSaving}
                    />
                    <p className="text-sm text-gray-500">
                      Transactions above this amount will require a PIN
                    </p>
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="cardLockoutThreshold">Card Lockout Threshold</Label>
                  <Input
                    id="cardLockoutThreshold"
                    type="number"
                    value={cardLockoutThreshold}
                    onChange={(e) => setCardLockoutThreshold(Number(e.target.value))}
                    className="w-full max-w-xs"
                    disabled={isSaving}
                  />
                  <p className="text-sm text-gray-500">
                    Number of failed attempts before a card is locked
                  </p>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="encryptData">Encrypt Transaction Data</Label>
                    <p className="text-sm text-gray-500">
                      Encrypt sensitive transaction data in the database
                    </p>
                  </div>
                  <Switch id="encryptData" defaultChecked disabled={isSaving} />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="offline">
            <Card>
              <CardHeader>
                <CardTitle>Offline Mode Settings</CardTitle>
                <CardDescription>
                  Configure how the system operates when internet connectivity is limited
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="offlineModeEnabled">Enable Offline Mode</Label>
                    <p className="text-sm text-gray-500">
                      Allow terminals to process transactions when offline
                    </p>
                  </div>
                  <Switch
                    id="offlineModeEnabled"
                    checked={offlineModeEnabled}
                    onCheckedChange={setOfflineModeEnabled}
                    disabled={isSaving}
                  />
                </div>

                {offlineModeEnabled && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="maxOfflineTransactions">Maximum Offline Transactions</Label>
                      <Input
                        id="maxOfflineTransactions"
                        type="number"
                        value={maxOfflineTransactions || ''}
                        onChange={(e) => setMaxOfflineTransactions(e.target.value ? Number(e.target.value) : null)}
                        className="w-full max-w-xs"
                        disabled={isSaving}
                      />
                      <p className="text-sm text-gray-500">
                        Maximum number of transactions a terminal can process while offline
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="offlineTransactionLimit">Offline Transaction Limit ({currencySymbol})</Label>
                      <Input
                        id="offlineTransactionLimit"
                        type="number"
                        value={offlineTransactionLimit || ''}
                        onChange={(e) => setOfflineTransactionLimit(e.target.value ? Number(e.target.value) : null)}
                        className="w-full max-w-xs"
                        disabled={isSaving}
                      />
                      <p className="text-sm text-gray-500">
                        Maximum amount allowed for a single offline transaction
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="syncInterval">Sync Interval (minutes)</Label>
                      <div className="flex items-center gap-4">
                        <Slider
                          id="syncInterval"
                          min={5}
                          max={60}
                          step={5}
                          value={[syncInterval || 15]}
                          onValueChange={(value) => setSyncInterval(value[0])}
                          className="w-full max-w-xs"
                          disabled={isSaving}
                        />
                        <span className="w-12 text-center">{syncInterval}</span>
                      </div>
                      <p className="text-sm text-gray-500">
                        How often terminals attempt to sync offline transactions
                      </p>
                    </div>
                  </>
                )}

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="autoSync">Auto-Sync When Online</Label>
                    <p className="text-sm text-gray-500">
                      Automatically sync offline transactions when connectivity is restored
                    </p>
                  </div>
                  <Switch id="autoSync" defaultChecked disabled={isSaving} />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance">
            <Card>
              <CardHeader>
                <CardTitle>Performance Settings</CardTitle>
                <CardDescription>
                  Configure system performance and optimization settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="cacheTimeout">Cache Timeout (minutes)</Label>
                  <div className="flex items-center gap-4">
                    <Slider
                      id="cacheTimeout"
                      min={5}
                      max={60}
                      step={5}
                      value={[cacheTimeout]}
                      onValueChange={(value) => setCacheTimeout(value[0])}
                      className="w-full max-w-xs"
                      disabled={isSaving}
                    />
                    <span className="w-12 text-center">{cacheTimeout}</span>
                  </div>
                  <p className="text-sm text-gray-500">
                    How long to cache data before refreshing from the server
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="batchSize">Batch Processing Size</Label>
                  <Input
                    id="batchSize"
                    type="number"
                    value={batchSize}
                    onChange={(e) => setBatchSize(Number(e.target.value))}
                    className="w-full max-w-xs"
                    disabled={isSaving}
                  />
                  <p className="text-sm text-gray-500">
                    Number of transactions to process in a single batch
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="logLevel">Log Level</Label>
                  <Select
                    value={logLevel}
                    onValueChange={setLogLevel}
                    disabled={isSaving}
                  >
                    <SelectTrigger id="logLevel" className="w-full max-w-xs">
                      <SelectValue placeholder="Select log level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="error">Error</SelectItem>
                      <SelectItem value="warn">Warning</SelectItem>
                      <SelectItem value="info">Info</SelectItem>
                      <SelectItem value="debug">Debug</SelectItem>
                      <SelectItem value="trace">Trace</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-gray-500">
                    Level of detail for system logs
                  </p>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="compressionEnabled">Enable Data Compression</Label>
                    <p className="text-sm text-gray-500">
                      Compress data to reduce bandwidth usage
                    </p>
                  </div>
                  <Switch id="compressionEnabled" defaultChecked disabled={isSaving} />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="backgroundSync">Background Synchronization</Label>
                    <p className="text-sm text-gray-500">
                      Sync data in the background to improve performance
                    </p>
                  </div>
                  <Switch id="backgroundSync" defaultChecked disabled={isSaving} />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      {!isLoading && (
        <div className="flex justify-end">
          <Button onClick={saveSettings} disabled={isSaving || !selectedEvent}>
            {isSaving ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Settings
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  );
}
