import { NextRequest, NextResponse } from 'next/server';
import { parseEventUrl, categorySlugMap } from '@/lib/utils/events';

/**
 * Middleware to handle event URL redirects from old format to new SEO-friendly format
 * This runs in the Edge runtime, so we can't use Prisma here
 */
export function eventUrlRedirectMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check if this is an old format event URL: /events/[id]
  const oldFormatMatch = pathname.match(/^\/events\/([a-zA-Z0-9_-]+)$/);
  
  if (oldFormatMatch) {
    const eventId = oldFormatMatch[1];
    
    // Check if this looks like an event ID (not a category slug)
    // Event IDs are typically longer and contain mixed case or special characters
    const isEventId = eventId.length > 10 || 
                     /[A-Z]/.test(eventId) || 
                     /[0-9]/.test(eventId) ||
                     !Object.values(categorySlugMap).includes(eventId);
    
    if (isEventId) {
      // This is an old format URL, redirect to API endpoint that will handle the database lookup and redirect
      const redirectUrl = new URL('/api/events/redirect', request.url);
      redirectUrl.searchParams.set('id', eventId);
      redirectUrl.searchParams.set('returnUrl', pathname);

      return NextResponse.redirect(redirectUrl, 301);
    }
  }

  // Check if this is a new format URL that needs validation
  const newFormatMatch = pathname.match(/^\/events\/([^\/]+)\/([^\/]+)\/([^\/]+)$/);
  
  if (newFormatMatch) {
    const [, categorySlug, titleSlug, eventId] = newFormatMatch;
    
    // Basic validation - check if category slug exists
    const categoryExists = Object.values(categorySlugMap).includes(categorySlug);
    
    if (!categoryExists) {
      // Invalid category slug, return 404
      return new NextResponse('Not Found', { status: 404 });
    }
    
    // URL looks valid, let it proceed to the route handler
    return NextResponse.next();
  }

  // Not an event URL, continue normally
  return NextResponse.next();
}
