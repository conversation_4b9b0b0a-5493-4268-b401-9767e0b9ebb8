'use client';

import React from 'react';
import { X } from 'lucide-react';
import CheckoutClient from './CheckoutClient';

interface TicketType {
  id: string;
  type: string;
  displayName?: string;
  price: number;
  description?: string;
  isAvailable: boolean;
  maxPerOrder?: number;
  totalSeats: number;
}

interface CheckoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  eventId: string;
  availableTickets: TicketType[];
  eventDetails: {
    title: string;
    startDate: Date | string;
    startTime: string;
    endTime: string;
    venue: string;
    imagePath?: string | null;
  };
}

export default function CheckoutModal({
  isOpen,
  onClose,
  eventId,
  availableTickets,
  eventDetails
}: CheckoutModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/80 backdrop-blur-sm transition-opacity"
        onClick={onClose}
      />

      {/* Modal content */}
      <div className="fixed z-50 w-[95vw] max-w-[1200px] h-[90vh] rounded-xl bg-white shadow-xl flex flex-col animate-in fade-in-50 zoom-in-95 duration-300">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute right-4 top-4 z-10 rounded-full bg-white/90 p-2 text-gray-600 shadow-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 transition-all duration-200"
          aria-label="Close"
        >
          <X className="h-5 w-5" />
          <span className="sr-only">Close</span>
        </button>

        {/* Modal body */}
        <div className="flex-1 overflow-hidden">
          <CheckoutClient
            id={eventId}
            availableTickets={availableTickets}
          />
        </div>
      </div>
    </div>
  );
}
