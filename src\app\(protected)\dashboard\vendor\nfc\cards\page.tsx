'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import {
  CreditCard,
  Search,
  Loader2,
  PlusCircle,
  Edit,
  Trash2,
  AlertCircle,
  CheckCircle,
  XCircle,
  Filter,
  ChevronLeft,
  ChevronRight,
  RefreshCw
} from 'lucide-react';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

// Define the NFC card interface
interface NFCCard {
  id: string;
  cardId: string;
  userId: string;
  userName: string;
  userEmail: string;
  status: 'ACTIVE' | 'INACTIVE' | 'LOST';
  balance: number;
  createdAt: string;
  lastUsed: string | null;
}

// Form schema for adding/editing cards
const cardFormSchema = z.object({
  cardId: z.string().min(1, "Card ID is required"),
  userName: z.string().min(1, "User name is required"),
  userEmail: z.string().email("Invalid email address"),
  status: z.enum(["ACTIVE", "INACTIVE", "LOST"]),
  balance: z.coerce.number().min(0, "Balance must be a positive number"),
});

// Constants
const PAGE_SIZE = 10;
const STATUS_OPTIONS = [
  { value: 'ALL', label: 'All Statuses' },
  { value: 'ACTIVE', label: 'Active' },
  { value: 'INACTIVE', label: 'Inactive' },
  { value: 'LOST', label: 'Lost' }
];

export default function NFCCardsPage() {
  const router = useRouter();

  // State variables
  const [cards, setCards] = useState<NFCCard[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCards, setTotalCards] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedCard, setSelectedCard] = useState<NFCCard | null>(null);
  const [isScanning, setIsScanning] = useState(false);

  // Form for adding new cards
  const addForm = useForm<z.infer<typeof cardFormSchema>>({
    resolver: zodResolver(cardFormSchema),
    defaultValues: {
      cardId: "",
      userName: "",
      userEmail: "",
      status: "ACTIVE",
      balance: 0,
    },
  });

  // Form for editing cards
  const editForm = useForm<z.infer<typeof cardFormSchema>>({
    resolver: zodResolver(cardFormSchema),
    defaultValues: {
      cardId: "",
      userName: "",
      userEmail: "",
      status: "ACTIVE",
      balance: 0,
    },
  });

  // Fetch cards on component mount and when filters change
  useEffect(() => {
    fetchCards();
  }, [currentPage, statusFilter]);

  // Function to fetch cards
  async function fetchCards() {
    try {
      setLoading(true);

      // In a real implementation, this would call an API endpoint
      // For now, we'll simulate it with mock data
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock data
      const mockCards: NFCCard[] = Array.from({ length: 10 }, (_, i) => ({
        id: `card-${i + 1 + (currentPage - 1) * 10}`,
        cardId: `NFC-${100000 + i + (currentPage - 1) * 10}`,
        userId: `user-${i + 1}`,
        userName: `User ${i + 1}`,
        userEmail: `user${i + 1}@example.com`,
        status: ['ACTIVE', 'INACTIVE', 'LOST'][Math.floor(Math.random() * 3)] as 'ACTIVE' | 'INACTIVE' | 'LOST',
        balance: Math.floor(Math.random() * 10000) / 100,
        createdAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString(),
        lastUsed: Math.random() > 0.3 ? new Date(Date.now() - Math.floor(Math.random() * 7) * 24 * 60 * 60 * 1000).toISOString() : null,
      }));

      setCards(mockCards);
      setTotalPages(5); // Mock total pages
      setTotalCards(50); // Mock total cards
    } catch (error) {
      console.error('Error fetching cards:', error);
      toast({
        title: 'Error',
        description: 'Failed to load NFC cards. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page
    fetchCards();
  };

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Format currency for display
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Active
          </Badge>
        );
      case 'INACTIVE':
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            <XCircle className="h-3 w-3 mr-1" />
            Inactive
          </Badge>
        );
      case 'LOST':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <AlertCircle className="h-3 w-3 mr-1" />
            Lost
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        );
    }
  };

  // Handle adding a new card
  const handleAddCard = (data: z.infer<typeof cardFormSchema>) => {
    try {
      // In a real implementation, this would call an API endpoint
      console.log('Adding new card:', data);

      toast({
        title: 'Card Added',
        description: 'The NFC card has been added successfully.',
      });

      setIsAddDialogOpen(false);
      addForm.reset();
      fetchCards();
    } catch (error) {
      console.error('Error adding card:', error);
      toast({
        title: 'Error',
        description: 'Failed to add the NFC card. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle editing a card
  const handleEditCard = (data: z.infer<typeof cardFormSchema>) => {
    try {
      // In a real implementation, this would call an API endpoint
      console.log('Editing card:', data);

      toast({
        title: 'Card Updated',
        description: 'The NFC card has been updated successfully.',
      });

      setIsEditDialogOpen(false);
      setSelectedCard(null);
      fetchCards();
    } catch (error) {
      console.error('Error updating card:', error);
      toast({
        title: 'Error',
        description: 'Failed to update the NFC card. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle deleting a card
  const handleDeleteCard = () => {
    try {
      // In a real implementation, this would call an API endpoint
      console.log('Deleting card:', selectedCard?.id);

      toast({
        title: 'Card Deleted',
        description: 'The NFC card has been deleted successfully.',
      });

      setIsDeleteDialogOpen(false);
      setSelectedCard(null);
      fetchCards();
    } catch (error) {
      console.error('Error deleting card:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete the NFC card. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle scanning for a new card
  const handleScanCard = async () => {
    setIsScanning(true);

    try {
      // In a real implementation, this would activate the NFC reader
      // For demonstration purposes, we'll simulate it immediately
      // In a real app, you would use the Web NFC API here:
      // const ndef = new NDEFReader();
      // await ndef.scan();

      // Simulate a card read without setTimeout to maintain user gesture context
      const mockCardId = `NFC-${Math.floor(Math.random() * 900000) + 100000}`;
      addForm.setValue('cardId', mockCardId);

      toast({
        title: 'Card Detected',
        description: `NFC card detected: ${mockCardId}`,
      });
    } catch (error) {
      console.error('Error scanning NFC card:', error);
      toast({
        title: 'Scan Failed',
        description: 'Failed to scan NFC card. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsScanning(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <h1 className="text-3xl font-bold">NFC Card Management</h1>
          <p className="text-gray-600 mt-1">
            Manage NFC cards for your customers
          </p>
        </div>

        <Button
          onClick={() => setIsAddDialogOpen(true)}
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          Add New Card
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>NFC Cards</CardTitle>
          <CardDescription>
            View and manage NFC cards linked to customer accounts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
            <form
              className="relative w-full md:w-1/3"
              onSubmit={handleSearch}
            >
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                placeholder="Search by card ID, user..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
            </form>

            <div className="flex flex-col sm:flex-row gap-2">
              <Select
                value={statusFilter}
                onValueChange={setStatusFilter}
              >
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  {STATUS_OPTIONS.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                onClick={fetchCards}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading cards...</span>
            </div>
          ) : cards.length === 0 ? (
            <div className="text-center py-12">
              <CreditCard className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium mb-2">No Cards Found</h3>
              <p className="text-gray-500 mb-4">
                {searchQuery || statusFilter !== 'ALL'
                  ? 'Try adjusting your filters to see more results'
                  : 'You haven\'t added any NFC cards yet'}
              </p>
              {(searchQuery || statusFilter !== 'ALL') && (
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery('');
                    setStatusFilter('ALL');
                    setCurrentPage(1);
                    fetchCards();
                  }}
                >
                  <Filter className="mr-2 h-4 w-4" />
                  Clear Filters
                </Button>
              )}
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Card ID</TableHead>
                      <TableHead>User</TableHead>
                      <TableHead>Balance</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Last Used</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {cards.map(card => (
                      <TableRow key={card.id}>
                        <TableCell className="font-medium">
                          {card.cardId}
                        </TableCell>
                        <TableCell>
                          <div>
                            <p>{card.userName}</p>
                            <p className="text-sm text-gray-500">{card.userEmail}</p>
                          </div>
                        </TableCell>
                        <TableCell>{formatCurrency(card.balance)}</TableCell>
                        <TableCell>{getStatusBadge(card.status)}</TableCell>
                        <TableCell>{formatDate(card.createdAt)}</TableCell>
                        <TableCell>{formatDate(card.lastUsed)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSelectedCard(card);
                                editForm.reset({
                                  cardId: card.cardId,
                                  userName: card.userName,
                                  userEmail: card.userEmail,
                                  status: card.status,
                                  balance: card.balance,
                                });
                                setIsEditDialogOpen(true);
                              }}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSelectedCard(card);
                                setIsDeleteDialogOpen(true);
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              <div className="flex items-center justify-between mt-6">
                <p className="text-sm text-gray-500">
                  Showing {cards.length} of {totalCards} cards
                </p>

                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <p className="text-sm">
                    Page {currentPage} of {totalPages}
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Add Card Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add New NFC Card</DialogTitle>
            <DialogDescription>
              Register a new NFC card for a customer
            </DialogDescription>
          </DialogHeader>

          <Form {...addForm}>
            <form onSubmit={addForm.handleSubmit(handleAddCard)} className="space-y-4">
              <FormField
                control={addForm.control}
                name="cardId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Card ID</FormLabel>
                    <div className="flex gap-2">
                      <FormControl>
                        <Input placeholder="NFC-123456" {...field} />
                      </FormControl>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleScanCard}
                        disabled={isScanning}
                      >
                        {isScanning ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <CreditCard className="h-4 w-4 mr-2" />
                        )}
                        {isScanning ? 'Scanning...' : 'Scan'}
                      </Button>
                    </div>
                    <FormDescription>
                      The unique identifier for the NFC card
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={addForm.control}
                name="userName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>User Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={addForm.control}
                name="userEmail"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>User Email</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={addForm.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="ACTIVE">Active</SelectItem>
                        <SelectItem value="INACTIVE">Inactive</SelectItem>
                        <SelectItem value="LOST">Lost</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={addForm.control}
                name="balance"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Initial Balance</FormLabel>
                    <FormControl>
                      <Input type="number" step="0.01" min="0" {...field} />
                    </FormControl>
                    <FormDescription>
                      The initial balance to load onto the card
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsAddDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">Add Card</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Edit Card Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit NFC Card</DialogTitle>
            <DialogDescription>
              Update the details for this NFC card
            </DialogDescription>
          </DialogHeader>

          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(handleEditCard)} className="space-y-4">
              <FormField
                control={editForm.control}
                name="cardId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Card ID</FormLabel>
                    <FormControl>
                      <Input placeholder="NFC-123456" {...field} readOnly />
                    </FormControl>
                    <FormDescription>
                      The unique identifier for the NFC card (cannot be changed)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="userName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>User Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="userEmail"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>User Email</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="ACTIVE">Active</SelectItem>
                        <SelectItem value="INACTIVE">Inactive</SelectItem>
                        <SelectItem value="LOST">Lost</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="balance"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Balance</FormLabel>
                    <FormControl>
                      <Input type="number" step="0.01" min="0" {...field} />
                    </FormControl>
                    <FormDescription>
                      The current balance on the card
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">Update Card</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Card Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Delete NFC Card</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this NFC card? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          {selectedCard && (
            <div className="py-4">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="font-medium">{selectedCard.cardId}</p>
                  <p className="text-sm text-gray-500">{selectedCard.userName}</p>
                </div>
                {getStatusBadge(selectedCard.status)}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Balance</p>
                  <p className="font-medium">{formatCurrency(selectedCard.balance)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Last Used</p>
                  <p className="font-medium">{formatDate(selectedCard.lastUsed)}</p>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteCard}
            >
              Delete Card
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
