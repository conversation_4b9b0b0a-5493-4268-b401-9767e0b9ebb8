'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import {
  CreditCard,
  Save,
  RefreshCcw,
  WifiOff,
  Smartphone,
  Bell,
  Receipt,
  Printer,
  AlertCircle
} from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ReceiptPreview } from '@/components/vendor/nfc/ReceiptPreview';
import { PrinterSetup } from '@/components/vendor/nfc/PrinterSetup';
import { BluetoothPrinterSettings } from '@/components/bluetooth/bluetooth-printer-settings';

interface NFCTerminalSettings {
  id: string;
  vendorId: string;
  terminalName: string;
  offlineMode: boolean;
  autoSync: boolean;
  notificationsEnabled: boolean;
  autoPrint: boolean;
  deviceId: string;
  lastSyncTime: string | null;
  softwareVersion: string | null;
  createdAt: string;
  updatedAt: string;
}

export default function NFCTerminalSettingsPage() {
  // State for terminal settings
  const [terminalName, setTerminalName] = useState('Main Terminal');
  const [offlineMode, setOfflineMode] = useState(false);
  const [autoSync, setAutoSync] = useState(true);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [autoPrint, setAutoPrint] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deviceId, setDeviceId] = useState<string>('');
  const [lastSyncTime, setLastSyncTime] = useState<string | null>(null);
  const [softwareVersion, setSoftwareVersion] = useState<string>('1.0.0');
  const [isSyncing, setIsSyncing] = useState(false);
  const [isTestingNFC, setIsTestingNFC] = useState(false);
  const [showReceiptPreview, setShowReceiptPreview] = useState(false);
  const [showPrinterSetup, setShowPrinterSetup] = useState(false);
  const [showBluetoothSettings, setShowBluetoothSettings] = useState(false);
  const [bluetoothPrinter, setBluetoothPrinter] = useState({
    enabled: false,
    deviceId: '',
    deviceName: '',
    deviceInfo: '',
    isPrinter: false,
    autoPrint: false,
  });

  // Fetch settings on component mount
  useEffect(() => {
    fetchSettings();
  }, []);

  // Fetch settings from API
  const fetchSettings = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Fetching settings...');
      const response = await fetch('/api/vendor/nfc/settings', {
        credentials: 'include', // Include cookies for authentication
      });

      console.log('Fetch response status:', response.status);

      if (!response.ok) {
        throw new Error(`Failed to fetch settings: ${response.statusText} (${response.status})`);
      }

      const data = await response.json();
      console.log('Fetched settings data:', data);

      if (data.success) {
        const settings = data.data as NFCTerminalSettings;

        // Update state with fetched settings
        setTerminalName(settings.terminalName);
        setOfflineMode(settings.offlineMode);
        setAutoSync(settings.autoSync);
        setNotificationsEnabled(settings.notificationsEnabled);
        setAutoPrint(settings.autoPrint);
        setDeviceId(settings.deviceId);
        setLastSyncTime(settings.lastSyncTime);
        setSoftwareVersion(settings.softwareVersion || '1.0.0');

        if (data.isNew) {
          toast({
            title: 'Default Settings Created',
            description: 'Default terminal settings have been created.',
          });
        }
      } else {
        throw new Error(data.error || 'Failed to fetch settings');
      }
    } catch (err) {
      console.error('Error fetching settings:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle saving settings
  const handleSaveSettings = async () => {
    setIsSaving(true);
    setError(null);

    try {
      console.log('Saving settings:', {
        terminalName,
        offlineMode,
        autoSync,
        notificationsEnabled,
        autoPrint,
      });

      const response = await fetch('/api/vendor/nfc/settings', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          terminalName,
          offlineMode,
          autoSync,
          notificationsEnabled,
          autoPrint,
        }),
        credentials: 'include', // Include cookies for authentication
      });

      console.log('Response status:', response.status);

      // Get the response text for debugging
      const responseText = await response.text();
      console.log('Response text:', responseText);

      // Parse the JSON if possible
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (e) {
        console.error('Failed to parse response as JSON:', e);
        throw new Error(`Failed to save settings: Invalid response format (${response.status})`);
      }

      if (!response.ok) {
        throw new Error(`Failed to save settings: ${data.error || response.statusText} (${response.status})`);
      }

      if (data.success) {
        toast({
          title: 'Settings Saved',
          description: 'Your terminal settings have been updated successfully.',
        });
      } else {
        throw new Error(data.error || 'Failed to save settings: Unknown error');
      }
    } catch (err) {
      console.error('Error saving settings:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');

      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to save settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle syncing transactions
  const handleSyncTransactions = async () => {
    setIsSyncing(true);

    try {
      console.log('Syncing transactions...');
      const response = await fetch('/api/vendor/nfc/settings/sync', {
        method: 'POST',
        credentials: 'include', // Include cookies for authentication
      });

      console.log('Sync response status:', response.status);

      if (!response.ok) {
        throw new Error(`Failed to sync transactions: ${response.statusText} (${response.status})`);
      }

      const data = await response.json();
      console.log('Sync response data:', data);

      if (data.success) {
        // Update last sync time
        setLastSyncTime(data.data.lastSyncTime);

        toast({
          title: 'Sync Complete',
          description: 'Your transactions have been synchronized successfully.',
        });
      } else {
        throw new Error(data.error || 'Failed to sync transactions');
      }
    } catch (err) {
      console.error('Error syncing transactions:', err);

      toast({
        title: 'Sync Failed',
        description: 'Failed to synchronize transactions. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSyncing(false);
    }
  };

  // Handle testing NFC reader
  const handleTestNFCReader = async () => {
    // Only run in browser environment
    if (typeof window === 'undefined') {
      return;
    }

    setIsTestingNFC(true);

    try {
      // Check if Web NFC API is available
      if (!('NDEFReader' in window)) {
        throw new Error('Web NFC API is not supported in this browser');
      }

      // Create a new NFC reader
      const ndef = new (window as any).NDEFReader();

      // Start scanning - this must be called directly from a user gesture
      await ndef.scan();

      toast({
        title: 'NFC Reader Active',
        description: 'Please tap an NFC card or tag to test.',
      });

      // Listen for NFC readings
      ndef.addEventListener("reading", ({ serialNumber }: any) => {
        toast({
          title: 'NFC Card Detected',
          description: `Card ID: ${serialNumber}`,
        });

        // Stop testing after a card is detected
        setIsTestingNFC(false);
      });

      // Add error event listener
      ndef.addEventListener("readingerror", (error: any) => {
        console.error('NFC reading error:', error);
        toast({
          title: 'NFC Reading Error',
          description: 'Error reading NFC card. Please try again.',
          variant: 'destructive',
        });
      });

      // Create a button to stop scanning after a reasonable time
      const stopButton = document.createElement('button');
      stopButton.style.display = 'none';
      document.body.appendChild(stopButton);

      // Set up a way to stop scanning after 10 seconds
      // We'll use a button click to maintain user gesture context
      setTimeout(() => {
        if (stopButton && document.body.contains(stopButton)) {
          stopButton.click(); // This maintains user gesture context
          document.body.removeChild(stopButton);
          setIsTestingNFC(false);
        }
      }, 10000);

      // Clean up
      return () => {
        if (stopButton && document.body.contains(stopButton)) {
          document.body.removeChild(stopButton);
        }
      };
    } catch (err) {
      console.error('Error testing NFC reader:', err);

      toast({
        title: 'NFC Test Failed',
        description: err instanceof Error ? err.message : 'Failed to test NFC reader',
        variant: 'destructive',
      });

      setIsTestingNFC(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <h1 className="text-3xl font-bold">Terminal Settings</h1>
          <p className="text-gray-600 mt-1">
            Configure your NFC payment terminal
          </p>
        </div>

        <Button
          onClick={handleSaveSettings}
          disabled={isSaving || isLoading}
        >
          {isSaving ? (
            <RefreshCcw className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Save className="mr-2 h-4 w-4" />
          )}
          {isSaving ? 'Saving...' : 'Save Settings'}
        </Button>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {isLoading ? (
        <div className="flex flex-col items-center justify-center py-12">
          <RefreshCcw className="h-8 w-8 animate-spin mb-4" />
          <p className="text-lg font-medium">Loading terminal settings...</p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>General Settings</CardTitle>
                <CardDescription>
                  Basic configuration for your NFC terminal
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="terminal-name">Terminal Name</Label>
                  <Input
                    id="terminal-name"
                    value={terminalName}
                    onChange={(e) => setTerminalName(e.target.value)}
                    placeholder="Enter a name for this terminal"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="offline-mode">Offline Mode</Label>
                    <p className="text-sm text-muted-foreground">
                      Process transactions when internet is unavailable
                    </p>
                  </div>
                  <Switch
                    id="offline-mode"
                    checked={offlineMode}
                    onCheckedChange={setOfflineMode}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="auto-sync">Auto Sync</Label>
                    <p className="text-sm text-muted-foreground">
                      Automatically sync offline transactions when online
                    </p>
                  </div>
                  <Switch
                    id="auto-sync"
                    checked={autoSync}
                    onCheckedChange={setAutoSync}
                    disabled={!offlineMode}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Notification Settings</CardTitle>
                <CardDescription>
                  Configure alerts and notifications
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="notifications">Transaction Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive notifications for successful transactions
                    </p>
                  </div>
                  <Switch
                    id="notifications"
                    checked={notificationsEnabled}
                    onCheckedChange={setNotificationsEnabled}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="auto-print">Auto Print Receipts</Label>
                    <p className="text-sm text-muted-foreground">
                      Automatically print receipts after transactions
                    </p>
                  </div>
                  <Switch
                    id="auto-print"
                    checked={autoPrint}
                    onCheckedChange={setAutoPrint}
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Device Information</CardTitle>
                <CardDescription>
                  Information about this terminal device
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Device ID</p>
                      <p className="font-medium">{deviceId}</p>
                    </div>

                    <div>
                      <p className="text-sm font-medium text-muted-foreground">NFC Support</p>
                      <div className="flex items-center mt-1">
                        <div className={`h-2 w-2 rounded-full ${(typeof window !== 'undefined' && 'NDEFReader' in window) ? 'bg-green-500' : 'bg-red-500'} mr-2`}></div>
                        <p className="font-medium">{(typeof window !== 'undefined' && 'NDEFReader' in window) ? 'Available' : 'Not Supported'}</p>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Last Sync</p>
                      <p className="font-medium">
                        {lastSyncTime
                          ? new Date(lastSyncTime).toLocaleString()
                          : 'Never synced'}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Software Version</p>
                      <p className="font-medium">v{softwareVersion}</p>
                    </div>

                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Pending Offline Transactions</p>
                      <p className="font-medium">{offlineMode ? '0' : 'N/A'}</p>
                    </div>

                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Connected Printer</p>
                      <div className="flex items-center mt-1">
                        <div className={`h-2 w-2 rounded-full ${
                          bluetoothPrinter.enabled && bluetoothPrinter.deviceId
                            ? (bluetoothPrinter.isPrinter ? 'bg-green-500' : 'bg-amber-500')
                            : 'bg-yellow-500'
                        } mr-2`}></div>
                        <p className="font-medium">
                          {bluetoothPrinter.enabled && bluetoothPrinter.deviceId
                            ? bluetoothPrinter.deviceName || 'Bluetooth Printer Connected'
                            : 'Not Detected'}
                        </p>
                        {bluetoothPrinter.enabled && bluetoothPrinter.deviceId && !bluetoothPrinter.isPrinter && (
                          <span className="ml-2 px-1.5 py-0.5 text-xs bg-amber-100 text-amber-800 rounded-full">
                            Unverified
                          </span>
                        )}
                      </div>
                      {bluetoothPrinter.enabled && bluetoothPrinter.deviceId && !bluetoothPrinter.isPrinter && (
                        <p className="text-xs text-amber-600 mt-1">
                          Device may not be a printer
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline">
                  <RefreshCcw className="mr-2 h-4 w-4" />
                  Check for Updates
                </Button>
                <Button
                  variant="outline"
                  onClick={handleTestNFCReader}
                  disabled={isTestingNFC || !(typeof window !== 'undefined' && 'NDEFReader' in window)}
                >
                  {isTestingNFC ? (
                    <RefreshCcw className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Smartphone className="mr-2 h-4 w-4" />
                  )}
                  {isTestingNFC ? 'Testing...' : 'Test NFC Reader'}
                </Button>
              </CardFooter>
            </Card>
          </div>

          <div className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Common terminal management tasks
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                  <Button
                    variant="outline"
                    className="h-auto py-4 flex flex-col items-center justify-center"
                    onClick={handleSyncTransactions}
                    disabled={isSyncing}
                  >
                    {isSyncing ? (
                      <RefreshCcw className="h-6 w-6 mb-2 animate-spin" />
                    ) : (
                      <RefreshCcw className="h-6 w-6 mb-2" />
                    )}
                    <span>{isSyncing ? 'Syncing...' : 'Sync Transactions'}</span>
                  </Button>

                  <Button
                    variant="outline"
                    className="h-auto py-4 flex flex-col items-center justify-center"
                    onClick={() => setOfflineMode(!offlineMode)}
                  >
                    <WifiOff className="h-6 w-6 mb-2" />
                    <span>Toggle Offline Mode</span>
                  </Button>

                  <Button
                    variant="outline"
                    className="h-auto py-4 flex flex-col items-center justify-center"
                    onClick={() => setShowReceiptPreview(true)}
                  >
                    <Receipt className="h-6 w-6 mb-2" />
                    <span>Test Receipt</span>
                  </Button>

                  <Button
                    variant="outline"
                    className="h-auto py-4 flex flex-col items-center justify-center"
                    onClick={() => setShowPrinterSetup(true)}
                  >
                    <Printer className="h-6 w-6 mb-2" />
                    <span>Printer Setup</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle>Bluetooth Printer</CardTitle>
                <CardDescription>
                  Connect and configure a Bluetooth receipt printer
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  variant="outline"
                  onClick={() => setShowBluetoothSettings(!showBluetoothSettings)}
                  className="mb-4"
                >
                  {showBluetoothSettings ? 'Hide Bluetooth Settings' : 'Show Bluetooth Settings'}
                </Button>

                {showBluetoothSettings && (
                  <BluetoothPrinterSettings
                    initialSettings={bluetoothPrinter}
                    onSave={(settings) => {
                      setBluetoothPrinter(settings);
                      setAutoPrint(settings.autoPrint);
                      toast({
                        title: 'Bluetooth Settings Saved',
                        description: settings.enabled
                          ? `Connected to printer: ${settings.deviceName || 'Unknown Device'}`
                          : 'Bluetooth printer disabled',
                      });
                    }}
                  />
                )}
              </CardContent>
            </Card>
          </div>

          {/* Receipt Preview Modal */}
          {showReceiptPreview && (
            <ReceiptPreview onClose={() => setShowReceiptPreview(false)} />
          )}

          {/* Printer Setup Modal */}
          {showPrinterSetup && (
            <PrinterSetup onClose={() => setShowPrinterSetup(false)} />
          )}
        </>
      )}
    </div>
  );
}
