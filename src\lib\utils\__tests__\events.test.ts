import { 
  generateEventSlug, 
  generateEventUrl, 
  parseEventUrl, 
  getCanonicalEventUrl,
  categorySlugMap,
  slugCategoryMap 
} from '../events';

describe('Event URL utilities', () => {
  describe('generateEventSlug', () => {
    it('should generate a proper slug from event title', () => {
      expect(generateEventSlug('Annual Developer Conference')).toBe('annual-developer-conference');
      expect(generateEventSlug('Tech Summit 2024!')).toBe('tech-summit-2024');
      expect(generateEventSlug('Music & Arts Festival')).toBe('music-arts-festival');
      expect(generateEventSlug('  Spaced  Out  Event  ')).toBe('spaced-out-event');
    });

    it('should handle special characters', () => {
      expect(generateEventSlug('Event@#$%^&*()Title')).toBe('event-title');
      expect(generateEventSlug('Event_with_underscores')).toBe('event-with-underscores');
    });
  });

  describe('generateEventUrl', () => {
    it('should generate SEO-friendly URLs', () => {
      const event = {
        id: 'cmaqv9i6c000grbxwii1gsv1v',
        title: 'Annual Developer Conference',
        category: 'CONFERENCES_AND_WORKSHOPS'
      };

      const url = generateEventUrl(event);
      expect(url).toBe('/events/conferences/annual-developer-conference/cmaqv9i6c000grbxwii1gsv1v');
    });

    it('should handle unknown categories', () => {
      const event = {
        id: 'test123',
        title: 'Unknown Event',
        category: 'UNKNOWN_CATEGORY'
      };

      const url = generateEventUrl(event);
      expect(url).toBe('/events/events/unknown-event/test123');
    });
  });

  describe('parseEventUrl', () => {
    it('should parse new format URLs correctly', () => {
      const url = '/events/conferences/annual-developer-conference/cmaqv9i6c000grbxwii1gsv1v';
      const parsed = parseEventUrl(url);

      expect(parsed).toEqual({
        id: 'cmaqv9i6c000grbxwii1gsv1v',
        category: 'CONFERENCES_AND_WORKSHOPS',
        titleSlug: 'annual-developer-conference',
        categorySlug: 'conferences',
        isLegacyUrl: false
      });
    });

    it('should parse legacy format URLs correctly', () => {
      const url = '/events/cmaqv9i6c000grbxwii1gsv1v';
      const parsed = parseEventUrl(url);

      expect(parsed).toEqual({
        id: 'cmaqv9i6c000grbxwii1gsv1v',
        category: null,
        titleSlug: null,
        isLegacyUrl: true
      });
    });

    it('should return null for invalid URLs', () => {
      expect(parseEventUrl('/invalid/url')).toBeNull();
      expect(parseEventUrl('/events')).toBeNull();
      expect(parseEventUrl('/events/too/many/parts/here/extra')).toBeNull();
    });
  });

  describe('getCanonicalEventUrl', () => {
    it('should always return the new format URL', () => {
      const event = {
        id: 'test123',
        title: 'Test Event',
        category: 'MUSIC'
      };

      const canonical = getCanonicalEventUrl(event);
      expect(canonical).toBe('/events/music/test-event/test123');
    });
  });

  describe('category mappings', () => {
    it('should have consistent category mappings', () => {
      // Test that every category has a corresponding slug
      Object.keys(categorySlugMap).forEach(category => {
        const slug = categorySlugMap[category];
        expect(slugCategoryMap[slug]).toBe(category);
      });

      // Test that every slug maps back to a category
      Object.keys(slugCategoryMap).forEach(slug => {
        const category = slugCategoryMap[slug];
        expect(categorySlugMap[category]).toBe(slug);
      });
    });
  });
});
