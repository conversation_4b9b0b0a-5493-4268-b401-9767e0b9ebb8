# Production Deployment Guide

This guide outlines the steps to deploy the event platform to production, with a focus on the event featuring system.

## Prerequisites

- A production server with Node.js 18+ installed
- A domain name configured with DNS
- SSL certificates for HTTPS
- A Stripe account with API keys
- A Resend account for email delivery

## Environment Setup

1. Create a `.env.production` file with the following variables:

```
# Application
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com

# Database
DATABASE_URL=your_production_database_url

# Authentication
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your_nextauth_secret
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Stripe
STRIPE_PRODUCTION_SECRET_KEY=your_stripe_production_secret_key
NEXT_PUBLIC_STRIPE_PRODUCTION_PUBLISHABLE_KEY=your_stripe_production_publishable_key
STRIPE_PRODUCTION_WEBHOOK_SECRET=your_stripe_production_webhook_secret

# Email
RESEND_API_KEY=your_resend_api_key
EMAIL_FROM=<EMAIL>

# Cron Jobs
CRON_SECRET=your_cron_secret
PRODUCTION_DOMAIN=your-domain.com
```

## Deployment Steps

### 1. Build the Application

```bash
# Install dependencies
npm ci

# Build the application
npm run build
```

### 2. Set Up Stripe in Production Mode

1. Log in to your Stripe Dashboard
2. Switch from "Test Mode" to "Live Mode"
3. Get your production API keys from the Developers > API keys section
4. Update your `.env.production` file with these keys

### 3. Configure Stripe Webhook Endpoints

1. In the Stripe Dashboard, go to Developers > Webhooks
2. Add a new endpoint: `https://your-domain.com/api/webhooks/stripe`
3. Select the following events to listen for:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `checkout.session.completed`
4. Get the webhook signing secret and add it to your `.env.production` file

### 4. Set Up Cron Jobs for Featuring Expiration

Run the cron job setup script:

```bash
chmod +x scripts/production/setup-cron-jobs.sh
./scripts/production/setup-cron-jobs.sh
```

This will set up the following cron jobs:
- Daily at midnight: Check for expired featuring and update their status
- Daily at 9 AM: Send notifications for featuring that will expire soon

### 5. Start the Application

```bash
# Start the application with PM2
npm install -g pm2
pm2 start npm --name "event-platform" -- start
pm2 save
pm2 startup
```

## Monitoring and Maintenance

### Stripe Webhook Monitoring

1. Set up Stripe webhook monitoring in your Stripe Dashboard
2. Configure alerts for failed webhook deliveries
3. Regularly check the webhook logs for any issues

### Cron Job Monitoring

1. Check the cron job logs at `/var/log/event-platform/cron.log`
2. Set up log rotation for the cron job logs:

```bash
sudo nano /etc/logrotate.d/event-platform
```

Add the following configuration:

```
/var/log/event-platform/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 root root
}
```

### Database Backups

Set up daily database backups:

```bash
# Create a backup script
mkdir -p /opt/event-platform/backups
nano /opt/event-platform/backup.sh
```

Add the following content:

```bash
#!/bin/bash
DATE=$(date +%Y-%m-%d)
pg_dump your_database_name > /opt/event-platform/backups/backup-$DATE.sql
find /opt/event-platform/backups -type f -name "backup-*.sql" -mtime +14 -delete
```

Make it executable and add to cron:

```bash
chmod +x /opt/event-platform/backup.sh
crontab -e
```

Add the following line:

```
0 2 * * * /opt/event-platform/backup.sh
```

## Troubleshooting

### Stripe Webhook Issues

If webhooks are not being received:

1. Check that the webhook URL is correct
2. Verify that the webhook secret is correctly set in your environment variables
3. Check the Stripe Dashboard for any failed webhook attempts
4. Ensure your server is accessible from the internet

### Cron Job Issues

If cron jobs are not running:

1. Check the cron job logs at `/var/log/event-platform/cron.log`
2. Verify that the cron jobs are correctly set up with `crontab -l`
3. Ensure the CRON_SECRET is correctly set in your environment variables
4. Check that the API endpoints are accessible

### Email Delivery Issues

If emails are not being sent:

1. Check the Resend dashboard for any failed email deliveries
2. Verify that the RESEND_API_KEY is correctly set in your environment variables
3. Ensure the EMAIL_FROM address is a verified domain in Resend

## Security Considerations

1. **API Keys**: Ensure all API keys are kept secure and not committed to version control
2. **HTTPS**: Ensure all traffic is served over HTTPS
3. **Rate Limiting**: Implement rate limiting for all API endpoints
4. **Input Validation**: Validate all user input to prevent injection attacks
5. **Error Handling**: Ensure errors are handled gracefully and do not expose sensitive information

## Performance Optimization

1. **Database Indexes**: Ensure all frequently queried fields are indexed
2. **Caching**: Implement caching for frequently accessed data
3. **Image Optimization**: Optimize images for faster loading
4. **CDN**: Use a CDN for static assets
5. **Server Scaling**: Monitor server load and scale as needed
