const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Import the notification functions
const { 
  notifyPartnerNewTransaction, 
  notifyPartnerNewReview, 
  notifyPartnerLoyaltyMilestone 
} = require('../src/lib/partner-notifications');

async function testPartnerNotifications() {
  try {
    console.log('🧪 Testing Partner Notification System...\n');

    // Get a test partner
    const partner = await prisma.partner.findFirst({
      include: {
        user: true
      }
    });

    if (!partner) {
      console.log('❌ No partners found. Please run the seeding script first.');
      return;
    }

    console.log(`📋 Testing notifications for partner: ${partner.businessName}`);
    console.log(`📧 Partner email: ${partner.user.email}\n`);

    // Test 1: New Transaction Notification
    console.log('🔄 Testing new transaction notification...');
    try {
      await notifyPartnerNewTransaction(partner.id, {
        amount: 125.50,
        customerName: '<PERSON>',
        itemCount: 3,
        loyaltyPoints: 12,
      });
      console.log('✅ Transaction notification sent successfully');
    } catch (error) {
      console.log('❌ Transaction notification failed:', error.message);
    }

    // Test 2: New Review Notification (Good Rating)
    console.log('\n🔄 Testing new review notification (good rating)...');
    try {
      await notifyPartnerNewReview(partner.id, {
        rating: 5,
        customerName: 'Jane Smith',
        comment: 'Excellent service and delicious food! Highly recommended.',
      });
      console.log('✅ Good review notification sent successfully');
    } catch (error) {
      console.log('❌ Good review notification failed:', error.message);
    }

    // Test 3: New Review Notification (Poor Rating)
    console.log('\n🔄 Testing new review notification (poor rating)...');
    try {
      await notifyPartnerNewReview(partner.id, {
        rating: 2,
        customerName: 'Bob Johnson',
        comment: 'Service was slow and food was cold. Not satisfied.',
      });
      console.log('✅ Poor review notification sent successfully');
    } catch (error) {
      console.log('❌ Poor review notification failed:', error.message);
    }

    // Test 4: Loyalty Milestone Notification
    console.log('\n🔄 Testing loyalty milestone notification...');
    try {
      await notifyPartnerLoyaltyMilestone(partner.id, {
        customerName: 'Alice Wilson',
        newTier: 'Gold',
        totalPoints: 1250,
      });
      console.log('✅ Loyalty milestone notification sent successfully');
    } catch (error) {
      console.log('❌ Loyalty milestone notification failed:', error.message);
    }

    // Check created notifications
    console.log('\n📊 Checking created notifications...');
    const notifications = await prisma.notification.findMany({
      where: {
        userId: partner.userId,
        createdAt: {
          gte: new Date(Date.now() - 5 * 60 * 1000) // Last 5 minutes
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`📬 Found ${notifications.length} recent notifications:`);
    notifications.forEach((notification, index) => {
      console.log(`  ${index + 1}. [${notification.type}] ${notification.message}`);
    });

    console.log('\n🎉 Partner notification testing completed!');
    console.log('\n📝 Summary:');
    console.log('- Transaction notifications: ✅ Implemented');
    console.log('- Review notifications: ✅ Implemented');
    console.log('- Loyalty notifications: ✅ Implemented');
    console.log('- Email notifications: ✅ Implemented');
    console.log('- In-app notifications: ✅ Implemented');
    console.log('\n💡 Partners will now receive notifications for:');
    console.log('  • New NFC transactions');
    console.log('  • Customer reviews (especially low ratings)');
    console.log('  • Loyalty program milestones');
    console.log('  • Email alerts for important events');

  } catch (error) {
    console.error('❌ Error testing partner notifications:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testPartnerNotifications();
