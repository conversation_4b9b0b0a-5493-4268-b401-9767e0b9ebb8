'use client';

import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  MessageCircle, 
  Calendar, 
  User, 
  Building, 
  Briefcase,
  MapPin,
  ExternalLink,
  Shield,
  Crown
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { EliteTier } from '@prisma/client';
import { hasFeature } from '@/config/elite-pricing';
import { AttendeeDirectoryProps, AttendeeProfile } from '@/types/elite-communication';

export default function AttendeeDirectory({
  eventId,
  currentUser,
  userTier,
  onStartDirectMessage
}: AttendeeDirectoryProps) {
  const [attendees, setAttendees] = useState<AttendeeProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [industryFilter, setIndustryFilter] = useState('');
  const [companyFilter, setCompanyFilter] = useState('');
  const [roleFilter, setRoleFilter] = useState('');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [total, setTotal] = useState(0);

  useEffect(() => {
    fetchAttendees();
  }, [eventId, searchTerm, industryFilter, companyFilter, roleFilter, page]);

  const fetchAttendees = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        eventId,
        page: page.toString(),
        limit: '20'
      });

      if (searchTerm) params.append('search', searchTerm);
      if (industryFilter) params.append('industry', industryFilter);
      if (companyFilter) params.append('company', companyFilter);
      if (roleFilter) params.append('role', roleFilter);

      const response = await fetch(`/api/elite-communication/attendees?${params}`);
      const data = await response.json();

      if (response.ok) {
        if (page === 1) {
          setAttendees(data.attendees);
        } else {
          setAttendees(prev => [...prev, ...data.attendees]);
        }
        setHasMore(data.hasMore);
        setTotal(data.total);
      } else {
        console.error('Error fetching attendees:', data.error);
      }
    } catch (error) {
      console.error('Error fetching attendees:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPage(1);
  };

  const handleFilterChange = (type: string, value: string) => {
    switch (type) {
      case 'industry':
        setIndustryFilter(value);
        break;
      case 'company':
        setCompanyFilter(value);
        break;
      case 'role':
        setRoleFilter(value);
        break;
    }
    setPage(1);
  };

  const loadMore = () => {
    setPage(prev => prev + 1);
  };

  const handleMessage = (attendeeId: string) => {
    if (onStartDirectMessage) {
      onStartDirectMessage(attendeeId);
    }
  };

  const handleScheduleMeeting = (attendeeId: string) => {
    // This will be implemented when meeting scheduler is ready
    console.log('Schedule meeting with:', attendeeId);
  };

  const canMessage = hasFeature(userTier, 'directMessaging');
  const canScheduleMeeting = hasFeature(userTier, 'meetingScheduling');
  const canAccessContactInfo = hasFeature(userTier, 'accessContactInfo');

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="space-y-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search attendees by name, company, or interests..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <Button variant="outline" className="flex items-center">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Select value={industryFilter} onValueChange={(value) => handleFilterChange('industry', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Filter by industry" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Industries</SelectItem>
              <SelectItem value="technology">Technology</SelectItem>
              <SelectItem value="finance">Finance</SelectItem>
              <SelectItem value="healthcare">Healthcare</SelectItem>
              <SelectItem value="education">Education</SelectItem>
              <SelectItem value="marketing">Marketing</SelectItem>
              <SelectItem value="consulting">Consulting</SelectItem>
            </SelectContent>
          </Select>

          <Select value={companyFilter} onValueChange={(value) => handleFilterChange('company', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Filter by company" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Companies</SelectItem>
              {/* Dynamic company list would be populated from API */}
            </SelectContent>
          </Select>

          <Select value={roleFilter} onValueChange={(value) => handleFilterChange('role', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Filter by role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Roles</SelectItem>
              <SelectItem value="ceo">CEO</SelectItem>
              <SelectItem value="cto">CTO</SelectItem>
              <SelectItem value="manager">Manager</SelectItem>
              <SelectItem value="developer">Developer</SelectItem>
              <SelectItem value="designer">Designer</SelectItem>
              <SelectItem value="consultant">Consultant</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-600">
          {total > 0 ? `${total} attendees found` : 'No attendees found'}
        </p>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="flex items-center">
            {userTier === EliteTier.BASIC && <Shield className="h-3 w-3 mr-1" />}
            {userTier === EliteTier.ELITE && <Shield className="h-3 w-3 mr-1 text-purple-600" />}
            {userTier === EliteTier.ELITE_PRO && <Crown className="h-3 w-3 mr-1 text-yellow-600" />}
            {userTier === EliteTier.BASIC ? 'Basic' : 
             userTier === EliteTier.ELITE ? 'Elite' : 'Elite Pro'} Access
          </Badge>
        </div>
      </div>

      {/* Attendee Grid */}
      {loading && page === 1 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-24"></div>
                    <div className="h-3 bg-gray-200 rounded w-32"></div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded w-full"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {attendees.map((attendee) => (
            <Card key={attendee.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={attendee.profilePhoto || attendee.user?.image} />
                      <AvatarFallback>
                        {attendee.displayName?.[0] || attendee.user?.name?.[0] || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-semibold text-sm">
                        {attendee.displayName || attendee.user?.name || 'Anonymous'}
                      </h3>
                      {attendee.role && (
                        <p className="text-xs text-gray-600 flex items-center">
                          <Briefcase className="h-3 w-3 mr-1" />
                          {attendee.role}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-3">
                {attendee.company && (
                  <div className="flex items-center text-sm text-gray-600">
                    <Building className="h-4 w-4 mr-2" />
                    {attendee.company}
                  </div>
                )}

                {attendee.industry && (
                  <Badge variant="secondary" className="text-xs">
                    {attendee.industry}
                  </Badge>
                )}

                {attendee.bio && (
                  <p className="text-sm text-gray-700 line-clamp-2">
                    {attendee.bio}
                  </p>
                )}

                {attendee.interests && attendee.interests.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {attendee.interests.slice(0, 3).map((interest, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {interest}
                      </Badge>
                    ))}
                    {attendee.interests.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{attendee.interests.length - 3} more
                      </Badge>
                    )}
                  </div>
                )}

                {/* Contact Information (Elite+ only) */}
                {canAccessContactInfo && (
                  <div className="flex items-center space-x-2 pt-2 border-t">
                    {attendee.linkedinUrl && (
                      <Button size="sm" variant="ghost" className="p-1 h-6 w-6">
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    )}
                    {attendee.twitterUrl && (
                      <Button size="sm" variant="ghost" className="p-1 h-6 w-6">
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex space-x-2 pt-2">
                  <Button
                    size="sm"
                    variant={canMessage ? "default" : "outline"}
                    disabled={!canMessage}
                    onClick={() => handleMessage(attendee.id)}
                    className="flex-1"
                  >
                    <MessageCircle className="h-4 w-4 mr-1" />
                    {canMessage ? 'Message' : 'Elite'}
                  </Button>
                  
                  <Button
                    size="sm"
                    variant={canScheduleMeeting ? "outline" : "ghost"}
                    disabled={!canScheduleMeeting}
                    onClick={() => handleScheduleMeeting(attendee.id)}
                  >
                    {canScheduleMeeting ? (
                      <Calendar className="h-4 w-4" />
                    ) : (
                      <Crown className="h-4 w-4 text-yellow-500" />
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Load More Button */}
      {hasMore && (
        <div className="text-center">
          <Button 
            onClick={loadMore} 
            disabled={loading}
            variant="outline"
          >
            {loading ? 'Loading...' : 'Load More Attendees'}
          </Button>
        </div>
      )}

      {/* Empty State */}
      {!loading && attendees.length === 0 && (
        <div className="text-center py-12">
          <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            No Attendees Found
          </h3>
          <p className="text-gray-600">
            {searchTerm || industryFilter || companyFilter || roleFilter
              ? 'Try adjusting your search filters to find more attendees.'
              : 'Be the first to create your networking profile for this event.'}
          </p>
        </div>
      )}
    </div>
  );
}
