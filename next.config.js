/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable ESLint during build for better code quality
  eslint: {
    ignoreDuringBuilds: false,
  },
  // Enable TypeScript type checking during build
  typescript: {
    ignoreBuildErrors: false,
  },
  // Use standalone output for Vercel deployment
  output: 'standalone',
  // Optimize for production
  experimental: {
    serverMinification: true,
    optimizePackageImports: ['lucide-react', 'sonner'],
  },
  // Ensure API routes are properly handled
  async headers() {
    return [
      {
        source: '/api/auth/:path*',
        headers: [
          { key: 'Content-Type', value: 'application/json' },
          { key: 'Cache-Control', value: 'no-store, max-age=0' },
        ],
      },
    ];
  },

  // External packages for server components
  serverExternalPackages: [
    'bcryptjs',
    'pdf-lib',
    'sharp',
    'react-syntax-highlighter',
    '@prisma/client'
  ],
  // Optimize bundle size
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  // Optimize for Vercel deployment
  poweredByHeader: false,

  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'your-image-domains.com',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      },
      {
        protocol: 'https',
        hostname: 'res.cloudinary.com',
      },
      {
        protocol: 'https',
        hostname: 'cdn-icons-png.flaticon.com',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
      },
      {
        protocol: 'http',
        hostname: '************',
      }
    ],
  },
}

module.exports = nextConfig