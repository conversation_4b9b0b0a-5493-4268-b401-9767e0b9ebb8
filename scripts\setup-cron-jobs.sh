#!/bin/bash

# This script sets up cron jobs for the application

# Generate a random cron secret if not already set
if [ -z "$CRON_SECRET" ]; then
  CRON_SECRET=$(openssl rand -hex 16)
  echo "Generated CRON_SECRET: $CRON_SECRET"
  
  # Add to .env file if it exists
  if [ -f ".env" ]; then
    if grep -q "CRON_SECRET=" .env; then
      # Replace existing CRON_SECRET
      sed -i "s/CRON_SECRET=.*/CRON_SECRET=$CRON_SECRET/" .env
    else
      # Add new CRON_SECRET
      echo "CRON_SECRET=$CRON_SECRET" >> .env
    fi
    echo "Added CRON_SECRET to .env file"
  else
    echo "Warning: .env file not found. Please add CRON_SECRET manually."
  fi
fi

# Set up cron jobs
(crontab -l 2>/dev/null || echo "") | grep -v "featuring-expiration" > temp_cron
echo "# Run featuring expiration check daily at midnight" >> temp_cron
echo "0 0 * * * curl -X GET https://your-domain.com/api/cron/featuring-expiration -H \"Authorization: Bearer $CRON_SECRET\"" >> temp_cron
crontab temp_cron
rm temp_cron

echo "Cron jobs set up successfully"
