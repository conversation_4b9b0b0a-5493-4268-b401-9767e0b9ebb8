import type { Metadata } from 'next';
import { Inter as FontSans } from 'next/font/google';
import './globals.css';
import './dark-mode.css';
import { cn } from '@/lib/utils';
import { themeScript } from '@/lib/theme-script';
import { ClientLayoutWrapper } from '@/components/providers/client-layout-wrapper';
import { DevDatabaseInitializer } from '@/components/providers/database-initializer';

const fontSans = FontSans({
  subsets: ['latin'],
  variable: '--font-sans',
})

export const metadata: Metadata = {
  title: {
    template: '%s | QUICK TIME',
    default: 'QuickTimeEvents',
  },
  description: 'find you favourite events here',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang='en' suppressHydrationWarning>
      <head>
        <script dangerouslySetInnerHTML={{ __html: themeScript() }} />
      </head>
      <body
        className={cn(
          'min-h-screen bg-background font-sans antialiased overflow-auto transition-colors duration-300 dark:bg-gray-950 dark:text-gray-50',
          fontSans.variable
        )}
      >
        <DevDatabaseInitializer>
          <ClientLayoutWrapper>
            {children}
          </ClientLayoutWrapper>
        </DevDatabaseInitializer>
      </body>
    </html>
  );
}
