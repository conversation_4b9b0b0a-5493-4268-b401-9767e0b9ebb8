const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  try {
    // Hash the password
    const hashedPassword = await bcrypt.hash('password123', 10);
    
    // Create a test user with ORGANIZER role
    const user = await prisma.user.create({
      data: {
        name: 'Test Organizer',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ORGANI<PERSON>ER',
        emailVerified: new Date(),
        accessToken: 'test-access-token-' + Math.random().toString(36).substring(2, 15),
      },
    });
    
    console.log('Test user created:', user);
  } catch (error) {
    console.error('Error creating test user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
