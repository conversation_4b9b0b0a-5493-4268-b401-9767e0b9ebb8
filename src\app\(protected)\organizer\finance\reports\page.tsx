'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useCurrentUser } from '@/hooks/use-current-user';
import {
  DollarSign,
  CreditCard,
  TrendingUp,
  Download,
  Calendar,
  Ticket,
  BarChart3
} from 'lucide-react';
import Link from 'next/link';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { DateRange } from 'react-day-picker';
import { downloadCSV, formatDateForFilename } from '@/lib/export-utils';
import { toast, Toaster } from 'sonner';

// Import custom components
import { OrganizerRevenueBreakdown } from '@/components/organizer/revenue-breakdown';
import { OrganizerTicketSalesBreakdown } from '@/components/organizer/ticket-sales-breakdown';

// Interface for revenue summary data
interface RevenueSummary {
  totalRevenue: number;
  ticketSales: number;
  platformFees: number;
  processingFees: number;
  netRevenue: number;
  ticketCount: number;
}

export default function OrganizerFinanceReportsPage() {
  const router = useRouter();
  const user = useCurrentUser();
  const [activeTab, setActiveTab] = useState('overview');
  const [dateRange, setDateRange] = useState<DateRange>({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    to: new Date()
  });
  const [isLoading, setIsLoading] = useState(true);
  const [revenueSummary, setRevenueSummary] = useState<RevenueSummary | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isExporting, setIsExporting] = useState(false);
  const [selectedEventId, setSelectedEventId] = useState<string>('all_events');

  // Function to export data based on the active tab
  const handleExportData = async () => {
    try {
      setIsExporting(true);

      // Build query parameters for date range
      const params = new URLSearchParams();

      if (dateRange?.from) {
        params.append('startDate', dateRange.from.toISOString());
      }

      if (dateRange?.to) {
        params.append('endDate', dateRange.to.toISOString());
      }

      if (selectedEventId && selectedEventId !== 'all_events') {
        params.append('eventId', selectedEventId);
      }

      // Generate filename with date range
      const fromDate = dateRange?.from ? formatDateForFilename(dateRange.from) : 'start';
      const toDate = dateRange?.to ? formatDateForFilename(dateRange.to) : 'now';
      const dateRangeStr = `${fromDate}_to_${toDate}`;

      // Export different data based on active tab
      if (activeTab === 'overview') {
        // Export revenue summary
        if (revenueSummary) {
          const summaryData = [
            {
              category: 'Total Revenue',
              amount: revenueSummary.totalRevenue
            },
            {
              category: 'Ticket Sales',
              amount: revenueSummary.ticketSales
            },
            {
              category: 'Platform Fees',
              amount: revenueSummary.platformFees
            },
            {
              category: 'Processing Fees',
              amount: revenueSummary.processingFees
            },
            {
              category: 'Net Revenue',
              amount: revenueSummary.netRevenue
            }
          ];

          downloadCSV(summaryData, `revenue_summary_${dateRangeStr}.csv`);
        }

        // Also export revenue breakdown
        const revenueResponse = await fetch(`/api/organizer/finance/revenue-breakdown?${params.toString()}`);
        if (revenueResponse.ok) {
          const revenueData = await revenueResponse.json();
          downloadCSV(revenueData, `revenue_breakdown_${dateRangeStr}.csv`);
        }
      } else if (activeTab === 'tickets') {
        // Export ticket sales data
        const response = await fetch(`/api/organizer/finance/ticket-sales?${params.toString()}`);
        if (response.ok) {
          const data = await response.json();
          downloadCSV(data, `ticket_sales_${dateRangeStr}.csv`);
        }
      }
    } catch (error) {
      console.error('Error exporting data:', error);
      toast.error('Export Failed', {
        description: 'There was an error exporting the data. Please try again.'
      });
    } finally {
      setIsExporting(false);
    }
  };

  // Fetch revenue summary data
  useEffect(() => {
    const fetchRevenueSummary = async () => {
      try {
        setIsLoading(true);
        const params = new URLSearchParams();

        if (dateRange.from) {
          params.append('startDate', dateRange.from.toISOString());
        }

        if (dateRange.to) {
          params.append('endDate', dateRange.to.toISOString());
        }

        if (selectedEventId && selectedEventId !== 'all_events') {
          params.append('eventId', selectedEventId);
        }

        const response = await fetch(`/api/organizer/finance/reports?${params.toString()}`);

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json();
        setRevenueSummary(data);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching revenue summary:', error);
        setError(error instanceof Error ? error.message : 'Failed to load revenue data');
        setIsLoading(false);
      }
    };

    fetchRevenueSummary();
  }, [dateRange, selectedEventId]);

  // If user is not authenticated, redirect to login
  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">You need to be logged in to view this page.</p>
          <Button onClick={() => router.push('/login')}>Go to Login</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Toaster />
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <div className="mb-2">
            <Link href="/organizer/dashboard" className="text-blue-600 hover:underline">
              ← Back to Dashboard
            </Link>
          </div>
          <h1 className="text-3xl font-bold">Revenue & Transaction Reports</h1>
          <p className="text-gray-500 mt-1">
            Comprehensive view of your event revenue
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex flex-col md:flex-row gap-3">
          <DateRangePicker
            date={dateRange}
            onSelect={setDateRange}
          />
          <Button
            variant="outline"
            onClick={handleExportData}
            disabled={isLoading || isExporting}
          >
            <Download className={`mr-2 h-4 w-4 ${isExporting ? 'animate-pulse' : ''}`} />
            {isExporting ? 'Exporting...' : 'Export Data'}
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-lg p-6 my-6">
          <h3 className="text-lg font-semibold mb-2">Error Loading Data</h3>
          <p>{error}</p>
          <Button
            onClick={() => {
              setError(null);
              setIsLoading(true);
              // Retry fetching data
              fetch(`/api/organizer/finance/reports?startDate=${dateRange.from?.toISOString()}&endDate=${dateRange.to?.toISOString()}`)
                .then(res => res.json())
                .then(data => {
                  setRevenueSummary(data);
                  setIsLoading(false);
                })
                .catch(err => {
                  setError(err instanceof Error ? err.message : 'Failed to load revenue data');
                  setIsLoading(false);
                });
            }}
            className="mt-4"
          >
            Try Again
          </Button>
        </div>
      ) : (
        <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid grid-cols-2 w-full">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="tickets">Ticket Sales</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                      <h3 className="text-2xl font-bold mt-1">${revenueSummary?.totalRevenue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</h3>
                      <p className="text-sm text-gray-600 mt-1">Gross revenue</p>
                    </div>
                    <div className="bg-blue-100 p-3 rounded-full">
                      <DollarSign className="h-5 w-5 text-blue-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Ticket Sales</p>
                      <h3 className="text-2xl font-bold mt-1">${revenueSummary?.ticketSales.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</h3>
                      <p className="text-sm text-gray-600 mt-1">{revenueSummary?.ticketCount.toLocaleString()} tickets sold</p>
                    </div>
                    <div className="bg-purple-100 p-3 rounded-full">
                      <Ticket className="h-5 w-5 text-purple-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Platform Fees</p>
                      <h3 className="text-2xl font-bold mt-1">${revenueSummary?.platformFees.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</h3>
                      <p className="text-sm text-gray-600 mt-1">6% of gross revenue</p>
                    </div>
                    <div className="bg-amber-100 p-3 rounded-full">
                      <CreditCard className="h-5 w-5 text-amber-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Net Revenue</p>
                      <h3 className="text-2xl font-bold mt-1">${revenueSummary?.netRevenue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</h3>
                      <p className="text-sm text-gray-600 mt-1">After fees</p>
                    </div>
                    <div className="bg-green-100 p-3 rounded-full">
                      <TrendingUp className="h-5 w-5 text-green-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle>Revenue Breakdown</CardTitle>
                  <CardDescription>Monthly revenue from ticket sales</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <OrganizerRevenueBreakdown dateRange={dateRange} eventId={selectedEventId} />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Fee Breakdown</CardTitle>
                  <CardDescription>Platform and processing fees</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium">Platform Fees (6%)</span>
                        <span className="font-medium">${revenueSummary?.platformFees.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{
                            width: `${Math.min(100, (revenueSummary?.platformFees || 0) / ((revenueSummary?.platformFees || 0) + (revenueSummary?.processingFees || 0)) * 100)}%`
                          }}
                        ></div>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium">Processing Fees (3.5%)</span>
                        <span className="font-medium">${revenueSummary?.processingFees.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-purple-600 h-2 rounded-full"
                          style={{
                            width: `${Math.min(100, (revenueSummary?.processingFees || 0) / ((revenueSummary?.platformFees || 0) + (revenueSummary?.processingFees || 0)) * 100)}%`
                          }}
                        ></div>
                      </div>
                    </div>

                    <div className="pt-4 border-t">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">Total Fees</span>
                        <span className="font-bold text-lg">${((revenueSummary?.platformFees || 0) + (revenueSummary?.processingFees || 0)).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
                      </div>
                    </div>

                    <div className="pt-4 border-t">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">Net Revenue</span>
                        <span className="font-bold text-lg text-green-600">${revenueSummary?.netRevenue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="tickets">
            <Card>
              <CardHeader>
                <CardTitle>Ticket Sales</CardTitle>
                <CardDescription>Revenue from ticket sales by event and ticket type</CardDescription>
              </CardHeader>
              <CardContent>
                <OrganizerTicketSalesBreakdown
                  dateRange={dateRange}
                  onEventSelect={setSelectedEventId}
                  selectedEventId={selectedEventId}
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
