'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import DeviceScanner from '@/components/vendor/device-scanner';
import { Info, Bluetooth, CreditCard } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

export default function DevicesPage() {
  const [selectedBluetoothDevice, setSelectedBluetoothDevice] = useState<any>(null);
  const [detectedNfcCard, setDetectedNfcCard] = useState<string | null>(null);

  const handleBluetoothDeviceSelected = (device: any) => {
    setSelectedBluetoothDevice(device);
    toast({
      title: 'Bluetooth Device Selected',
      description: `Selected: ${device.name}`,
    });
  };

  const handleNfcCardDetected = (cardId: string) => {
    setDetectedNfcCard(cardId);
    toast({
      title: 'NFC Card Detected',
      description: `Card ID: ${cardId}`,
    });
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div>
        <h1 className="text-3xl font-bold">Devices & Scanning</h1>
        <p className="text-gray-600 mt-1">
          Manage Bluetooth and NFC devices for your vendor operations
        </p>
      </div>

      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>Important Information</AlertTitle>
        <AlertDescription>
          Both Bluetooth and NFC APIs require user gestures (like button clicks) to request permissions.
          Click the scan buttons to start scanning.
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Tabs defaultValue="scanner" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="scanner">
                <Bluetooth className="mr-2 h-4 w-4" />
                Device Scanner
              </TabsTrigger>
              <TabsTrigger value="management">
                <CreditCard className="mr-2 h-4 w-4" />
                Device Management
              </TabsTrigger>
            </TabsList>

            <TabsContent value="scanner" className="mt-4">
              <DeviceScanner
                onBluetoothDeviceSelected={handleBluetoothDeviceSelected}
                onNfcCardDetected={handleNfcCardDetected}
                title="Scan for Devices"
                description="Use the tabs below to scan for Bluetooth devices or NFC cards"
              />
            </TabsContent>

            <TabsContent value="management" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Device Management</CardTitle>
                  <CardDescription>
                    Manage your connected devices and saved NFC cards
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-center py-8 text-gray-500">
                    Device management features coming soon.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="space-y-6">
          {selectedBluetoothDevice && (
            <Card>
              <CardHeader>
                <CardTitle>Selected Bluetooth Device</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p><span className="font-medium">Name:</span> {selectedBluetoothDevice.name}</p>
                  <p><span className="font-medium">ID:</span> {selectedBluetoothDevice.id}</p>
                  <p><span className="font-medium">Type:</span> {selectedBluetoothDevice.isPrinter ? 'Printer' : 'Other Device'}</p>
                  {selectedBluetoothDevice.info && (
                    <p><span className="font-medium">Info:</span> {selectedBluetoothDevice.info}</p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {detectedNfcCard && (
            <Card>
              <CardHeader>
                <CardTitle>Detected NFC Card</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p><span className="font-medium">Card ID:</span> {detectedNfcCard}</p>
                  <p><span className="font-medium">Detected At:</span> {new Date().toLocaleString()}</p>
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>Device Tips</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium">Bluetooth Scanning</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Make sure your Bluetooth device is powered on and in pairing mode before scanning.
                </p>
              </div>
              <div>
                <h3 className="font-medium">NFC Scanning</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Hold the NFC card close to the back of your device when scanning.
                </p>
              </div>
              <div>
                <h3 className="font-medium">Compatibility</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Web Bluetooth is supported in Chrome, Edge, and Opera. Web NFC is currently only supported in Chrome for Android.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
