/**
 * Social Media Integration for Event Featuring
 *
 * This module provides functions for integrating featured events
 * with social media platforms.
 */

import { db } from '@/lib/prisma';
import { format } from 'date-fns';
import { generateEventUrl } from './utils/events';

// Types for social media
export type SocialPlatform = 'TWITTER' | 'FACEBOOK' | 'INSTAGRAM' | 'LINKEDIN';

export interface SocialMediaPost {
  id: string;
  platform: SocialPlatform;
  content: string;
  imageUrl?: string;
  status: 'DRAFT' | 'SCHEDULED' | 'PUBLISHED' | 'FAILED';
  scheduledDate?: Date;
  publishedDate?: Date;
  eventId?: string;
  engagementMetrics?: {
    likes: number;
    shares: number;
    comments: number;
    clicks: number;
  };
}

/**
 * Generate social media content for a featured event
 *
 * @param eventId The event ID
 * @param platform The social media platform
 * @returns The generated content
 */
export async function generateSocialContent(
  eventId: string,
  platform: SocialPlatform
): Promise<{ content: string; imageUrl?: string }> {
  try {
    // Get the event details
    const event = await db.event.findUnique({
      where: { id: eventId },
      include: {
        user: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!event) {
      throw new Error(`Event not found: ${eventId}`);
    }

    // Generate content based on platform
    let content = '';

    switch (platform) {
      case 'TWITTER':
        content = `🌟 FEATURED EVENT: ${event.title} 🌟\n\n`;
        content += `${event.description?.substring(0, 100)}${event.description && event.description.length > 100 ? '...' : ''}\n\n`;
        content += `📅 ${format(new Date(event.startDate), 'MMM dd, yyyy')}\n`;
        content += `📍 ${event.venue}\n\n`;
        const eventUrl = generateEventUrl({ id: event.id, title: event.title, category: event.category });
        content += `Get tickets now: ${process.env.NEXT_PUBLIC_APP_URL}${eventUrl}\n\n`;
        content += `#Event #${event.category.replace(/[^a-zA-Z0-9]/g, '')} #Featured`;
        break;

      case 'FACEBOOK':
      case 'LINKEDIN':
        content = `🌟 FEATURED EVENT: ${event.title} 🌟\n\n`;
        content += `We're excited to feature this amazing event by ${event.user.name}!\n\n`;
        content += `${event.description?.substring(0, 250)}${event.description && event.description.length > 250 ? '...' : ''}\n\n`;
        content += `📅 Date: ${format(new Date(event.startDate), 'MMMM dd, yyyy')}\n`;
        content += `⏰ Time: ${format(new Date(event.startDate), 'h:mm a')}\n`;
        content += `📍 Location: ${event.venue}\n\n`;
        content += `Don't miss out on this featured event! Get your tickets now:\n`;
        const eventUrlFacebook = generateEventUrl({ id: event.id, title: event.title, category: event.category });
        content += `${process.env.NEXT_PUBLIC_APP_URL}${eventUrlFacebook}\n\n`;
        content += `#Event #${event.category.replace(/[^a-zA-Z0-9]/g, '')} #Featured`;
        break;

      case 'INSTAGRAM':
        content = `🌟 FEATURED EVENT: ${event.title} 🌟\n\n`;
        content += `${event.description?.substring(0, 150)}${event.description && event.description.length > 150 ? '...' : ''}\n\n`;
        content += `📅 ${format(new Date(event.startDate), 'MMMM dd, yyyy')}\n`;
        content += `⏰ ${format(new Date(event.startDate), 'h:mm a')}\n`;
        content += `📍 ${event.venue}\n\n`;
        content += `Link in bio to get tickets!\n\n`;
        content += `#Event #${event.category.replace(/[^a-zA-Z0-9]/g, '')} #Featured`;
        break;
    }

    return {
      content,
      imageUrl: event.imagePath || undefined,
    };
  } catch (error) {
    console.error('Error generating social content:', error);
    throw error;
  }
}

/**
 * Create a social media post for a featured event
 *
 * @param eventId The event ID
 * @param platform The social media platform
 * @param scheduledDate Optional scheduled date
 * @returns The created post
 */
export async function createSocialPost(
  eventId: string,
  platform: SocialPlatform,
  scheduledDate?: Date
): Promise<SocialMediaPost> {
  try {
    // Check if the event is featured
    const featuring = await db.eventFeaturing.findFirst({
      where: {
        eventId,
        status: 'ACTIVE',
        tier: {
          in: ['PREMIUM', 'ELITE'], // Only Premium and Elite tiers get social media promotion
        },
      },
    });

    if (!featuring) {
      throw new Error(`Event is not featured with Premium or Elite tier: ${eventId}`);
    }

    // Generate content
    const { content, imageUrl } = await generateSocialContent(eventId, platform);

    // Create post record
    const post = await db.socialMediaPost.create({
      data: {
        platform,
        content,
        imageUrl,
        status: scheduledDate ? 'SCHEDULED' : 'DRAFT',
        scheduledDate,
        eventId,
      },
    });

    return {
      id: post.id,
      platform: post.platform as SocialPlatform,
      content: post.content,
      imageUrl: post.imageUrl || undefined,
      status: post.status as 'DRAFT' | 'SCHEDULED' | 'PUBLISHED' | 'FAILED',
      scheduledDate: post.scheduledDate || undefined,
      publishedDate: post.publishedDate || undefined,
      eventId: post.eventId || undefined,
    };
  } catch (error) {
    console.error('Error creating social post:', error);
    throw error;
  }
}

/**
 * Publish a social media post
 *
 * @param postId The post ID
 * @returns The updated post
 */
export async function publishSocialPost(postId: string): Promise<SocialMediaPost> {
  try {
    // Get the post
    const post = await db.socialMediaPost.findUnique({
      where: { id: postId },
    });

    if (!post) {
      throw new Error(`Post not found: ${postId}`);
    }

    if (post.status === 'PUBLISHED') {
      throw new Error(`Post already published: ${postId}`);
    }

    // In a real implementation, this would call the social media API
    // For now, we'll just simulate a successful publish

    // Update post status
    const updatedPost = await db.socialMediaPost.update({
      where: { id: postId },
      data: {
        status: 'PUBLISHED',
        publishedDate: new Date(),
      },
    });

    return {
      id: updatedPost.id,
      platform: updatedPost.platform as SocialPlatform,
      content: updatedPost.content,
      imageUrl: updatedPost.imageUrl || undefined,
      status: updatedPost.status as 'DRAFT' | 'SCHEDULED' | 'PUBLISHED' | 'FAILED',
      scheduledDate: updatedPost.scheduledDate || undefined,
      publishedDate: updatedPost.publishedDate || undefined,
      eventId: updatedPost.eventId || undefined,
    };
  } catch (error) {
    console.error('Error publishing social post:', error);

    // Update post status to failed
    await db.socialMediaPost.update({
      where: { id: postId },
      data: {
        status: 'FAILED',
      },
    });

    throw error;
  }
}

/**
 * Get social media posts for a featured event
 *
 * @param eventId The event ID
 * @returns The social media posts
 */
export async function getSocialPostsForEvent(eventId: string): Promise<SocialMediaPost[]> {
  try {
    // Get all posts for the event
    const posts = await db.socialMediaPost.findMany({
      where: { eventId },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return posts.map((post: any) => ({
      id: post.id,
      platform: post.platform as SocialPlatform,
      content: post.content,
      imageUrl: post.imageUrl || undefined,
      status: post.status as 'DRAFT' | 'SCHEDULED' | 'PUBLISHED' | 'FAILED',
      scheduledDate: post.scheduledDate || undefined,
      publishedDate: post.publishedDate || undefined,
      eventId: post.eventId || undefined,
      engagementMetrics: post.engagementMetrics as any,
    }));
  } catch (error) {
    console.error('Error getting social posts for event:', error);
    throw error;
  }
}





