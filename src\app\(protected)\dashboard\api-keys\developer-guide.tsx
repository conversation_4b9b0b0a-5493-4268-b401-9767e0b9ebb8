'use client';

import React from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON> } from '@/components/ui/button';
import Link from 'next/link';

export default function DeveloperGuide() {
  return (
    <div className="mt-8">
      <h2 className="text-2xl font-bold mb-4">Developer Guide</h2>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="authentication">Authentication</TabsTrigger>
          <TabsTrigger value="endpoints">Endpoints</TabsTrigger>
          <TabsTrigger value="examples">Examples</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>API Overview</CardTitle>
              <CardDescription>
                The QuickTime Events API allows you to access event data and functionality programmatically.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                Our API provides access to event data, ticket information, and more. You can use it to:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li>Retrieve event listings and details</li>
                <li>Access ticket information</li>
                <li>Create and manage events (with authentication)</li>
                <li>Process orders and payments (with authentication)</li>
              </ul>
              <p>
                The API is RESTful and returns data in JSON format. All API requests should be made to the base URL:
              </p>
              <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md overflow-x-auto">
                <code>https://yourdomain.com/api</code>
              </pre>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="authentication" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Authentication</CardTitle>
              <CardDescription>
                Learn how to authenticate your API requests
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <h3 className="text-lg font-semibold">API Key Authentication</h3>
              <p>
                Most API endpoints require authentication using an API key. You can generate API keys on this page.
              </p>
              <p>
                To authenticate your requests, include your API key in the <code className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded">X-API-Key</code> header:
              </p>
              <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md overflow-x-auto">
                <code>{`X-API-Key: your-api-key`}</code>
              </pre>

              <h3 className="text-lg font-semibold mt-6">Public Endpoints</h3>
              <p>
                Some endpoints are publicly accessible and don&apos;t require authentication:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li><code className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded">/api/events/published</code> - Get published events</li>
                <li><code className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded">/api/eventdetails/:id</code> - Get event details</li>
                <li><code className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded">/api/test</code> - Test API endpoint</li>
              </ul>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="endpoints" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>API Endpoints</CardTitle>
              <CardDescription>
                Explore the available API endpoints
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold">Public Endpoints</h3>
                <div className="mt-4 space-y-4">
                  <div className="border rounded-md p-4">
                    <h4 className="font-medium">Get Published Events</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Retrieve a list of published events</p>
                    <div className="mt-2">
                      <span className="inline-block bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 text-xs font-semibold rounded">GET</span>
                      <code className="ml-2 text-sm">/api/events/published</code>
                    </div>
                  </div>

                  <div className="border rounded-md p-4">
                    <h4 className="font-medium">Get Event Details</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Retrieve details for a specific event</p>
                    <div className="mt-2">
                      <span className="inline-block bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 text-xs font-semibold rounded">GET</span>
                      <code className="ml-2 text-sm">/api/eventdetails/:id</code>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold">Authenticated Endpoints</h3>
                <div className="mt-4 space-y-4">
                  <div className="border rounded-md p-4">
                    <h4 className="font-medium">Get Events (Authenticated)</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Retrieve events with additional data when authenticated</p>
                    <div className="mt-2">
                      <span className="inline-block bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 text-xs font-semibold rounded">GET</span>
                      <code className="ml-2 text-sm">/api/external/events</code>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Link href="/api/docs" target="_blank">
                <Button variant="outline">View Full Documentation</Button>
              </Link>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="examples" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Code Examples</CardTitle>
              <CardDescription>
                Examples of how to use the API in different programming languages
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold">JavaScript (Fetch API)</h3>
                <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md overflow-x-auto mt-2">
                  <code>{`// Get published events
fetch('https://yourdomain.com/api/events/published')
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));

// Get event details
fetch('https://yourdomain.com/api/eventdetails/event-id')
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));

// Authenticated request
fetch('https://yourdomain.com/api/external/events', {
  headers: {
    'X-API-Key': 'your-api-key'
  }
})
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));`}</code>
                </pre>
              </div>
            </CardContent>
            <CardFooter className="flex gap-4">
              <Link href="/api/docs" target="_blank">
                <Button variant="outline">View API Docs</Button>
              </Link>
              <Link href="/api/docs" target="_blank">
                <Button>Try Interactive Demo</Button>
              </Link>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
