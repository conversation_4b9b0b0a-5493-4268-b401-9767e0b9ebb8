import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { db } from '@/lib/prisma';
import { categorySlugMap, slugCategoryMap } from '@/lib/utils/events';
import EventCard from '@/components/ui/eventform/EventCard';

interface PageProps {
  params: Promise<{ category: string }>;
}

// Generate metadata for category pages
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const { category } = resolvedParams;
  
  const categoryName = slugCategoryMap[category];
  if (!categoryName) {
    return {
      title: 'Category Not Found',
      description: 'The event category you are looking for could not be found.'
    };
  }

  const displayName = categoryName.replace(/_/g, ' ').toLowerCase()
    .replace(/\b\w/g, l => l.toUpperCase());

  return {
    title: `${displayName} Events | Quick Time Events`,
    description: `Discover amazing ${displayName.toLowerCase()} events in your area. Book tickets for conferences, workshops, concerts, and more.`,
    keywords: [displayName.toLowerCase(), 'events', 'tickets', 'booking'],
    openGraph: {
      title: `${displayName} Events`,
      description: `Find and book ${displayName.toLowerCase()} events`,
      type: 'website',
    },
  };
}

export default async function CategoryPage({ params }: PageProps) {
  const resolvedParams = await params;
  const { category } = resolvedParams;

  // Validate category
  const categoryName = slugCategoryMap[category];
  if (!categoryName) {
    notFound();
  }

  try {
    // Fetch events for this category
    const events = await db.event.findMany({
      where: {
        category: categoryName,
        status: 'Published',
        startDate: {
          gte: new Date(), // Only future events
        },
      },
      include: {
        user: {
          select: {
            name: true,
            image: true,
          },
        },
        tickets: {
          select: {
            price: true,
            isAvailable: true,
          },
        },
      },
      orderBy: {
        startDate: 'asc',
      },
    });

    const displayName = categoryName.replace(/_/g, ' ').toLowerCase()
      .replace(/\b\w/g, l => l.toUpperCase());

    return (
      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                {displayName} Events
              </h1>
              <p className="text-xl text-blue-100 mb-8">
                Discover amazing {displayName.toLowerCase()} events and book your tickets today
              </p>
              <div className="flex justify-center items-center space-x-6 text-blue-100">
                <div className="text-center">
                  <div className="text-2xl font-bold">{events.length}</div>
                  <div className="text-sm">Upcoming Events</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {events.filter(e => e.tickets.some(t => t.price === 0)).length}
                  </div>
                  <div className="text-sm">Free Events</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Events Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {events.length > 0 ? (
            <>
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Upcoming {displayName} Events
                </h2>
                <p className="text-gray-600">
                  Browse through our curated selection of {displayName.toLowerCase()} events
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {events.map((event) => (
                  <EventCard
                    key={event.id}
                    event={event}
                    viewType="grid"
                    showActions={false}
                  />
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  No {displayName} Events Found
                </h3>
                <p className="text-gray-600 mb-8">
                  There are currently no upcoming {displayName.toLowerCase()} events. 
                  Check back soon or explore other categories.
                </p>
                <a
                  href="/events"
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  Browse All Events
                </a>
              </div>
            </div>
          )}
        </div>

        {/* SEO Content Section */}
        <div className="bg-white py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="prose prose-lg max-w-none">
              <h2>About {displayName} Events</h2>
              <p>
                {displayName} events offer unique opportunities to connect, learn, and experience 
                something special. Whether you're looking for professional development, entertainment, 
                or community engagement, our {displayName.toLowerCase()} events provide memorable experiences.
              </p>
              
              <h3>Why Choose Our {displayName} Events?</h3>
              <ul>
                <li>Carefully curated selection of high-quality events</li>
                <li>Easy online booking and secure payment processing</li>
                <li>Detailed event information and venue details</li>
                <li>Customer support throughout your event experience</li>
              </ul>

              <h3>Popular {displayName} Event Types</h3>
              <p>
                Our platform features a wide variety of {displayName.toLowerCase()} events including 
                workshops, seminars, networking sessions, and special presentations. Each event is 
                designed to provide value and create lasting memories.
              </p>
            </div>
          </div>
        </div>
      </div>
    );

  } catch (error) {
    console.error('Error fetching category events:', error);
    notFound();
  }
}

// Generate static params for popular categories
export async function generateStaticParams() {
  return Object.values(categorySlugMap).map((slug) => ({
    category: slug,
  }));
}
