'use client';

import React, { useState, useEffect } from 'react';
import { 
  MessageSquare, 
  Users, 
  Plus, 
  Crown, 
  Lock, 
  Globe,
  Hash,
  MoreVertical
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { EliteTier, ChatRoomType } from '@prisma/client';
import { hasFeature } from '@/config/elite-pricing';
import { toast } from 'sonner';

interface ChatRoom {
  id: string;
  name: string;
  description?: string;
  roomType: ChatRoomType;
  isPrivate: boolean;
  maxMembers?: number;
  createdBy: {
    id: string;
    name?: string;
    image?: string;
  };
  members: Array<{
    id: string;
    userId: string;
    role: string;
    user: {
      id: string;
      name?: string;
      image?: string;
    };
  }>;
  _count: {
    messages: number;
    members: number;
  };
}

interface ChatRoomsListProps {
  eventId: string;
  currentUser: {
    id: string;
    name?: string;
    image?: string;
  };
  userTier: EliteTier;
  onRoomSelect: (roomId: string) => void;
  selectedRoomId?: string;
}

export default function ChatRoomsList({
  eventId,
  currentUser,
  userTier,
  onRoomSelect,
  selectedRoomId
}: ChatRoomsListProps) {
  const [chatRooms, setChatRooms] = useState<ChatRoom[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newRoom, setNewRoom] = useState({
    name: '',
    description: '',
    roomType: 'GENERAL' as ChatRoomType,
    isPrivate: false,
    maxMembers: ''
  });

  useEffect(() => {
    loadChatRooms();
  }, [eventId]);

  const loadChatRooms = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/messaging/chat-rooms?eventId=${eventId}`);
      const data = await response.json();

      if (response.ok) {
        setChatRooms(data.chatRooms);
      } else {
        toast.error(data.error || 'Failed to load chat rooms');
      }
    } catch (error) {
      console.error('Error loading chat rooms:', error);
      toast.error('Failed to load chat rooms');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateRoom = async () => {
    try {
      const roomData = {
        eventId,
        name: newRoom.name,
        description: newRoom.description || undefined,
        roomType: newRoom.roomType,
        isPrivate: newRoom.isPrivate,
        maxMembers: newRoom.maxMembers ? parseInt(newRoom.maxMembers) : undefined
      };

      const response = await fetch('/api/messaging/chat-rooms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(roomData)
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Chat room created successfully');
        setShowCreateDialog(false);
        setNewRoom({
          name: '',
          description: '',
          roomType: 'GENERAL',
          isPrivate: false,
          maxMembers: ''
        });
        loadChatRooms();
      } else {
        toast.error(data.error || 'Failed to create chat room');
      }
    } catch (error) {
      console.error('Error creating chat room:', error);
      toast.error('Failed to create chat room');
    }
  };

  const handleJoinRoom = async (roomId: string) => {
    try {
      const response = await fetch(`/api/messaging/chat-rooms?roomId=${roomId}&action=join`, {
        method: 'PUT'
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Joined chat room successfully');
        loadChatRooms();
      } else {
        toast.error(data.error || 'Failed to join chat room');
      }
    } catch (error) {
      console.error('Error joining chat room:', error);
      toast.error('Failed to join chat room');
    }
  };

  const handleLeaveRoom = async (roomId: string) => {
    try {
      const response = await fetch(`/api/messaging/chat-rooms?roomId=${roomId}&action=leave`, {
        method: 'PUT'
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Left chat room successfully');
        loadChatRooms();
      } else {
        toast.error(data.error || 'Failed to leave chat room');
      }
    } catch (error) {
      console.error('Error leaving chat room:', error);
      toast.error('Failed to leave chat room');
    }
  };

  const getRoomIcon = (roomType: ChatRoomType) => {
    switch (roomType) {
      case 'ELITE_EXCLUSIVE':
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'ELITE_PRO_EXCLUSIVE':
        return <Crown className="h-4 w-4 text-purple-500" />;
      default:
        return <Hash className="h-4 w-4 text-gray-500" />;
    }
  };

  const getRoomTypeLabel = (roomType: ChatRoomType) => {
    switch (roomType) {
      case 'ELITE_EXCLUSIVE':
        return 'Elite';
      case 'ELITE_PRO_EXCLUSIVE':
        return 'Elite Pro';
      default:
        return 'General';
    }
  };

  const isUserMember = (room: ChatRoom) => {
    return room.members.some(member => member.userId === currentUser.id);
  };

  const canAccessRoom = (roomType: ChatRoomType) => {
    if (roomType === 'ELITE_PRO_EXCLUSIVE') {
      return userTier === EliteTier.ELITE_PRO;
    }
    if (roomType === 'ELITE_EXCLUSIVE') {
      return userTier === EliteTier.ELITE || userTier === EliteTier.ELITE_PRO;
    }
    return true;
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map(i => (
          <div key={i} className="animate-pulse">
            <div className="h-20 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Chat Rooms</h3>
        {hasFeature(userTier, 'exclusiveChatRooms') && (
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Create Room
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Chat Room</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Room Name</Label>
                  <Input
                    id="name"
                    value={newRoom.name}
                    onChange={(e) => setNewRoom(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter room name"
                  />
                </div>
                
                <div>
                  <Label htmlFor="description">Description (Optional)</Label>
                  <Textarea
                    id="description"
                    value={newRoom.description}
                    onChange={(e) => setNewRoom(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe the purpose of this room"
                  />
                </div>
                
                <div>
                  <Label htmlFor="roomType">Room Type</Label>
                  <Select
                    value={newRoom.roomType}
                    onValueChange={(value) => setNewRoom(prev => ({ ...prev, roomType: value as ChatRoomType }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="GENERAL">General</SelectItem>
                      <SelectItem value="ELITE_EXCLUSIVE">Elite Exclusive</SelectItem>
                      {userTier === EliteTier.ELITE_PRO && (
                        <SelectItem value="ELITE_PRO_EXCLUSIVE">Elite Pro Exclusive</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch
                    id="private"
                    checked={newRoom.isPrivate}
                    onCheckedChange={(checked) => setNewRoom(prev => ({ ...prev, isPrivate: checked }))}
                  />
                  <Label htmlFor="private">Private Room</Label>
                </div>
                
                <div>
                  <Label htmlFor="maxMembers">Max Members (Optional)</Label>
                  <Input
                    id="maxMembers"
                    type="number"
                    value={newRoom.maxMembers}
                    onChange={(e) => setNewRoom(prev => ({ ...prev, maxMembers: e.target.value }))}
                    placeholder="Leave empty for unlimited"
                  />
                </div>
                
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateRoom} disabled={!newRoom.name.trim()}>
                    Create Room
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>

      <div className="space-y-2">
        {chatRooms.map(room => {
          const isMember = isUserMember(room);
          const canAccess = canAccessRoom(room.roomType);
          
          return (
            <Card 
              key={room.id} 
              className={`cursor-pointer transition-colors hover:bg-gray-50 ${
                selectedRoomId === room.id ? 'ring-2 ring-blue-500' : ''
              }`}
              onClick={() => isMember && onRoomSelect(room.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getRoomIcon(room.roomType)}
                    <div>
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium">{room.name}</h4>
                        {room.isPrivate && <Lock className="h-3 w-3 text-gray-400" />}
                        <Badge variant="secondary" className="text-xs">
                          {getRoomTypeLabel(room.roomType)}
                        </Badge>
                      </div>
                      {room.description && (
                        <p className="text-sm text-gray-600 mt-1">{room.description}</p>
                      )}
                      <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                        <span className="flex items-center">
                          <Users className="h-3 w-3 mr-1" />
                          {room._count.members} members
                        </span>
                        <span className="flex items-center">
                          <MessageSquare className="h-3 w-3 mr-1" />
                          {room._count.messages} messages
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {!canAccess ? (
                      <Badge variant="destructive" className="text-xs">
                        Upgrade Required
                      </Badge>
                    ) : !isMember ? (
                      <Button size="sm" onClick={(e) => {
                        e.stopPropagation();
                        handleJoinRoom(room.id);
                      }}>
                        Join
                      </Button>
                    ) : (
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={(e) => {
                          e.stopPropagation();
                          handleLeaveRoom(room.id);
                        }}
                      >
                        Leave
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {chatRooms.length === 0 && (
        <div className="text-center py-12">
          <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            No Chat Rooms Yet
          </h3>
          <p className="text-gray-600 mb-4">
            Create or join chat rooms to start group conversations.
          </p>
          {hasFeature(userTier, 'exclusiveChatRooms') && (
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create First Room
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
