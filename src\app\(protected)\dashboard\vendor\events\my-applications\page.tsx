'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
} from '@/components/ui/card';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { toast } from '@/components/ui/use-toast';
import { 
  Calendar, 
  Search, 
  Loader2, 
  MapPin, 
  Clock, 
  CheckCircle, 
  XCircle, 
  Store,
  Filter,
  CalendarDays,
  Info,
  AlertCircle
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from '@/components/ui/label';
import { formatDistanceToNow } from 'date-fns';

interface VendorApplication {
  id: string;
  eventId: string;
  eventTitle: string;
  eventStartDate: string;
  eventEndDate: string;
  eventVenue: string;
  eventLocation: string;
  eventCategory: string;
  eventStatus: string;
  status: string;
  notes?: string;
  boothNumber?: string;
  rejectionReason?: string;
  createdAt: string;
  updatedAt: string;
}

export default function MyApplicationsPage() {
  const router = useRouter();
  const [applications, setApplications] = useState<VendorApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedApplication, setSelectedApplication] = useState<VendorApplication | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  useEffect(() => {
    async function fetchApplications() {
      try {
        setLoading(true);
        
        const response = await fetch('/api/vendors/events/applications');
        
        if (!response.ok) {
          throw new Error('Failed to fetch applications');
        }
        
        const data = await response.json();
        setApplications(data.applications || []);
        
      } catch (err) {
        console.error('Error fetching applications:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
        toast({
          title: 'Error',
          description: 'Failed to load your applications',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }
    
    fetchApplications();
  }, []);

  const filteredApplications = applications.filter(app => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        app.eventTitle.toLowerCase().includes(query) ||
        app.eventVenue.toLowerCase().includes(query) ||
        app.eventLocation.toLowerCase().includes(query) ||
        app.eventCategory.toLowerCase().includes(query) ||
        app.status.toLowerCase().includes(query)
      );
    }
    
    return true;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            Pending
          </Badge>
        );
      case 'APPROVED':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            Approved
          </Badge>
        );
      case 'REJECTED':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            Rejected
          </Badge>
        );
      case 'CANCELLED':
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            Cancelled
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        );
    }
  };

  const viewApplicationDetails = (application: VendorApplication) => {
    setSelectedApplication(application);
    setIsDialogOpen(true);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading your applications...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="w-full max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle className="text-red-500 flex items-center">
            <XCircle className="mr-2 h-5 w-5" />
            Error Loading Applications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p>{error}</p>
          <Button 
            variant="outline" 
            className="mt-4"
            onClick={() => router.push('/dashboard/vendor')}
          >
            Return to Dashboard
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <h1 className="text-3xl font-bold">My Applications</h1>
          <p className="text-gray-600 mt-1">
            Track the status of your vendor applications
          </p>
        </div>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Application Status</CardTitle>
          <CardDescription>
            View and manage your event applications
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
            <div className="relative w-full md:w-1/3">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                placeholder="Search applications..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => router.push('/dashboard/vendor/events/applications')}
                size="sm"
              >
                <Store className="mr-1 h-4 w-4" />
                Apply for Events
              </Button>
            </div>
          </div>
          
          {filteredApplications.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Store className="h-12 w-12 mx-auto text-gray-300 mb-4" />
              <h3 className="text-lg font-medium mb-2">No Applications Found</h3>
              <p className="mb-4">You haven't applied to any events yet</p>
              <Button 
                onClick={() => router.push('/dashboard/vendor/events/applications')}
              >
                Apply for Events
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Event</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Applied</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredApplications.map((application) => (
                    <TableRow key={application.id}>
                      <TableCell className="font-medium">{application.eventTitle}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1 text-gray-500" />
                          <span>
                            {new Date(application.eventStartDate).toLocaleDateString()} - {new Date(application.eventEndDate).toLocaleDateString()}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-1 text-gray-500" />
                          <span>{application.eventVenue}, {application.eventLocation}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1 text-gray-500" />
                          <span>{formatDistanceToNow(new Date(application.createdAt), { addSuffix: true })}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(application.status)}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => viewApplicationDetails(application)}
                        >
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Application Details Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[525px]">
          {selectedApplication && (
            <>
              <DialogHeader>
                <DialogTitle>Application Details</DialogTitle>
                <DialogDescription>
                  Details of your application for {selectedApplication.eventTitle}
                </DialogDescription>
              </DialogHeader>
              <div className="py-4">
                <div className="mb-4">
                  <h3 className="font-medium text-lg">{selectedApplication.eventTitle}</h3>
                  <div className="flex items-center text-sm text-gray-500 mt-1">
                    <Calendar className="h-4 w-4 mr-1" />
                    <span>
                      {new Date(selectedApplication.eventStartDate).toLocaleDateString()} - {new Date(selectedApplication.eventEndDate).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex items-center text-sm text-gray-500 mt-1">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span>{selectedApplication.eventVenue}, {selectedApplication.eventLocation}</span>
                  </div>
                </div>
                
                <div className="space-y-4 mt-6">
                  <div className="flex justify-between items-center">
                    <Label>Application Status</Label>
                    {getStatusBadge(selectedApplication.status)}
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Application Date</Label>
                    <div className="flex items-center text-sm">
                      <Clock className="h-4 w-4 mr-1 text-gray-500" />
                      <span>{new Date(selectedApplication.createdAt).toLocaleString()}</span>
                    </div>
                  </div>
                  
                  {selectedApplication.notes && (
                    <div className="space-y-2">
                      <Label>Your Notes</Label>
                      <div className="p-3 bg-gray-50 rounded-md text-sm">
                        {selectedApplication.notes}
                      </div>
                    </div>
                  )}
                  
                  {selectedApplication.status === 'APPROVED' && selectedApplication.boothNumber && (
                    <div className="space-y-2 bg-green-50 p-4 rounded-md">
                      <div className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-green-700">Application Approved</h4>
                          <p className="text-green-600 text-sm mt-1">
                            Your application has been approved. You have been assigned booth number:
                          </p>
                          <p className="text-green-800 font-bold text-lg mt-1">
                            {selectedApplication.boothNumber}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {selectedApplication.status === 'REJECTED' && (
                    <div className="space-y-2 bg-red-50 p-4 rounded-md">
                      <div className="flex items-start">
                        <XCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-red-700">Application Rejected</h4>
                          {selectedApplication.rejectionReason ? (
                            <>
                              <p className="text-red-600 text-sm mt-1">
                                Reason for rejection:
                              </p>
                              <p className="text-red-800 mt-1">
                                {selectedApplication.rejectionReason}
                              </p>
                            </>
                          ) : (
                            <p className="text-red-600 text-sm mt-1">
                              No reason provided.
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {selectedApplication.status === 'PENDING' && (
                    <div className="space-y-2 bg-yellow-50 p-4 rounded-md">
                      <div className="flex items-start">
                        <AlertCircle className="h-5 w-5 text-yellow-500 mr-2 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-yellow-700">Application Pending</h4>
                          <p className="text-yellow-600 text-sm mt-1">
                            Your application is currently under review by the event organizer.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <DialogFooter>
                <div className="flex items-center text-sm text-gray-500 mr-auto">
                  <Info className="h-4 w-4 mr-1" />
                  <span>
                    Last updated {formatDistanceToNow(new Date(selectedApplication.updatedAt), { addSuffix: true })}
                  </span>
                </div>
                <Button 
                  variant="outline" 
                  onClick={() => setIsDialogOpen(false)}
                >
                  Close
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
