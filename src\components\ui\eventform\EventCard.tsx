'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { format } from 'date-fns';
import {
  Calendar,
  MapPin,
  Clock,
  Tag,
  Users,
  Edit,
  Trash2,
  Eye,
  MoreHorizontal,
  AlertTriangle,
  CheckCircle2,
  Clock4,
  Send,
  LayoutGrid
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { generateEventUrl } from '@/lib/utils/events';

// Define event status type
type EventStatus = 'Published' | 'Draft' | 'Cancelled' | 'Approved' | 'Rejected' | 'UnderReview' | 'Pending' | 'Completed';

// Define event interface
interface Event {
  id: string;
  title: string;
  description: string;
  status: EventStatus;
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  category: string;
  location: string;
  venue: string;
  imagePath?: string | null;
  isFree?: boolean;
  regularPrice?: number;
  vipPrice?: number;
  userId: string;
  user: {
    name: string;
  };
  createdAt: string;
  teamId?: string | null;
  team?: {
    id: string;
    name: string;
    owner?: {
      id: string;
      name: string;
    };
  } | null;
  _count?: {
    tickets?: number;
    attendees?: number;
  };
}

interface EventCardProps {
  event: Event;
  currentUserId: string;
  onDelete: (id: string) => void;
  viewMode?: 'grid' | 'list';
}

const EventCard: React.FC<EventCardProps> = ({
  event,
  currentUserId,
  onDelete,
  viewMode = 'grid'
}) => {
  const isOwner = currentUserId === event.userId;
  const [isPublishing, setIsPublishing] = useState(false);

  // Format date for display
  const formatEventDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (error) {
      return dateString;
    }
  };

  // Get status badge color
  const getStatusBadge = (status: EventStatus) => {
    switch (status) {
      case 'Published':
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle2 className="w-3 h-3 mr-1" />
            Published
          </Badge>
        );
      case 'Draft':
        return (
          <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
            <Clock4 className="w-3 h-3 mr-1" />
            Draft
          </Badge>
        );
      case 'Approved':
        return (
          <Badge className="bg-blue-100 text-blue-800 border-blue-200">
            <CheckCircle2 className="w-3 h-3 mr-1" />
            Approved
          </Badge>
        );
      case 'UnderReview':
        return (
          <Badge className="bg-purple-100 text-purple-800 border-purple-200">
            <Clock4 className="w-3 h-3 mr-1" />
            Under Review
          </Badge>
        );
      case 'Pending':
        return (
          <Badge className="bg-orange-100 text-orange-800 border-orange-200">
            <Clock4 className="w-3 h-3 mr-1" />
            Pending
          </Badge>
        );
      case 'Rejected':
        return (
          <Badge className="bg-red-100 text-red-800 border-red-200">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Rejected
          </Badge>
        );
      case 'Cancelled':
        return (
          <Badge className="bg-red-100 text-red-800 border-red-200">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Cancelled
          </Badge>
        );
      case 'Completed':
        return (
          <Badge className="bg-gray-100 text-gray-800 border-gray-200">
            <CheckCircle2 className="w-3 h-3 mr-1" />
            Completed
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        );
    }
  };

  // Handle delete with confirmation
  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this event?')) {
      onDelete(event.id);
    }
  };

  // Handle publishing an approved event
  const handlePublish = async () => {
    if (!isOwner) return;

    try {
      setIsPublishing(true);
      console.log('Publishing event:', event.id);

      const response = await fetch(`/api/events/${event.id}/publish`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log('Publish response status:', response.status);

      const responseData = await response.json();
      console.log('Publish response data:', responseData);

      if (!response.ok) {
        throw new Error(responseData.error || 'Failed to publish event');
      }

      // Show success message
      alert('Event published successfully!');

      // Reload the page to show updated status
      window.location.reload();

    } catch (error) {
      console.error('Error publishing event:', error);
      alert(error instanceof Error ? error.message : 'Failed to publish event');
    } finally {
      setIsPublishing(false);
    }
  };

  // Grid view card
  if (viewMode === 'grid') {
    return (
      <Card className="overflow-hidden h-full flex flex-col transition-all duration-300 hover:shadow-md border border-gray-200">
        {/* Card Header with Image */}
        <div className="relative">
          <div className="h-48 overflow-hidden bg-gray-100">
            {event.imagePath ? (
              <Image
                src={event.imagePath}
                alt={event.title}
                fill
                className="object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-200">
                <Calendar className="h-12 w-12 text-gray-400" />
              </div>
            )}
          </div>

          {/* Status Badge */}
          <div className="absolute top-3 right-3">
            {getStatusBadge(event.status)}
          </div>
        </div>

        {/* Card Content */}
        <CardContent className="flex-grow p-4">
          <h3 className="font-semibold text-lg mb-2 line-clamp-1">{event.title}</h3>
          <p className="text-gray-600 text-sm mb-3 line-clamp-2">{event.description}</p>

          <div className="space-y-2 text-sm">
            <div className="flex items-center text-gray-600">
              <Calendar className="h-4 w-4 mr-2 text-gray-400" />
              <span>{formatEventDate(event.startDate)}</span>
            </div>

            <div className="flex items-center text-gray-600">
              <Clock className="h-4 w-4 mr-2 text-gray-400" />
              <span>{event.startTime} - {event.endTime}</span>
            </div>

            <div className="flex items-center text-gray-600">
              <MapPin className="h-4 w-4 mr-2 text-gray-400" />
              <span className="truncate">{event.venue || event.location}</span>
            </div>

            <div className="flex items-center text-gray-600">
              <Tag className="h-4 w-4 mr-2 text-gray-400" />
              <span>{event.category}</span>
            </div>

            {event.team && (
              <div className="flex items-center text-gray-600">
                <Users className="h-4 w-4 mr-2 text-gray-400" />
                <span className="flex items-center">
                  <span className="truncate">Team: {event.team.name}</span>
                  {event.team.owner && event.userId !== event.team.owner.id && (
                    <Badge variant="outline" className="ml-2 text-xs">
                      Team Event
                    </Badge>
                  )}
                </span>
              </div>
            )}
          </div>
        </CardContent>

        {/* Card Footer */}
        <CardFooter className="p-4 bg-gray-50 border-t border-gray-100">
          <div className="w-full flex justify-between items-center">
            <div className="flex items-center">
              {event._count?.attendees !== undefined && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center text-gray-600 mr-3">
                        <Users className="h-4 w-4 mr-1" />
                        <span className="text-sm">{event._count.attendees}</span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Attendees</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}

              {!event.isFree && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center text-gray-600">
                        <Tag className="h-4 w-4 mr-1" />
                        <span className="text-sm">
                          {event.regularPrice ? `$${event.regularPrice}` : 'Paid'}
                        </span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Starting price</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>

            <div className="flex space-x-2">
              <Link href={generateEventUrl({ id: event.id, title: event.title, category: event.category })}>
                <Button size="sm" variant="outline">
                  <Eye className="h-4 w-4 mr-1" />
                  View
                </Button>
              </Link>
              <Link href={`/dashboard/organizer/events/details/${event.id}`}>
                <Button size="sm" variant="outline">
                  <Edit className="h-4 w-4 mr-1" />
                  Manage
                </Button>
              </Link>

              {isOwner && (
                <>
                  {/* Show Publish button for Approved events */}
                  {event.status === 'Approved' && (
                    <Button
                      size="sm"
                      variant="outline"
                      className="mr-2 bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200"
                      onClick={handlePublish}
                      disabled={isPublishing}
                    >
                      {isPublishing ? (
                        <>
                          <div className="h-4 w-4 mr-1 animate-spin rounded-full border-2 border-current border-t-transparent" />
                          Publishing...
                        </>
                      ) : (
                        <>
                          <Send className="h-4 w-4 mr-1" />
                          Publish
                        </>
                      )}
                    </Button>
                  )}

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button size="sm" variant="ghost">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem asChild>
                        <Link href={`/dashboard/organizer/events/edit/${event.id}`}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/dashboard/organizer/events/${event.id}/seating-setup`}>
                          <LayoutGrid className="h-4 w-4 mr-2" />
                          Seating Setup
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={handleDelete} className="text-red-600">
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </>
              )}
            </div>
          </div>
        </CardFooter>
      </Card>
    );
  }

  // List view card
  return (
    <Card className="overflow-hidden border border-gray-200 hover:shadow-md transition-all duration-300">
      <div className="flex flex-col sm:flex-row">
        {/* Image Section (for list view) */}
        <div className="relative w-full sm:w-48 h-32 sm:h-auto">
          {event.imagePath ? (
            <Image
              src={event.imagePath}
              alt={event.title}
              fill
              className="object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-200">
              <Calendar className="h-8 w-8 text-gray-400" />
            </div>
          )}
        </div>

        {/* Content Section */}
        <div className="flex-grow p-4">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="font-semibold text-lg mb-1">{event.title}</h3>
              <p className="text-gray-600 text-sm mb-2 line-clamp-1">{event.description}</p>
            </div>
            <div>
              {getStatusBadge(event.status)}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-1 text-sm mt-2">
            <div className="flex items-center text-gray-600">
              <Calendar className="h-4 w-4 mr-2 text-gray-400" />
              <span>{formatEventDate(event.startDate)}</span>
            </div>

            <div className="flex items-center text-gray-600">
              <Clock className="h-4 w-4 mr-2 text-gray-400" />
              <span>{event.startTime} - {event.endTime}</span>
            </div>

            <div className="flex items-center text-gray-600">
              <MapPin className="h-4 w-4 mr-2 text-gray-400" />
              <span className="truncate">{event.venue || event.location}</span>
            </div>

            <div className="flex items-center text-gray-600">
              <Tag className="h-4 w-4 mr-2 text-gray-400" />
              <span>{event.category}</span>
            </div>

            {event.team && (
              <div className="flex items-center text-gray-600">
                <Users className="h-4 w-4 mr-2 text-gray-400" />
                <span className="flex items-center">
                  <span className="truncate">Team: {event.team.name}</span>
                  {event.team.owner && event.userId !== event.team.owner.id && (
                    <Badge variant="outline" className="ml-2 text-xs">
                      Team Event
                    </Badge>
                  )}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Actions Section */}
        <div className="flex sm:flex-col justify-between items-center p-4 bg-gray-50 border-t sm:border-t-0 sm:border-l border-gray-100">
          <div className="hidden sm:flex flex-col items-center justify-center mb-auto mt-2">
            {event._count?.attendees !== undefined && (
              <div className="flex flex-col items-center text-gray-600 mb-2">
                <Users className="h-4 w-4 mb-1" />
                <span className="text-sm font-medium">{event._count.attendees}</span>
                <span className="text-xs">Attendees</span>
              </div>
            )}

            {!event.isFree && event.regularPrice && (
              <div className="text-center">
                <span className="text-sm font-medium">${event.regularPrice}</span>
              </div>
            )}
          </div>

          <div className="flex sm:flex-col space-x-2 sm:space-x-0 sm:space-y-2">
            <Link href={generateEventUrl({ id: event.id, title: event.title, category: event.category })}>
              <Button size="sm" variant="outline" className="w-full">
                <Eye className="h-4 w-4 mr-1 sm:mr-0 sm:mb-1" />
                <span className="hidden sm:inline">View</span>
              </Button>
            </Link>
            <Link href={`/dashboard/organizer/events/details/${event.id}`}>
              <Button size="sm" variant="outline" className="w-full">
                <Edit className="h-4 w-4 mr-1 sm:mr-0 sm:mb-1" />
                <span className="hidden sm:inline">Manage</span>
              </Button>
            </Link>

            {isOwner && (
              <>
                {/* Show Publish button for Approved events */}
                {event.status === 'Approved' && (
                  <Button
                    size="sm"
                    variant="outline"
                    className="w-full bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200"
                    onClick={handlePublish}
                    disabled={isPublishing}
                  >
                    {isPublishing ? (
                      <>
                        <div className="h-4 w-4 mr-1 sm:mr-0 sm:mb-1 animate-spin rounded-full border-2 border-current border-t-transparent" />
                        <span className="hidden sm:inline">Publishing...</span>
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-1 sm:mr-0 sm:mb-1" />
                        <span className="hidden sm:inline">Publish</span>
                      </>
                    )}
                  </Button>
                )}

                <Link href={`/dashboard/organizer/events/edit/${event.id}`}>
                  <Button size="sm" variant="outline" className="w-full">
                    <Edit className="h-4 w-4 mr-1 sm:mr-0 sm:mb-1" />
                    <span className="hidden sm:inline">Edit</span>
                  </Button>
                </Link>

                <Link href={`/dashboard/organizer/events/${event.id}/seating-setup`}>
                  <Button size="sm" variant="outline" className="w-full">
                    <LayoutGrid className="h-4 w-4 mr-1 sm:mr-0 sm:mb-1" />
                    <span className="hidden sm:inline">Seating</span>
                  </Button>
                </Link>

                <Button
                  size="sm"
                  variant="outline"
                  className="w-full text-red-600 hover:text-red-700 hover:bg-red-50"
                  onClick={handleDelete}
                >
                  <Trash2 className="h-4 w-4 mr-1 sm:mr-0 sm:mb-1" />
                  <span className="hidden sm:inline">Delete</span>
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default EventCard;
