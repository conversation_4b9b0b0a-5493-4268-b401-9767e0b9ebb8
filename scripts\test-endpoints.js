/**
 * Comprehensive API Endpoint Testing Script
 * 
 * This script tests all available API endpoints with different HTTP methods.
 * It verifies that the endpoints are accessible and return the expected responses.
 * 
 * Usage:
 * node test-endpoints.js <api-key>
 * 
 * Example:
 * node test-endpoints.js your-api-key
 */

const fetch = require('node-fetch');

// Get command line arguments
const apiKey = process.argv[2];
const baseUrl = process.argv[3] || 'http://localhost:3000';

if (!apiKey) {
  console.error('Please provide an API key as the first argument');
  process.exit(1);
}

// Define all endpoints to test with their methods and expected status codes
const endpoints = [
  // Public endpoints
  { url: '/api/test', method: 'GET', description: 'Test API (GET)', expectedStatus: 200 },
  { url: '/api/test', method: 'POST', description: 'Test API (POST)', expectedStatus: 200, body: { test: 'data' } },
  { url: '/api/events/published', method: 'GET', description: 'Published Events', expectedStatus: 200 },
  { url: '/api/eventdetails', method: 'GET', description: 'Event Details', expectedStatus: 200 },
  
  // Protected endpoints
  { url: '/api/external/events', method: 'GET', description: 'External Events API', expectedStatus: 200 },
  { url: '/api/events', method: 'GET', description: 'Events API', expectedStatus: 200 },
  { url: '/api/events/create', method: 'POST', description: 'Create Event', expectedStatus: 200, body: {
    title: 'Test Event',
    description: 'Test Description',
    startDate: new Date(Date.now() + 86400000).toISOString(), // Tomorrow
    endDate: new Date(Date.now() + 172800000).toISOString(),  // Day after tomorrow
    location: 'Test Location',
    venue: 'Test Venue',
    category: 'MUSIC',
    eventType: 'CONCERT'
  }},
  { url: '/api/tickets', method: 'GET', description: 'Tickets API', expectedStatus: 200 },
  { url: '/api/orders', method: 'GET', description: 'Orders API', expectedStatus: 200 },
  
  // Invalid endpoints (should return 404)
  { url: '/api/nonexistent', method: 'GET', description: 'Nonexistent Endpoint', expectedStatus: 404 },
  
  // Invalid methods (should return 405)
  { url: '/api/test', method: 'PUT', description: 'Test API (PUT - Invalid Method)', expectedStatus: 405 },
  
  // Rate limiting test (make multiple requests to the same endpoint)
  ...Array(5).fill().map((_, i) => ({ 
    url: '/api/test', 
    method: 'GET', 
    description: `Rate Limit Test ${i+1}`, 
    expectedStatus: 200 
  }))
];

console.log(`Testing API endpoints with key: ${apiKey}`);
console.log(`Base URL: ${baseUrl}`);
console.log('---------------------------------------------------');

// Function to make a request to an endpoint
async function testEndpoint(endpoint) {
  try {
    console.log(`Testing: ${endpoint.description}`);
    console.log(`URL: ${baseUrl}${endpoint.url}`);
    console.log(`Method: ${endpoint.method}`);
    
    const options = {
      method: endpoint.method,
      headers: {
        'X-API-Key': apiKey,
        'Content-Type': 'application/json'
      }
    };
    
    if (endpoint.body) {
      options.body = JSON.stringify(endpoint.body);
      console.log(`Body: ${JSON.stringify(endpoint.body, null, 2)}`);
    }
    
    const startTime = Date.now();
    const response = await fetch(`${baseUrl}${endpoint.url}`, options);
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    const status = response.status;
    
    // Get rate limit headers
    const rateLimit = response.headers.get('X-RateLimit-Limit');
    const rateLimitRemaining = response.headers.get('X-RateLimit-Remaining');
    const rateLimitReset = response.headers.get('X-RateLimit-Reset');
    
    // Format the reset time
    const resetTime = rateLimitReset ? new Date(parseInt(rateLimitReset) * 1000).toLocaleTimeString() : 'N/A';
    
    // Get response data
    let data;
    try {
      data = await response.json();
    } catch (e) {
      data = await response.text();
    }
    
    console.log(`Status: ${status} (Expected: ${endpoint.expectedStatus}) - Time: ${responseTime}ms`);
    
    if (rateLimit) {
      console.log(`Rate Limit: ${rateLimit} - Remaining: ${rateLimitRemaining} - Reset: ${resetTime}`);
    }
    
    if (status !== endpoint.expectedStatus) {
      console.log(`ERROR: Unexpected status code. Expected ${endpoint.expectedStatus}, got ${status}`);
      console.log(`Response: ${JSON.stringify(data, null, 2)}`);
    } else {
      console.log(`SUCCESS: Status code matches expected value`);
      
      // Print a sample of the response data
      if (typeof data === 'object') {
        console.log(`Response: ${JSON.stringify(data, null, 2).substring(0, 200)}...`);
      } else if (typeof data === 'string') {
        console.log(`Response: ${data.substring(0, 200)}...`);
      }
    }
    
    console.log('---------------------------------------------------');
    
    return { 
      endpoint: endpoint.url, 
      method: endpoint.method,
      status, 
      expectedStatus: endpoint.expectedStatus,
      success: status === endpoint.expectedStatus,
      responseTime,
      rateLimit,
      rateLimitRemaining
    };
  } catch (error) {
    console.error(`Error testing endpoint ${endpoint.url}: ${error.message}`);
    console.log('---------------------------------------------------');
    return { 
      endpoint: endpoint.url, 
      method: endpoint.method,
      error: error.message, 
      success: false,
      expectedStatus: endpoint.expectedStatus
    };
  }
}

// Function to delay execution
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Main function to run the tests
async function runTests() {
  console.log('Starting endpoint tests...');
  
  const results = [];
  
  for (const endpoint of endpoints) {
    const result = await testEndpoint(endpoint);
    results.push(result);
    
    // Add a small delay between requests to avoid overwhelming the server
    await delay(500);
  }
  
  // Print summary
  console.log('Endpoint Test Results:');
  console.log('---------------------------------------------------');
  
  const successCount = results.filter(r => r.success).length;
  const failureCount = results.length - successCount;
  
  console.log(`Total Tests: ${results.length}`);
  console.log(`Successful: ${successCount}`);
  console.log(`Failed: ${failureCount}`);
  
  if (failureCount > 0) {
    console.log('\nFailed Tests:');
    results.filter(r => !r.success).forEach(result => {
      console.log(`  - ${result.method} ${result.endpoint}: Expected ${result.expectedStatus}, got ${result.status || 'error'}`);
    });
  }
  
  // Check for rate limiting
  const rateLimitedResults = results.filter(r => r.status === 429);
  if (rateLimitedResults.length > 0) {
    console.log('\nRate Limited Requests:');
    rateLimitedResults.forEach(result => {
      console.log(`  - ${result.method} ${result.endpoint}`);
    });
  }
  
  // Calculate average response time
  const avgResponseTime = results
    .filter(r => r.responseTime)
    .reduce((sum, r) => sum + r.responseTime, 0) / 
    results.filter(r => r.responseTime).length;
  
  console.log(`\nAverage Response Time: ${avgResponseTime.toFixed(2)}ms`);
  
  console.log('---------------------------------------------------');
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
});
