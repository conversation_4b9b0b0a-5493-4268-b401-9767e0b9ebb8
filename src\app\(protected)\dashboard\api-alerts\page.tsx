'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, AlertTriangle, CheckCircle, Clock, Filter, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ApiAlert {
  id: string;
  type: string;
  severity: string;
  message: string;
  details: string;
  timestamp: string;
  apiKeyId: string | null;
  userId: string | null;
  resolved: boolean;
  resolvedAt: string | null;
  resolvedBy: string | null;
  resolution: string | null;
}

export default function ApiAlertsPage() {
  const [loading, setLoading] = useState(true);
  const [alerts, setAlerts] = useState<ApiAlert[]>([]);
  const [selectedAlert, setSelectedAlert] = useState<ApiAlert | null>(null);
  const [resolutionNote, setResolutionNote] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'resolved'>('all');
  const [filterSeverity, setFilterSeverity] = useState<'all' | 'low' | 'medium' | 'high' | 'critical'>('all');

  const fetchAlerts = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/api-alerts?status=${filterStatus}&severity=${filterSeverity}`);

      if (!response.ok) {
        throw new Error('Failed to fetch alerts');
      }

      const data = await response.json();
      setAlerts(data);
    } catch (error) {
      console.error('Error fetching alerts:', error);
      toast.error('Failed to fetch alerts');
    } finally {
      setLoading(false);
    }
  }, [filterStatus, filterSeverity]);

  // Fetch alerts
  useEffect(() => {
    fetchAlerts();
  }, [fetchAlerts]);

  // Resolve an alert
  const resolveAlert = async (alertId: string) => {
    try {
      const response = await fetch(`/api/api-alerts/${alertId}/resolve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          resolution: resolutionNote,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to resolve alert');
      }

      toast.success('Alert resolved successfully');
      setResolutionNote('');
      fetchAlerts();
    } catch (error) {
      console.error('Error resolving alert:', error);
      toast.error('Failed to resolve alert');
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  // Get severity color
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'critical':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get alert type display name
  const getAlertTypeDisplay = (type: string) => {
    switch (type) {
      case 'high_error_rate':
        return 'High Error Rate';
      case 'unusual_traffic':
        return 'Unusual Traffic';
      case 'rate_limit_exceeded':
        return 'Rate Limit Exceeded';
      case 'suspicious_ip':
        return 'Suspicious IP';
      case 'unusual_endpoint_access':
        return 'Unusual Endpoint Access';
      case 'brute_force_attempt':
        return 'Brute Force Attempt';
      default:
        return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  // Filter alerts
  const filteredAlerts = alerts.filter(alert => {
    if (filterStatus !== 'all' &&
        ((filterStatus === 'active' && alert.resolved) ||
         (filterStatus === 'resolved' && !alert.resolved))) {
      return false;
    }

    if (filterSeverity !== 'all' && alert.severity !== filterSeverity) {
      return false;
    }

    return true;
  });

  // Render loading state
  if (loading && alerts.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">API Alerts</h1>
        <div className="flex gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <Select value={filterStatus} onValueChange={(value: any) => setFilterStatus(value)}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Alerts</SelectItem>
                <SelectItem value="active">Active Alerts</SelectItem>
                <SelectItem value="resolved">Resolved Alerts</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4 text-gray-500" />
            <Select value={filterSeverity} onValueChange={(value: any) => setFilterSeverity(value)}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Filter by severity" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Severities</SelectItem>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="critical">Critical</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button variant="outline" onClick={fetchAlerts} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all">All Alerts</TabsTrigger>
          <TabsTrigger value="active">Active Alerts</TabsTrigger>
          <TabsTrigger value="resolved">Resolved Alerts</TabsTrigger>
          <TabsTrigger value="critical">Critical Alerts</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6">
          <AlertsList
            alerts={filteredAlerts}
            setSelectedAlert={setSelectedAlert}
            getSeverityColor={getSeverityColor}
            getAlertTypeDisplay={getAlertTypeDisplay}
            formatDate={formatDate}
          />
        </TabsContent>

        <TabsContent value="active" className="mt-6">
          <AlertsList
            alerts={filteredAlerts.filter(alert => !alert.resolved)}
            setSelectedAlert={setSelectedAlert}
            getSeverityColor={getSeverityColor}
            getAlertTypeDisplay={getAlertTypeDisplay}
            formatDate={formatDate}
          />
        </TabsContent>

        <TabsContent value="resolved" className="mt-6">
          <AlertsList
            alerts={filteredAlerts.filter(alert => alert.resolved)}
            setSelectedAlert={setSelectedAlert}
            getSeverityColor={getSeverityColor}
            getAlertTypeDisplay={getAlertTypeDisplay}
            formatDate={formatDate}
          />
        </TabsContent>

        <TabsContent value="critical" className="mt-6">
          <AlertsList
            alerts={filteredAlerts.filter(alert => alert.severity === 'critical' || alert.severity === 'high')}
            setSelectedAlert={setSelectedAlert}
            getSeverityColor={getSeverityColor}
            getAlertTypeDisplay={getAlertTypeDisplay}
            formatDate={formatDate}
          />
        </TabsContent>
      </Tabs>

      {/* Alert Details Dialog */}
      {selectedAlert && (
        <Dialog open={!!selectedAlert} onOpenChange={(open) => !open && setSelectedAlert(null)}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <AlertTriangle className={`h-5 w-5 ${
                  selectedAlert.severity === 'critical' ? 'text-purple-500' :
                  selectedAlert.severity === 'high' ? 'text-red-500' :
                  selectedAlert.severity === 'medium' ? 'text-yellow-500' :
                  'text-green-500'
                }`} />
                {getAlertTypeDisplay(selectedAlert.type)}
                <Badge className={`ml-2 ${getSeverityColor(selectedAlert.severity)}`}>
                  {selectedAlert.severity.toUpperCase()}
                </Badge>
              </DialogTitle>
              <DialogDescription>
                Alert ID: {selectedAlert.id}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Message</h3>
                <p className="mt-1">{selectedAlert.message}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">Details</h3>
                <pre className="mt-1 p-3 bg-gray-100 rounded-md text-xs overflow-auto">
                  {JSON.stringify(JSON.parse(selectedAlert.details), null, 2)}
                </pre>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Timestamp</h3>
                  <p className="mt-1">{formatDate(selectedAlert.timestamp)}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Status</h3>
                  <p className="mt-1 flex items-center gap-2">
                    {selectedAlert.resolved ? (
                      <>
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        Resolved
                      </>
                    ) : (
                      <>
                        <Clock className="h-4 w-4 text-yellow-500" />
                        Active
                      </>
                    )}
                  </p>
                </div>
              </div>

              {selectedAlert.apiKeyId && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">API Key ID</h3>
                  <p className="mt-1">{selectedAlert.apiKeyId}</p>
                </div>
              )}

              {selectedAlert.userId && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">User ID</h3>
                  <p className="mt-1">{selectedAlert.userId}</p>
                </div>
              )}

              {selectedAlert.resolved && (
                <div className="border-t pt-4 mt-4">
                  <h3 className="text-sm font-medium text-gray-500">Resolution</h3>
                  <p className="mt-1">{selectedAlert.resolution || 'No resolution notes provided.'}</p>

                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Resolved At</h3>
                      <p className="mt-1">{selectedAlert.resolvedAt ? formatDate(selectedAlert.resolvedAt) : 'N/A'}</p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Resolved By</h3>
                      <p className="mt-1">{selectedAlert.resolvedBy || 'N/A'}</p>
                    </div>
                  </div>
                </div>
              )}

              {!selectedAlert.resolved && (
                <div className="border-t pt-4 mt-4">
                  <Label htmlFor="resolution-note">Resolution Note</Label>
                  <Textarea
                    id="resolution-note"
                    placeholder="Enter notes about how this alert was resolved..."
                    value={resolutionNote}
                    onChange={(e) => setResolutionNote(e.target.value)}
                    className="mt-2"
                  />
                </div>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setSelectedAlert(null)}>
                Close
              </Button>

              {!selectedAlert.resolved && (
                <Button onClick={() => resolveAlert(selectedAlert.id)}>
                  Mark as Resolved
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

// AlertsList component
function AlertsList({
  alerts,
  setSelectedAlert,
  getSeverityColor,
  getAlertTypeDisplay,
  formatDate
}: {
  alerts: ApiAlert[],
  setSelectedAlert: (alert: ApiAlert) => void,
  getSeverityColor: (severity: string) => string,
  getAlertTypeDisplay: (type: string) => string,
  formatDate: (dateString: string) => string
}) {
  if (alerts.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center h-64">
          <CheckCircle className="h-12 w-12 text-gray-300 mb-4" />
          <p className="text-gray-500">No alerts found</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {alerts.map((alert) => (
        <Card key={alert.id} className={`overflow-hidden ${alert.resolved ? 'opacity-75' : ''}`}>
          <div className={`h-1 ${
            alert.severity === 'critical' ? 'bg-purple-500' :
            alert.severity === 'high' ? 'bg-red-500' :
            alert.severity === 'medium' ? 'bg-yellow-500' :
            'bg-green-500'
          }`}></div>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className={`h-5 w-5 ${
                    alert.severity === 'critical' ? 'text-purple-500' :
                    alert.severity === 'high' ? 'text-red-500' :
                    alert.severity === 'medium' ? 'text-yellow-500' :
                    'text-green-500'
                  }`} />
                  {getAlertTypeDisplay(alert.type)}
                </CardTitle>
                <CardDescription>{alert.message}</CardDescription>
              </div>
              <Badge className={getSeverityColor(alert.severity)}>
                {alert.severity.toUpperCase()}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center text-sm">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-gray-400" />
                {formatDate(alert.timestamp)}
              </div>
              <div className="flex items-center gap-2">
                {alert.resolved ? (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Resolved</span>
                  </>
                ) : (
                  <>
                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    <span>Active</span>
                  </>
                )}
              </div>
            </div>
          </CardContent>
          <CardFooter className="bg-gray-50 py-2">
            <Button variant="ghost" size="sm" onClick={() => setSelectedAlert(alert)}>
              View Details
            </Button>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}
