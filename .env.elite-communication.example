# Elite Communication System Environment Variables
# Copy these to your .env.local file and configure with your actual values

# Stripe Price IDs for Elite Communication Tiers
STRIPE_ELITE_PER_EVENT_PRICE_ID=price_1234567890abcdef
STRIPE_ELITE_PRO_PER_EVENT_PRICE_ID=price_1234567890abcdef
STRIPE_ELITE_PRO_MONTHLY_PRICE_ID=price_1234567890abcdef
STRIPE_ELITE_PRO_ANNUAL_PRICE_ID=price_1234567890abcdef

# WebSocket Configuration (Optional - for real-time messaging)
WEBSOCKET_URL=ws://localhost:3001
WEBSOCKET_SECRET=your-websocket-secret-key

# File Upload Configuration (for message attachments)
MAX_FILE_SIZE_MB=50
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf,text/plain

# Rate Limiting Configuration
ELITE_MESSAGE_RATE_LIMIT_PER_HOUR=100
ELITE_PRO_MESSAGE_RATE_LIMIT_PER_HOUR=500
MEETING_REQUEST_RATE_LIMIT_PER_DAY=20

# Email Configuration (for meeting notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Calendar Integration (Optional)
GOOGLE_CALENDAR_CLIENT_ID=your-google-client-id
GOOGLE_CALENDAR_CLIENT_SECRET=your-google-client-secret

# Video Call Integration (Optional)
ZOOM_API_KEY=your-zoom-api-key
ZOOM_API_SECRET=your-zoom-api-secret

# Content Moderation (Optional)
OPENAI_API_KEY=your-openai-api-key
CONTENT_MODERATION_ENABLED=true
