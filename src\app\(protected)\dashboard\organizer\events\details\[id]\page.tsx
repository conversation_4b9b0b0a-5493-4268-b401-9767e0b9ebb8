// app/events/[id]/page.tsx
'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import Image from 'next/image'; // Add Image import
import {
  Calendar, MapPin, Users, Clock, Tag,
  Share2, Edit, Trash2, ArrowLeft, AlertTriangle,
  Car, UserCheck, Send, CheckCircle, Loader2, Star, DollarSign, LayoutGrid
} from 'lucide-react';
import Link from 'next/link';
import { SimpleFeatureButton } from '@/components/organizer/simple-feature-button';

// Types
interface Event {
  id: string;
  title: string;
  description: string;
  status: string;
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  category: string;
  eventType: string;
  location: string;
  venue?: string;
  virtualPlatform?: string;
  virtualLink?: string;
  imagePath?: string;
  isFree?: boolean;
  regularPrice?: number;
  vipPrice?: number;
  regularSeats?: number;
  vipSeats?: number;
  vvipSeats?: number;
  venueCapacity?: number;
  userId: string;
  user: {
    id: string;
    name: string;
    email?: string;
  };
  ageRestriction?: {
    id?: string;
    minAge?: number;
    maxAge?: number;
    ageGroups?: string;
    description?: string;
  } | null;
  ParkingManagement?: {
    id?: string;
    totalSpaces?: number;
    reservedSpaces?: number;
    pricePerHour?: number;
    isFree?: boolean;
    reservationRequired?: boolean;
    description?: string;
  } | null;
  seoSettings?: {
    id?: string;
    title?: string;
    description?: string;
    keywords?: string[];
  } | null;
  socialSettings?: {
    id?: string;
    facebookTitle?: string;
    facebookDescription?: string;
    twitterTitle?: string;
    twitterDescription?: string;
    ogImage?: string;
  } | null;
  tickets?: any[];
  createdAt: string;
  updatedAt?: string;
}

export default function EventDetailPage() {
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();

  const isOrganizer = session?.user?.role === 'ORGANIZER';
  const isOwner = event && session?.user?.id === event.userId;

  // Fetch event details
  useEffect(() => {
    const fetchEvent = async () => {
      try {
        // Use the dashboard API endpoint to get all event details
        const response = await fetch(`/api/dashboard/organiser/events/${params.id}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Event not found');
          }
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json();
        console.log('Event data:', data);
        console.log('Age restriction:', data.ageRestriction);
        console.log('Parking management:', data.ParkingManagement);
        setEvent(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch event details');
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchEvent();
    }
  }, [params.id]);

  // Handle delete event
  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this event?')) return;

    try {
      const response = await fetch(`/api/dashboard/organiser/events/delete?eventId=${event?.id}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }

      router.push('/dashboard/organizer/events/myEvents');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete event');
    }
  };

  // Handle submit event for review
  const handleSubmit = async () => {
    if (!event) return;

    if (!confirm('Are you sure you want to submit this event for review? Once submitted, you cannot edit it until it is approved or rejected.')) return;

    try {
      setIsSubmitting(true);
      setError(null);
      setSubmitSuccess(false);

      console.log('Submitting event with ID:', event.id);
      const response = await fetch(`/api/events/${event.id}/submit`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
      });

      console.log('Response status:', response.status);

      // Get the response as text first
      const responseText = await response.text();
      console.log('Response text length:', responseText.length);

      // Check if it looks like HTML (contains <!DOCTYPE or <html)
      if (responseText.includes('<!DOCTYPE') || responseText.includes('<html')) {
        console.error('Received HTML response instead of JSON');
        throw new Error(`Received HTML instead of JSON. Status: ${response.status}. Please check the server logs.`);
      }

      // Try to parse as JSON
      let data;
      try {
        data = JSON.parse(responseText);
        console.log('Parsed JSON data:', data);
      } catch (parseError) {
        console.error('Failed to parse response as JSON:', parseError);
        throw new Error(`Invalid JSON response. Status: ${response.status}. Response starts with: ${responseText.substring(0, 100)}...`);
      }

      if (!response.ok) {
        const errorMessage = data.error || data.details || `Error: ${response.status}`;
        throw new Error(errorMessage);
      }

      // Update the local event state with the new status from the response
      const newStatus = data.event?.status || 'UnderReview';
      console.log('Setting event status to:', newStatus);

      setEvent(prev => prev ? { ...prev, status: newStatus } : null);
      setSubmitSuccess(true);

      // Show success message for 3 seconds, then refresh the page
      setTimeout(() => {
        console.log('Refreshing page...');
        router.refresh();
      }, 3000);

    } catch (err) {
      console.error('Error submitting event:', err);
      setError(err instanceof Error ? err.message : 'Failed to submit event for review');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Event status badge component
  const StatusBadge = ({ status }: { status: string }) => {
    const getStatusClasses = () => {
      switch (status) {
        case 'Published':
          return 'bg-green-100 text-green-800';
        case 'Draft':
          return 'bg-yellow-100 text-yellow-800';
        case 'Cancelled':
          return 'bg-red-100 text-red-800';
        case 'Pending':
          return 'bg-blue-100 text-blue-800';
        case 'UnderReview':
          return 'bg-purple-100 text-purple-800';
        case 'Approved':
          return 'bg-emerald-100 text-emerald-800';
        case 'Rejected':
          return 'bg-pink-100 text-pink-800';
        case 'Completed':
          return 'bg-gray-100 text-gray-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    };

    // Format status for display (add spaces between camelCase words)
    const formatStatus = (status: string) => {
      return status
        .replace(/([A-Z])/g, ' $1') // Add space before capital letters
        .replace(/^./, (str) => str.toUpperCase()); // Capitalize first letter
    };

    return (
      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusClasses()}`}>
        {formatStatus(status)}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-16 flex justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error || !event) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <AlertTriangle className="mx-auto h-12 w-12 text-red-500 mb-4" />
          <h2 className="text-2xl font-bold text-red-700 mb-2">
            {error || 'Event not found'}
          </h2>
          <p className="text-red-600 mb-6">
            We couldn&apos;t find the event you&apos;re looking for.
          </p>
          <Link href="/events" className="inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            <ArrowLeft className="mr-2 h-5 w-5" />
            Back to Events
          </Link>
        </div>
      </div>
    );
  }

  // Block access to draft events for non-owners
  if (event.status === 'Draft' && !isOwner) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
          <AlertTriangle className="mx-auto h-12 w-12 text-yellow-500 mb-4" />
          <h2 className="text-2xl font-bold text-yellow-700 mb-2">
            Access Restricted
          </h2>
          <p className="text-yellow-600 mb-6">
            This event is currently in draft mode and is only viewable by the organizer.
          </p>
          <Link href="/events" className="inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            <ArrowLeft className="mr-2 h-5 w-5" />
            Back to Events
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back navigation */}
      <div className="mb-6">
        <Link href="/dashboard/organizer/events/myEvents" className="inline-flex items-center text-blue-600 hover:underline">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to My Events
        </Link>
      </div>

      {/* Success message */}
      {submitSuccess && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6 flex items-center">
          <CheckCircle className="h-5 w-5 mr-2 text-green-500" />
          <p>Your event has been successfully submitted for review. You will be notified once it is approved or rejected.</p>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6 flex items-center">
          <AlertTriangle className="h-5 w-5 mr-2 text-red-500" />
          <p>{error}</p>
        </div>
      )}

      {/* Event header */}
      <div className="flex flex-col md:flex-row justify-between mb-6 items-start md:items-center">
        <div>
          <div className="flex items-center gap-3 mb-2">
            <h1 className="text-3xl font-bold">{event.title}</h1>
            <StatusBadge status={event.status} />
          </div>
          <p className="text-gray-600">Organized by {event.user.name}</p>
        </div>

        {/* Actions */}
        {isOwner && (
          <div className="flex gap-3 mt-4 md:mt-0">
            {/* Show Edit button only for Draft status */}
            {event.status === 'Draft' && (
              <Link
                href={`/dashboard/organizer/events/edit/${event.id}`}
                className="inline-flex items-center bg-yellow-500 text-white px-4 py-2 rounded-md hover:bg-yellow-600"
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Link>
            )}

            {/* Show Submit button only for Draft status */}
            {event.status === 'Draft' && (
              <button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <Send className="mr-2 h-4 w-4" />
                    Submit for Review
                  </>
                )}
              </button>
            )}

            {/* Feature button - only for published events */}
            {event.status === 'Published' && (
              <SimpleFeatureButton
                eventId={event.id}
                className="inline-flex items-center bg-yellow-500 text-white px-4 py-2 rounded-md hover:bg-yellow-600"
              />
            )}

            {/* Delete button */}
            <button
              onClick={handleDelete}
              className="inline-flex items-center bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </button>
          </div>
        )}
      </div>

      {/* Main content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Event details */}
        <div className="lg:col-span-2">
          {/* Event image */}
          {event.imagePath ? (
            <div className="relative w-full h-64 mb-6">
              <Image
                src={event.imagePath}
                alt={event.title}
                fill
                sizes="(max-width: 768px) 100vw, 50vw"
                priority
                className="object-cover rounded-lg"
              />
            </div>
          ) : (
            <div className="w-full h-64 bg-gray-200 rounded-lg mb-6 flex items-center justify-center">
              <Calendar className="h-16 w-16 text-gray-400" />
            </div>
          )}

          {/* Description */}
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">About This Event</h2>
            <p className="whitespace-pre-line text-gray-700">
              {event.description}
            </p>
          </div>

          {/* Additional details if applicable */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Age restriction */}
            <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center mb-3">
                  <UserCheck className="h-5 w-5 text-blue-500 mr-2" />
                  <h3 className="text-lg font-medium">Age Requirement</h3>
                </div>
                <div className="space-y-2">
                  {event.ageRestriction?.minAge ? (
                    <p className="text-gray-700">
                      Minimum age: <span className="font-semibold">{event.ageRestriction?.minAge} years</span>
                    </p>
                  ) : (
                    <p className="text-green-600 font-medium">No minimum age requirement</p>
                  )}

                  {event.ageRestriction?.maxAge !== undefined && event.ageRestriction.maxAge > 0 && (
                    <p className="text-gray-700">
                      Maximum age: <span className="font-semibold">{event.ageRestriction?.maxAge} years</span>
                    </p>
                  )}

                  {event.ageRestriction?.ageGroups && (
                    <p className="text-gray-700">
                      Age groups: <span className="font-semibold">{event.ageRestriction?.ageGroups}</span>
                    </p>
                  )}

                  {event.ageRestriction?.description && (
                    <p className="text-gray-700 mt-2">
                      {event.ageRestriction?.description}
                    </p>
                  )}

                  {event.ageRestriction?.minAge !== undefined && event.ageRestriction.minAge > 0 && (
                    <p className="text-sm text-gray-500 mt-2">
                      ID verification may be required at entry
                    </p>
                  )}

                  {(!event.ageRestriction?.minAge || event.ageRestriction?.minAge <= 0) &&
                   (!event.ageRestriction?.maxAge || event.ageRestriction?.maxAge <= 0) &&
                   (!event.ageRestriction?.ageGroups || event.ageRestriction?.ageGroups === '') &&
                   (!event.ageRestriction?.description || event.ageRestriction?.description === '') && (
                    <p className="text-gray-700">
                      This event has no age restrictions.
                    </p>
                  )}
                </div>
              </div>

            {/* Parking info */}
            <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center mb-3">
                  <Car className="h-5 w-5 text-blue-500 mr-2" />
                  <h3 className="text-lg font-medium">Parking Information</h3>
                </div>
                <div className="space-y-2">
                  {event.ParkingManagement ? (
                    <>
                      <p className="text-gray-700">
                        {event.ParkingManagement?.totalSpaces !== undefined && event.ParkingManagement.totalSpaces > 0 ? (
                          <span className="text-green-600 font-medium">Parking available</span>
                        ) : (
                          <span className="text-red-600 font-medium">No parking available</span>
                        )}
                      </p>

                      {event.ParkingManagement?.totalSpaces !== undefined && event.ParkingManagement.totalSpaces > 0 && (
                        <>
                          <p className="text-gray-700">
                            Total spaces: <span className="font-semibold">{event.ParkingManagement?.totalSpaces}</span>
                          </p>
                          {event.ParkingManagement?.reservedSpaces !== undefined && event.ParkingManagement.reservedSpaces > 0 && (
                            <p className="text-gray-700">
                              Reserved spaces: <span className="font-semibold">{event.ParkingManagement?.reservedSpaces}</span>
                            </p>
                          )}
                          <p className="text-gray-700">
                            {event.ParkingManagement?.isFree ? (
                              <span className="text-green-600 font-medium">Free parking</span>
                            ) : (
                              <span>
                                Paid parking
                                {event.ParkingManagement?.pricePerHour && (
                                  <span className="font-semibold"> (${event.ParkingManagement?.pricePerHour}/hour)</span>
                                )}
                              </span>
                            )}
                          </p>
                          {event.ParkingManagement?.reservationRequired && (
                            <p className="text-amber-600 font-medium">
                              Reservation required
                            </p>
                          )}
                        </>
                      )}

                      {event.ParkingManagement?.description && (
                        <p className="text-gray-700 mt-2">
                          {event.ParkingManagement?.description}
                        </p>
                      )}
                    </>
                  ) : (
                    <p className="text-red-600 font-medium">No parking information available</p>
                  )}


                </div>
              </div>

              {/* SEO Settings */}
              <div className="bg-white rounded-lg shadow p-6 mt-6">
                <div className="flex items-center mb-3">
                  <h3 className="text-lg font-medium">SEO Settings</h3>
                </div>
                <div className="space-y-2">
                  {event.seoSettings ? (
                    <>
                      <p className="text-gray-700">
                        <span className="font-semibold">Title:</span> {event.seoSettings.title || event.title}
                      </p>
                      <p className="text-gray-700">
                        <span className="font-semibold">Description:</span> {event.seoSettings.description || 'No SEO description'}
                      </p>
                      {event.seoSettings.keywords && event.seoSettings.keywords.length > 0 && (
                        <div>
                          <span className="font-semibold">Keywords:</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {event.seoSettings.keywords.map((keyword, index) => (
                              <span key={index} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                                {keyword}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    <p className="text-gray-700">No SEO settings configured</p>
                  )}
                </div>
              </div>

              {/* Social Settings */}
              <div className="bg-white rounded-lg shadow p-6 mt-6">
                <div className="flex items-center mb-3">
                  <h3 className="text-lg font-medium">Social Media Settings</h3>
                </div>
                <div className="space-y-4">
                  {event.socialSettings ? (
                    <>
                      <div>
                        <h4 className="font-medium text-gray-700">Facebook</h4>
                        <p className="text-gray-700">
                          <span className="font-semibold">Title:</span> {event.socialSettings.facebookTitle || event.title}
                        </p>
                        <p className="text-gray-700">
                          <span className="font-semibold">Description:</span> {event.socialSettings.facebookDescription || 'No Facebook description'}
                        </p>
                      </div>

                      <div>
                        <h4 className="font-medium text-gray-700">Twitter</h4>
                        <p className="text-gray-700">
                          <span className="font-semibold">Title:</span> {event.socialSettings.twitterTitle || event.title}
                        </p>
                        <p className="text-gray-700">
                          <span className="font-semibold">Description:</span> {event.socialSettings.twitterDescription || 'No Twitter description'}
                        </p>
                      </div>

                      {event.socialSettings.ogImage && (
                        <div>
                          <h4 className="font-medium text-gray-700">Open Graph Image</h4>
                          <p className="text-gray-700 truncate">{event.socialSettings.ogImage}</p>
                        </div>
                      )}
                    </>
                  ) : (
                    <p className="text-gray-700">No social media settings configured</p>
                  )}
                </div>
              </div>
          </div>

          {/* Ticket Information */}
          {event.tickets && event.tickets.length > 0 && (
            <div className="bg-white rounded-lg shadow p-6 mt-6">
              <h2 className="text-xl font-semibold mb-4">Ticket Information</h2>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Special Guest</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {event.tickets.map((ticket, index) => (
                      <tr key={ticket.id || index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{ticket.name || ticket.type}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {ticket.price ? `$${ticket.price.toFixed(2)}` : 'Free'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{ticket.quantity}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {ticket.specialGuestType && ticket.specialGuestName && ticket.specialGuestType !== 'None' ? (
                            <>
                              <span className="font-medium">{ticket.specialGuestType}:</span> {ticket.specialGuestName}
                            </>
                          ) : '-'}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500">{ticket.description || '-'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow overflow-hidden sticky top-6">
            {/* Event time & location */}
            <div className="p-6 border-b">
              <div className="flex items-start mb-4">
                <Calendar className="h-5 w-5 text-blue-500 mt-1 mr-3 flex-shrink-0" />
                <div>
                  <h3 className="font-medium">Date & Time</h3>
                  <p className="text-gray-700">{formatDate(event.startDate)}</p>
                  <p className="text-gray-700">
                    {formatTime(event.startDate)}
                    {event.endDate && ` - ${formatTime(event.endDate)}`}
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <MapPin className="h-5 w-5 text-blue-500 mt-1 mr-3 flex-shrink-0" />
                <div>
                  <h3 className="font-medium">Location</h3>
                  <p className="text-gray-700">{event.location}</p>
                </div>
              </div>
            </div>

            {/* Other event details */}
            <div className="p-6">
              {/* Status explanation */}
              <div className="flex items-start mb-4">
                <AlertTriangle className="h-5 w-5 text-blue-500 mt-1 mr-3 flex-shrink-0" />
                <div>
                  <h3 className="font-medium">Status</h3>
                  <p className="text-gray-700 flex items-center">
                    <StatusBadge status={event.status} />
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    {event.status === 'Draft' && 'Your event is in draft mode. Only you can see it. Submit it for review when ready.'}
                    {event.status === 'Pending' && 'Your event is pending review by our team. You will be notified once it is approved or rejected.'}
                    {event.status === 'UnderReview' && 'Your event is currently being reviewed by our team. You will be notified once it is approved or rejected.'}
                    {event.status === 'Approved' && 'Your event has been approved but is not yet published. You can now publish it.'}
                    {event.status === 'Published' && 'Your event is live and visible to the public.'}
                    {event.status === 'Rejected' && 'Your event has been rejected. Please check your email for details.'}
                    {event.status === 'Cancelled' && 'This event has been cancelled.'}
                    {event.status === 'Completed' && 'This event has already taken place.'}
                  </p>
                </div>
              </div>

              <div className="flex items-start mb-4">
                <Tag className="h-5 w-5 text-blue-500 mt-1 mr-3 flex-shrink-0" />
                <div>
                  <h3 className="font-medium">Category</h3>
                  <p className="text-gray-700">{event.category}</p>
                </div>
              </div>

              <div className="flex items-start mb-4">
                <Users className="h-5 w-5 text-blue-500 mt-1 mr-3 flex-shrink-0" />
                <div>
                  <h3 className="font-medium">Capacity</h3>
                  {event.venueCapacity !== undefined && event.venueCapacity > 0 && (
                    <p className="text-gray-700">Venue capacity: <span className="font-semibold">{event.venueCapacity}</span> people</p>
                  )}
                  {event.regularSeats !== undefined && event.regularSeats > 0 && (
                    <p className="text-gray-700">Regular: <span className="font-semibold">{event.regularSeats}</span> seats</p>
                  )}
                  {event.vipSeats !== undefined && event.vipSeats > 0 && (
                    <p className="text-gray-700">VIP: <span className="font-semibold">{event.vipSeats}</span> seats</p>
                  )}
                  {event.vvipSeats !== undefined && event.vvipSeats > 0 && (
                    <p className="text-gray-700">VVIP: <span className="font-semibold">{event.vvipSeats}</span> seats</p>
                  )}
                  {(!event.venueCapacity || event.venueCapacity <= 0) &&
                   (!event.regularSeats || event.regularSeats <= 0) &&
                   (!event.vipSeats || event.vipSeats <= 0) &&
                   (!event.vvipSeats || event.vvipSeats <= 0) && (
                    <p className="text-gray-700">Not specified</p>
                  )}
                </div>
              </div>

              <div className="flex items-start mb-4">
                <Clock className="h-5 w-5 text-blue-500 mt-1 mr-3 flex-shrink-0" />
                <div>
                  <h3 className="font-medium">Created</h3>
                  <p className="text-gray-700">{new Date(event.createdAt).toLocaleDateString()}</p>
                  {event.updatedAt && event.updatedAt !== event.createdAt && (
                    <p className="text-gray-700 text-sm">Updated: {new Date(event.updatedAt).toLocaleDateString()}</p>
                  )}
                </div>
              </div>

              {/* Feature button */}
              {event.status === 'Published' && (
                <div className="w-full mt-4">
                  <SimpleFeatureButton
                    eventId={event.id}
                    className="w-full bg-yellow-500 hover:bg-yellow-600 text-white py-2 px-4 rounded-md flex items-center justify-center"
                  />
                </div>
              )}

              {/* Seating Setup button */}
              <Link
                href={`/dashboard/organizer/events/${event.id}/seating-setup`}
                className="w-full mt-4 bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-md flex items-center justify-center"
              >
                <LayoutGrid className="mr-2 h-4 w-4" />
                Manage Seating
              </Link>

              {/* Share button */}
              <button
                onClick={() => {
                  if (navigator.share) {
                    navigator.share({
                      title: event.title,
                      text: `Check out this event: ${event.title}`,
                      url: window.location.href,
                    });
                  } else {
                    navigator.clipboard.writeText(window.location.href);
                    alert('Link copied to clipboard!');
                  }
                }}
                className="w-full mt-4 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center justify-center"
              >
                <Share2 className="mr-2 h-4 w-4" />
                Share Event
              </button>

              {/* Payout button - only for published or completed events */}
              {(event.status === 'Published' || event.status === 'Completed') && (
                <Link
                  href={`/dashboard/organizer/events/payouts/${event.id}`}
                  className="w-full mt-4 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md flex items-center justify-center"
                >
                  <DollarSign className="mr-2 h-4 w-4" />
                  View Payout
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}