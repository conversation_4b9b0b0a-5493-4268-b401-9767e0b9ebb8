'use client';

import { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/use-toast';
import { 
  Smartphone, 
  Calendar, 
  Tag, 
  Clock, 
  CheckCircle2, 
  XCircle, 
  AlertCircle,
  Receipt,
  DollarSign
} from 'lucide-react';
import { format } from 'date-fns';

interface POSDeviceRental {
  id: string;
  deviceId: string;
  vendorId: string;
  eventId?: string;
  rentalStartDate: string;
  rentalEndDate?: string;
  status: string;
  rentalFee: number;
  depositAmount?: number;
  isReturned: boolean;
  returnDate?: string;
  condition?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  device: {
    id: string;
    deviceId: string;
    model: string;
    manufacturer: string;
  };
  event?: {
    id: string;
    title: string;
  };
  transactions: POSRentalTransaction[];
}

interface POSRentalTransaction {
  id: string;
  rentalId: string;
  amount: number;
  type: string;
  status: string;
  paymentMethod?: string;
  reference?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export default function VendorPOSRentalsPage() {
  const [activeRentals, setActiveRentals] = useState<POSDeviceRental[]>([]);
  const [rentalHistory, setRentalHistory] = useState<POSDeviceRental[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('active');
  
  // Load rentals
  useEffect(() => {
    fetchRentals();
  }, []);
  
  const fetchRentals = async () => {
    try {
      setLoading(true);
      
      // Fetch active rentals
      const activeResponse = await fetch('/api/vendors/pos-rentals?type=active');
      
      if (!activeResponse.ok) {
        throw new Error('Failed to fetch active rentals');
      }
      
      const activeData = await activeResponse.json();
      setActiveRentals(activeData);
      
      // Fetch rental history
      const historyResponse = await fetch('/api/vendors/pos-rentals?type=history');
      
      if (!historyResponse.ok) {
        throw new Error('Failed to fetch rental history');
      }
      
      const historyData = await historyResponse.json();
      setRentalHistory(historyData);
    } catch (error) {
      console.error('Error fetching rentals:', error);
      toast({
        title: 'Error',
        description: 'Failed to load POS device rentals',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };
  
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'COMPLETED':
        return 'bg-blue-100 text-blue-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  const getTransactionStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'FAILED':
        return 'bg-red-100 text-red-800';
      case 'CANCELLED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  const renderRentalTable = (rentals: POSDeviceRental[]) => {
    if (loading) {
      return (
        <div className="flex justify-center items-center h-40">
          <p>Loading rentals...</p>
        </div>
      );
    }
    
    if (rentals.length === 0) {
      return (
        <div className="flex justify-center items-center h-40">
          <p className="text-gray-500">No rentals found</p>
        </div>
      );
    }
    
    return (
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Device</TableHead>
            <TableHead>Event</TableHead>
            <TableHead>Rental Period</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Rental Fee</TableHead>
            <TableHead>Payment Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {rentals.map((rental) => (
            <TableRow key={rental.id}>
              <TableCell>
                <div className="flex items-center">
                  <Smartphone className="h-4 w-4 mr-2 text-gray-500" />
                  <div>
                    <div className="font-medium">{rental.device.manufacturer} {rental.device.model}</div>
                    <div className="text-xs text-gray-500">ID: {rental.device.deviceId}</div>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                {rental.event ? (
                  <div>{rental.event.title}</div>
                ) : (
                  <span className="text-gray-500">No event</span>
                )}
              </TableCell>
              <TableCell>
                <div className="text-xs">
                  From: {format(new Date(rental.rentalStartDate), 'MMM d, yyyy')}
                </div>
                {rental.rentalEndDate && (
                  <div className="text-xs">
                    To: {format(new Date(rental.rentalEndDate), 'MMM d, yyyy')}
                  </div>
                )}
              </TableCell>
              <TableCell>
                <span className={`px-2 py-1 rounded-full text-xs ${getStatusBadgeColor(rental.status)}`}>
                  {rental.status}
                </span>
              </TableCell>
              <TableCell>
                <div className="font-medium">
                  ${rental.rentalFee.toFixed(2)}
                </div>
                {rental.depositAmount && (
                  <div className="text-xs text-gray-500">
                    Deposit: ${rental.depositAmount.toFixed(2)}
                  </div>
                )}
              </TableCell>
              <TableCell>
                {rental.transactions.map((transaction) => (
                  <div key={transaction.id} className="mb-1">
                    <span className={`px-2 py-1 rounded-full text-xs ${getTransactionStatusBadgeColor(transaction.status)}`}>
                      {transaction.type}: {transaction.status}
                    </span>
                  </div>
                ))}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    );
  };
  
  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">POS Device Rentals</h1>
          <p className="text-gray-500">Manage your POS device rentals</p>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="active">Active Rentals</TabsTrigger>
          <TabsTrigger value="history">Rental History</TabsTrigger>
        </TabsList>
        
        <TabsContent value="active">
          <Card>
            <CardHeader>
              <CardTitle>Active Rentals</CardTitle>
              <CardDescription>
                Your current POS device rentals
              </CardDescription>
            </CardHeader>
            <CardContent>
              {renderRentalTable(activeRentals)}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Rental History</CardTitle>
              <CardDescription>
                Your past POS device rentals
              </CardDescription>
            </CardHeader>
            <CardContent>
              {renderRentalTable(rentalHistory)}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Rental Information</CardTitle>
          <CardDescription>
            Important information about POS device rentals
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <DollarSign className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <h3 className="font-medium">Rental Fee</h3>
                <p className="text-sm text-gray-600">
                  The standard rental fee for a POS device is K1000. This fee is charged for each rental period.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <Receipt className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <h3 className="font-medium">Transaction Processing</h3>
                <p className="text-sm text-gray-600">
                  A 3.5% transaction processing fee is applied to all sales processed through the POS device.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <AlertCircle className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <h3 className="font-medium">Device Care</h3>
                <p className="text-sm text-gray-600">
                  You are responsible for the care of the device during the rental period. Any damage to the device may result in additional charges.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <Calendar className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <h3 className="font-medium">Rental Period</h3>
                <p className="text-sm text-gray-600">
                  The rental period begins on the start date and ends on the return date. Late returns may incur additional charges.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
