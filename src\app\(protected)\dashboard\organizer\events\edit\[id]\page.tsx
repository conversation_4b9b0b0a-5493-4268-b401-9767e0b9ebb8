'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Loader2, Check, ChevronRight, ChevronLeft } from 'lucide-react';
import { EventCategory, EventType } from '@prisma/client';
import { useLoadScript } from '@react-google-maps/api';

// Import the EventFormData type
import { EventFormData, TicketType } from '@/types/events';

// Define the Ticket interface based on the Prisma model
interface Ticket {
  id: string;
  type: string;
  price: number;
  quantity: number;
  description?: string;
  specialGuestType?: string;
  specialGuestName?: string;
  saleStartTime?: string;
  saleEndTime?: string;
  [key: string]: any; // Allow for additional properties
}

// Use EventFormData as our Event type
export type Event = EventFormData;

// Define props interface for EventForm
export interface EventFormProps {
  initialValues: Event;
  onSubmit: (formData: Event) => Promise<void>;
  isSubmitting: boolean;
}

// Import step components
import { BasicInfoSection } from '@/components/ui/eventform/BasicInfoSection';
import { TypeSelector } from '@/components/ui/eventform/TypeSelector';
import { DateTimeSection } from '@/components/ui/eventform/DateTimeSection';
import { LocationSection } from '@/components/ui/eventform/LocationSection';
import { AgeRestrictionSection } from '@/components/ui/eventform/AgeRestrictionSection';
import { ParkingSection } from '@/components/ui/eventform/ParkingSection';
import { TicketsStep } from '@/components/ui/eventform/wizard-steps/TicketsStep';
import SponsorsSection from '@/components/ui/eventform/wizard-steps/SponsorsSection';

export default function EditEventPage() {
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);

  // Define the steps
  const steps = [
    { id: 1, name: 'Basic Info', icon: '📝' },
    { id: 2, name: 'Event Type', icon: '🏢' },
    { id: 3, name: 'Date & Time', icon: '🗓️' },
    { id: 4, name: 'Location', icon: '📍' },
    { id: 5, name: 'Age Restriction', icon: '🔞' },
    { id: 6, name: 'Parking', icon: '🅿️' },
    { id: 7, name: 'Tickets', icon: '🎟️' },
    { id: 8, name: 'Sponsors', icon: '🏆' },
    { id: 9, name: 'Review', icon: '✅' },
  ];

  const router = useRouter();
  const params = useParams();
  const { data: session } = useSession();

  const { isLoaded } = useLoadScript({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || "",
    libraries: ["places"],
  });

  // Navigation functions
  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
      window.scrollTo(0, 0);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      window.scrollTo(0, 0);
    }
  };

  const goToStep = (step: number) => {
    if (step >= 1 && step <= steps.length) {
      setCurrentStep(step);
      window.scrollTo(0, 0);
    }
  };

  // Fetch event data
  useEffect(() => {
    const fetchEvent = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/dashboard/organiser/events/${params.id}`);

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json();

        // Log the raw data from API
        console.log('Raw event data from API:', data);
        console.log('Parking Management from API:', data.ParkingManagement);
        console.log('Sponsors from API:', data.sponsors);

        // Transform API data to match the expected format
        const transformedData: Event = {
          ...data,
          startDate: data.startDate, // Map from API field to component field
          endDate: data.endDate,
          startTime: data.startTime,
          endTime: data.endTime,
          // Make sure category and eventType are the correct types
          category: data.category as EventCategory,
          eventType: data.eventType as string,
          // Keep both ParkingManagement (from API) and parkingManagement (for component)
          ParkingManagement: data.ParkingManagement,
          parkingManagement: data.ParkingManagement ? {
            totalSpaces: data.ParkingManagement.totalSpaces === 0 ? '' : data.ParkingManagement.totalSpaces,
            reservedSpaces: data.ParkingManagement.reservedSpaces === 0 ? '' : data.ParkingManagement.reservedSpaces,
            pricePerHour: data.ParkingManagement.pricePerHour === 0 ? '' : data.ParkingManagement.pricePerHour,
            isFree: data.ParkingManagement.isFree || false,
            reservationRequired: data.ParkingManagement.reservationRequired || false,
            description: data.ParkingManagement.description || ''
          } : {
            totalSpaces: '',
            reservedSpaces: '',
            pricePerHour: '',
            isFree: false,
            reservationRequired: false,
            description: 'No parking available'
          },
          // Initialize ticketTypes if not present
          ticketTypes: data.tickets?.map((ticket: Ticket) => ({
            id: ticket.id,
            name: ticket.type,
            price: ticket.price,
            quantity: ticket.quantity,
            description: ticket.description || '',
            specialGuestType: ticket.specialGuestType || 'None',
            specialGuestName: ticket.specialGuestName || '',
            startSaleDate: ticket.saleStartTime || '',
            endSaleDate: ticket.saleEndTime || ''
          })) || [],

          // Initialize sponsors if present, ensure it's always an array
          sponsors: Array.isArray(data.sponsors) ? [...data.sponsors] :
                   (data.sponsors ? [data.sponsors] : []),
        };

        setEvent(transformedData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch event');
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchEvent();
    }
  }, [params.id]);

  // Function to render the current step
  const renderStep = () => {
    if (!event) return null;

    const handleInputChange = (name: string, value: any) => {
      console.log(`handleInputChange: ${name}`, value);

      setEvent(prev => {
        if (!prev) return null;

        // Handle nested fields (e.g., 'ageRestriction.minAge')
        if (name.includes('.')) {
          const [parent, child] = name.split('.');

          // Handle specific nested objects based on the parent field
          if (parent === 'ageRestriction') {
            return {
              ...prev,
              ageRestriction: {
                ...prev.ageRestriction,
                [child]: value
              }
            };
          } else if (parent === 'parkingManagement') {
            // Create parkingManagement if it doesn't exist
            const currentParking = prev.parkingManagement || {};

            // Log the current and updated parking state
            console.log('Current parking state:', currentParking);
            console.log('Updating field:', child, 'to', value);

            return {
              ...prev,
              parkingManagement: {
                ...currentParking,
                [child]: value
              }
            };
          }

          // If we reach here, it's a field we don't explicitly handle
          console.warn(`Unhandled nested field: ${name}`);
          return prev;
        }

        // Handle regular fields
        return { ...prev, [name]: value };
      });
    };

    switch (currentStep) {
      case 1: // Basic Info
        return (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-bold mb-6">Basic Information</h2>
            <BasicInfoSection
              title={event.title}
              description={event.description}
              category={event.category}
              onInputChange={handleInputChange}
              errors={{}}
            />
          </div>
        );
      case 2: // Event Type
        return (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-bold mb-6">Event Type</h2>
            <TypeSelector
              selectedType={event.eventType}
              onTypeChange={(type) => handleInputChange('eventType', type)}
            />
            {(event.eventType === 'PHYSICAL' || event.eventType === 'HYBRID') && (
              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Number of Days
                </label>
                <input
                  type="number"
                  min="1"
                  value={event.numberOfDays || 1}
                  onChange={(e) =>
                    handleInputChange('numberOfDays', parseInt(e.target.value))
                  }
                  className="w-full sm:w-1/2 md:w-1/4 border-gray-300 rounded-lg shadow-sm"
                  required
                />
              </div>
            )}
          </div>
        );
      case 3: // Date & Time
        return (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-bold mb-6">Date & Time</h2>
            <DateTimeSection
              startDate={event.startDate}
              endDate={event.endDate}
              startTime={event.startTime}
              endTime={event.endTime}
              onInputChange={handleInputChange}
            />
          </div>
        );
      case 4: // Location
        return (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-bold mb-6">Location & Venue</h2>
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Venue Name
              </label>
              <input
                type="text"
                value={event.venue || ''}
                onChange={(e) => handleInputChange('venue', e.target.value)}
                className="w-full px-3 py-2 border rounded-lg border-gray-300"
                placeholder="Enter venue name"
                required
              />
            </div>
            <LocationSection
              eventType={event.eventType}
              onInputChange={handleInputChange}
            />
          </div>
        );
      case 5: // Age Restriction
        return (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-bold mb-6">Age Restrictions</h2>
            <AgeRestrictionSection
              formData={event}
              onChange={(name, value) => handleInputChange(`ageRestriction.${name}`, value)}
            />
          </div>
        );
      case 6: // Parking
        return (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-bold mb-6">Parking Information</h2>
            <ParkingSection
              formData={event}
              onChange={(name, value) => {
                console.log(`Parking section onChange: ${name}`, value);
                if (name === 'parkingManagement') {
                  // Direct update of the entire parkingManagement object
                  setEvent(prev => {
                    if (!prev) return null;
                    return { ...prev, parkingManagement: value };
                  });
                } else {
                  // Legacy support for individual field updates
                  handleInputChange(`parkingManagement.${name}`, value);
                }
              }}
            />
          </div>
        );
      case 7: // Tickets
        return (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-bold mb-6">Tickets</h2>
            <TicketsStep
              formData={event}
              onInputChange={handleInputChange}
              errors={{}}
            />
          </div>
        );
      case 8: // Sponsors
        return (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-bold mb-6">Sponsors</h2>
            <SponsorsSection
              sponsors={event.sponsors || []}
              onSponsorsChange={(sponsors) => handleInputChange('sponsors', sponsors)}
            />
          </div>
        );
      case 9: // Review
        return (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-bold mb-6">Review Your Event</h2>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium">Basic Information</h3>
                <p><strong>Title:</strong> {event.title}</p>
                <p><strong>Description:</strong> {event.description}</p>
                <p><strong>Category:</strong> {event.category}</p>
              </div>

              <div>
                <h3 className="text-lg font-medium">Event Details</h3>
                <p><strong>Type:</strong> {event.eventType}</p>
                <p><strong>Start Date:</strong> {event.startDate}</p>
                <p><strong>End Date:</strong> {event.endDate}</p>
                <p><strong>Start Time:</strong> {event.startTime}</p>
                <p><strong>End Time:</strong> {event.endTime}</p>
              </div>

              <div>
                <h3 className="text-lg font-medium">Location</h3>
                <p><strong>Venue:</strong> {event.venue}</p>
                <p><strong>Address:</strong> {event.location}</p>
              </div>

              <div>
                <h3 className="text-lg font-medium">Tickets</h3>
                <ul className="list-disc pl-5">
                  {event.ticketTypes?.map((ticket, index) => (
                    <li key={index}>
                      {ticket.name}: ${ticket.price} (Quantity: {ticket.quantity})
                    </li>
                  ))}
                </ul>
              </div>

              {event.sponsors && event.sponsors.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium">Sponsors</h3>
                  <ul className="list-disc pl-5">
                    {event.sponsors.map((sponsor, index) => (
                      <li key={index}>
                        {sponsor.name} - {sponsor.tier} Tier {sponsor.amount > 0 ? `($${sponsor.amount})` : ''}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  const handleSubmit = async (formData: Event) => {
    try {
      setIsSubmitting(true);
      setError(null);

      // Log the parking management data for debugging
      console.log('Submitting parkingManagement:', formData.parkingManagement);

      // Log the sponsors data for debugging
      console.log('Submitting sponsors:', formData.sponsors);
      console.log('Sponsors JSON:', JSON.stringify(formData.sponsors || []));
      console.log('Sponsors length:', (formData.sponsors || []).length);

      // IMPORTANT: Create a dedicated sponsors array to ensure proper format
      // This is a completely new implementation to fix the sponsors issue once and for all
      let sponsorsArray = [];
      console.log('Raw sponsors data type:', typeof formData.sponsors);
      console.log('Raw sponsors data:', formData.sponsors);

      // First, try to get sponsors from the formData.sponsors field
      if (formData.sponsors) {
        if (Array.isArray(formData.sponsors)) {
          console.log('Sponsors is already an array with length:', formData.sponsors.length);
          // Create a new array to avoid reference issues
          sponsorsArray = [...formData.sponsors];
          // Log each sponsor for debugging
          sponsorsArray.forEach((sponsor, index) => {
            console.log(`Sponsor ${index}:`, sponsor);
          });
        } else if (typeof formData.sponsors === 'object') {
          console.log('Sponsors is a single object, converting to array');
          // Handle single sponsor object
          sponsorsArray = [formData.sponsors];
          console.log('Single sponsor:', formData.sponsors);
        } else if (typeof formData.sponsors === 'string') {
          try {
            // Try to parse the string as JSON
            const parsed = JSON.parse(formData.sponsors);
            if (Array.isArray(parsed)) {
              sponsorsArray = parsed;
              console.log('Parsed sponsors string into array with length:', sponsorsArray.length);
            } else if (parsed && typeof parsed === 'object') {
              sponsorsArray = [parsed];
              console.log('Parsed sponsors string into single object');
            }
          } catch (error) {
            console.error('Error parsing sponsors string:', error);
          }
        }
      }

      // If sponsorsArray is still empty, try to reconstruct from individual fields
      if (sponsorsArray.length === 0) {
        console.log('Sponsors array is empty, attempting to reconstruct from individual fields');
        // Try to get the count from sponsorsCount field
        const count = formData.sponsorsCount ? parseInt(formData.sponsorsCount) : 0;

        if (count > 0) {
          console.log(`Found sponsorsCount: ${count}, reconstructing sponsors array`);
          for (let i = 0; i < count; i++) {
            if (formData[`sponsor_${i}_name`]) {
              const sponsor = {
                id: crypto.randomUUID(),
                name: formData[`sponsor_${i}_name`],
                tier: formData[`sponsor_${i}_tier`] || 'BRONZE',
                logo: formData[`sponsor_${i}_logo`] || '',
                website: formData[`sponsor_${i}_website`] || '',
                amount: parseFloat(formData[`sponsor_${i}_amount`] || '0')
              };
              sponsorsArray.push(sponsor);
              console.log(`Reconstructed sponsor ${i}:`, sponsor);
            }
          }
        }
      }

      // Final check to ensure sponsorsArray is valid
      if (!Array.isArray(sponsorsArray)) {
        console.error('sponsorsArray is not an array, resetting to empty array');
        sponsorsArray = [];
      }

      console.log('Sponsors array after conversion:', sponsorsArray);
      console.log('Sponsors array length after conversion:', sponsorsArray.length);
      console.log('Sponsors array JSON after conversion:', JSON.stringify(sponsorsArray));

      // Validate and clean sponsors data
      const cleanedSponsors = sponsorsArray.map(sponsor => ({
        id: sponsor.id || crypto.randomUUID(),
        name: sponsor.name || '',
        logo: sponsor.logo || '',
        website: sponsor.website || '',
        tier: sponsor.tier || 'BRONZE',
        amount: typeof sponsor.amount === 'number' ? sponsor.amount : parseFloat(sponsor.amount?.toString() || '0')
      }));

      console.log('Cleaned sponsors data:', cleanedSponsors);
      console.log('Sponsors JSON:', JSON.stringify(cleanedSponsors));

      // IMPORTANT: Update the sponsors array in the form data
      formData.sponsors = cleanedSponsors;

      // IMPORTANT: Also add a dedicated field for sponsors data to ensure it's received
      formData.sponsorsData = cleanedSponsors;

      // IMPORTANT: Add a special field with the raw sponsors data
      formData.rawSponsors = cleanedSponsors;

      // IMPORTANT: Add a special field with the sponsors as a JSON string
      formData.sponsorsJson = JSON.stringify(cleanedSponsors);

      // IMPORTANT: Add a special field with the sponsors as a stringified array
      formData.sponsorsStringified = JSON.stringify(cleanedSponsors);

      // IMPORTANT: Add a special field with the sponsors count
      formData.sponsorsCount = cleanedSponsors.length.toString();

      // IMPORTANT: Add a special field to indicate if there are sponsors
      formData.hasSponsors = cleanedSponsors.length > 0 ? 'true' : 'false';

      // Add each sponsor individually
      cleanedSponsors.forEach((sponsor, index) => {
        console.log(`Adding sponsor ${index}:`, sponsor);
        formData[`sponsor_${index}_name`] = sponsor.name;
        formData[`sponsor_${index}_tier`] = sponsor.tier;
        formData[`sponsor_${index}_logo`] = sponsor.logo;
        formData[`sponsor_${index}_website`] = sponsor.website;
        formData[`sponsor_${index}_amount`] = sponsor.amount.toString();
      });

      // Add a special field with the count
      formData.sponsorsCount = cleanedSponsors.length.toString();

      // Add a special field to indicate sponsors were processed
      formData.hasSponsors = cleanedSponsors.length > 0 ? "true" : "false";

      // Add a special field for a single sponsor
      if (cleanedSponsors.length === 1) {
        console.log('Adding single sponsor data');
        formData.singleSponsor = cleanedSponsors[0];
        formData.hasSingleSponsor = "true";
      }

      // Transform form data back to API expected format if needed
      const apiData = {
        ...formData,
        // Ensure dates are properly formatted
        startDate: formData.startDate,
        endDate: formData.endDate,
        startTime: formData.startTime,
        endTime: formData.endTime,
        // Make sure nested objects are properly structured and convert empty strings to numbers
        ageRestriction: formData.ageRestriction ? {
          minAge: formData.ageRestriction.minAge === '' ? 0 : Number(formData.ageRestriction.minAge),
          maxAge: formData.ageRestriction.maxAge === '' ? 0 : Number(formData.ageRestriction.maxAge),
          ageGroups: formData.ageRestriction.ageGroups || '',
          description: formData.ageRestriction.description || ''
        } : null,
        parkingManagement: formData.parkingManagement || null,

        // Log the parking management data for debugging (without using spread)
        // This line is just for logging, not for adding properties
        // Include ticket types
        ticketTypes: formData.ticketTypes?.map((ticket: TicketType) => ({
          id: ticket.id,
          name: ticket.name,
          price: ticket.price,
          quantity: ticket.quantity,
          description: ticket.description || '',
          specialGuestType: ticket.specialGuestType || 'None',
          specialGuestName: ticket.specialGuestName || '',
          startSaleDate: ticket.startSaleDate || '',
          endSaleDate: ticket.endSaleDate || ''
        })) || [],
      };

      // Make sure parkingManagement is properly formatted for the API
      if (apiData.parkingManagement) {
        // Convert empty string values to numbers for the API
        const totalSpaces = apiData.parkingManagement.totalSpaces === '' ? 0 :
                           Number(apiData.parkingManagement.totalSpaces);
        const reservedSpaces = apiData.parkingManagement.reservedSpaces === '' ? 0 :
                              Number(apiData.parkingManagement.reservedSpaces);
        const pricePerHour = apiData.parkingManagement.pricePerHour === '' ? 0 :
                           Number(apiData.parkingManagement.pricePerHour);

        // Create a clean ParkingManagement object for the API
        (apiData as any).ParkingManagement = {
          totalSpaces: totalSpaces,
          reservedSpaces: reservedSpaces,
          pricePerHour: pricePerHour,
          isFree: apiData.parkingManagement.isFree || false,
          reservationRequired: apiData.parkingManagement.reservationRequired || false,
          description: apiData.parkingManagement.description || ''
        };

        // If the event already has a ParkingManagement record, include its ID
        if (formData.ParkingManagement && formData.ParkingManagement.id) {
          (apiData as any).ParkingManagement.id = formData.ParkingManagement.id;
        }

        // Remove the camelCase version to avoid confusion
        // Use type assertion to allow deletion
        delete (apiData as any).parkingManagement;
      }

      console.log('Final ParkingManagement data:', (apiData as any).ParkingManagement);

      console.log('Submitting data:', JSON.stringify(apiData, null, 2));

      // Note the spelling of "organiser" in the API path
      const response = await fetch(`/api/dashboard/organiser/events/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiData),
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        console.error('Error response:', errorData);
        throw new Error(errorData?.error || `Error: ${response.status}`);
      }

      // Show success message
      alert('Event updated successfully!');

      // Redirect to event details page
      router.push(`/dashboard/organizer/events/myEvents`);
    } catch (err) {
      console.error('Submit error:', err);
      setError(err instanceof Error ? err.message : 'Failed to update event');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading || !isLoaded) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader2 className="animate-spin h-8 w-8 text-blue-500" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Edit Event</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          <p>{error}</p>
        </div>
      )}

      {event && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* Sidebar with steps */}
          <div className="md:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-4 sticky top-6">
              <h2 className="text-lg font-medium mb-4">Steps</h2>
              <ol className="space-y-2">
                {steps.map((step) => (
                  <li key={step.id}>
                    <button
                      onClick={() => goToStep(step.id)}
                      className={`w-full text-left px-3 py-2 rounded-md flex items-center ${currentStep === step.id
                        ? 'bg-blue-100 text-blue-700 font-medium'
                        : 'hover:bg-gray-100'
                        }`}
                    >
                      <span className="mr-2">{step.icon}</span>
                      <span>{step.name}</span>
                      {currentStep > step.id && (
                        <Check className="ml-auto h-4 w-4 text-green-500" />
                      )}
                    </button>
                  </li>
                ))}
              </ol>
            </div>
          </div>

          {/* Main content area */}
          <div className="md:col-span-3">
            {/* Progress bar */}
            <div className="mb-6">
              <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-blue-600"
                  style={{ width: `${(currentStep / steps.length) * 100}%` }}
                ></div>
              </div>
              <div className="flex justify-between mt-2 text-sm text-gray-500">
                <span>Step {currentStep} of {steps.length}</span>
                <span>{Math.round((currentStep / steps.length) * 100)}% Complete</span>
              </div>
            </div>

            {/* Current step content */}
            {renderStep()}

            {/* Navigation buttons */}
            <div className="mt-6 flex justify-between">
              {currentStep > 1 ? (
                <button
                  onClick={prevStep}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 flex items-center"
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </button>
              ) : (
                <button
                  onClick={() => router.push('/dashboard/organizer/events/myEvents')}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
                >
                  Cancel
                </button>
              )}

              {currentStep < steps.length ? (
                <button
                  onClick={nextStep}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                >
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </button>
              ) : (
                <button
                  onClick={() => event && handleSubmit(event)}
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center"
                >
                  {isSubmitting ? 'Saving...' : 'Save Changes'}
                  <Check className="h-4 w-4 ml-1" />
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}