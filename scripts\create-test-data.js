// scripts/create-test-data.js
const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');

const prisma = new PrismaClient();

async function main() {
  console.log('Creating test data...');

  // Create a test user if none exists
  const userCount = await prisma.user.count();
  
  console.log(`Found ${userCount} users in the database`);
  
  let testUser;
  
  if (userCount === 0) {
    console.log('Creating test user...');
    
    testUser = await prisma.user.create({
      data: {
        id: crypto.randomUUID(),
        name: 'Test User',
        email: '<EMAIL>',
        role: 'VENDOR',
        emailVerified: new Date(),
      },
    });
    
    console.log('Test user created:', testUser);
  } else {
    // Get the first user
    testUser = await prisma.user.findFirst();
    console.log('Using existing user:', testUser);
  }
  
  // Create NFC Terminal Settings if they don't exist
  const nfcSettings = await prisma.nFCTerminalSettings.findUnique({
    where: { vendorId: testUser.id },
  });
  
  if (!nfcSettings) {
    console.log('Creating NFC Terminal Settings...');
    
    // Generate a unique device ID
    const deviceId = `TERM-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;
    
    const newSettings = await prisma.nFCTerminalSettings.create({
      data: {
        vendorId: testUser.id,
        deviceId,
        terminalName: 'Main Terminal',
        offlineMode: false,
        autoSync: true,
        notificationsEnabled: true,
        autoPrint: false,
        softwareVersion: '1.0.0',
      },
    });
    
    console.log('NFC Terminal Settings created:', newSettings);
  } else {
    console.log('NFC Terminal Settings already exist:', nfcSettings);
  }
  
  // Create a test event if none exist
  const eventCount = await prisma.event.count({
    where: { userId: testUser.id },
  });
  
  if (eventCount === 0) {
    console.log('Creating test event...');
    
    const event = await prisma.event.create({
      data: {
        userId: testUser.id,
        title: 'Test Event',
        description: 'This is a test event created by the script',
        location: 'Test Location',
        startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 3 * 60 * 60 * 1000), // 3 hours after start
        published: true,
        capacity: 100,
        hasStadiumSeating: false,
      },
    });
    
    console.log('Test event created:', event);
    
    // Create a ticket type for the event
    const ticketType = await prisma.ticketType.create({
      data: {
        eventId: event.id,
        name: 'General Admission',
        description: 'Standard entry ticket',
        price: 1000, // $10.00
        quantity: 100,
        maxPerOrder: 10,
      },
    });
    
    console.log('Ticket Type created:', ticketType);
  } else {
    console.log(`User already has ${eventCount} events`);
  }
  
  console.log('Test data creation complete!');
}

main()
  .catch((e) => {
    console.error('Error creating test data:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
