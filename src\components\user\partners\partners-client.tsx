'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { toast } from 'sonner';
import {
  Search,
  Filter,
  Eye,
  MapPin,
  Star,
  Hotel,
  Utensils,
  Wine,
  Music,
  ChevronLeft,
  ExternalLink
} from 'lucide-react';
import Link from 'next/link';

interface Partner {
  id: string;
  businessName: string;
  partnerType: 'HOTEL' | 'RESTAURANT' | 'BAR' | 'NIGHTCLUB';
  description: string;
  rating: number;
  totalReviews: number;
  city: string;
  province: string;
  isVerified: boolean;
  acceptsNfcPayments: boolean;
  hasActivePromotions: boolean;
  distance?: number;
}

export default function UserPartnersClient() {
  const [partners, setPartners] = useState<Partner[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [locationFilter, setLocationFilter] = useState('all');
  const [selectedPartner, setSelectedPartner] = useState<Partner | null>(null);

  // Fetch partners
  const fetchPartners = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        ...(typeFilter !== 'all' && { type: typeFilter }),
        ...(locationFilter !== 'all' && { location: locationFilter }),
        ...(searchTerm && { search: searchTerm })
      });

      // Use the public partners API endpoint for users
      const response = await fetch(`/api/partners?${params}`);

      if (!response.ok) {
        throw new Error('Failed to fetch partners');
      }

      const data = await response.json();
      setPartners(data.partners || []);
    } catch (error) {
      console.error('Error fetching partners:', error);
      toast.error('Failed to load partners');
    } finally {
      setLoading(false);
    }
  };

  // Get partner type icon
  const getPartnerIcon = (type: string) => {
    switch (type) {
      case 'HOTEL':
        return <Hotel className="h-4 w-4" />;
      case 'RESTAURANT':
        return <Utensils className="h-4 w-4" />;
      case 'BAR':
        return <Wine className="h-4 w-4" />;
      case 'NIGHTCLUB':
        return <Music className="h-4 w-4" />;
      default:
        return <Utensils className="h-4 w-4" />;
    }
  };

  // Get partner type badge variant
  const getTypeBadge = (type: string) => {
    const variants = {
      'HOTEL': 'default',
      'RESTAURANT': 'secondary',
      'BAR': 'outline',
      'NIGHTCLUB': 'destructive'
    };
    return variants[type as keyof typeof variants] || 'secondary';
  };

  useEffect(() => {
    fetchPartners();
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/dashboard/user">
                <ChevronLeft className="h-4 w-4 mr-1" />
                Back to Dashboard
              </Link>
            </Button>
          </div>
          <h1 className="text-3xl font-bold">Event Partners</h1>
          <p className="text-gray-500 mt-1">
            Discover verified partners and services for events
          </p>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              Available Partners
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{partners.length}</div>
            <p className="text-xs text-gray-500 mt-1">Verified partners</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              NFC Enabled
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {partners.filter(p => p.acceptsNfcPayments).length}
            </div>
            <p className="text-xs text-gray-500 mt-1">Accept NFC payments</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              Active Promotions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {partners.filter(p => p.hasActivePromotions).length}
            </div>
            <p className="text-xs text-gray-500 mt-1">Running promotions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              Average Rating
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {partners.length > 0 ? (partners.reduce((sum, p) => sum + p.rating, 0) / partners.length).toFixed(1) : '0.0'}
            </div>
            <p className="text-xs text-gray-500 mt-1">Partner rating</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
            <div className="flex flex-col md:flex-row gap-4 flex-1">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search partners..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="HOTEL">Hotels</SelectItem>
                  <SelectItem value="RESTAURANT">Restaurants</SelectItem>
                  <SelectItem value="BAR">Bars</SelectItem>
                  <SelectItem value="NIGHTCLUB">Nightclubs</SelectItem>
                </SelectContent>
              </Select>
              <Select value={locationFilter} onValueChange={setLocationFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by location" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Locations</SelectItem>
                  <SelectItem value="lusaka">Lusaka</SelectItem>
                  <SelectItem value="kitwe">Kitwe</SelectItem>
                  <SelectItem value="ndola">Ndola</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button onClick={fetchPartners} disabled={loading}>
              <Filter className="mr-2 h-4 w-4" />
              Apply Filters
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Partners Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {loading ? (
          [...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : partners.length === 0 ? (
          <div className="col-span-full text-center py-8">
            <p className="text-gray-500">No partners found</p>
          </div>
        ) : (
          partners.map((partner) => (
            <Card key={partner.id} className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    {getPartnerIcon(partner.partnerType)}
                    <CardTitle className="text-lg">{partner.businessName}</CardTitle>
                  </div>
                  <div className="flex gap-1">
                    {partner.isVerified && (
                      <Badge variant="outline" className="bg-green-100 text-green-800 text-xs">
                        Verified
                      </Badge>
                    )}
                    {partner.hasActivePromotions && (
                      <Badge variant="outline" className="bg-blue-100 text-blue-800 text-xs">
                        Promo
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <Badge variant={getTypeBadge(partner.partnerType) as any}>
                    {partner.partnerType}
                  </Badge>
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 text-yellow-400" />
                    <span>{partner.rating}</span>
                    <span>({partner.totalReviews})</span>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-3">{partner.description}</p>

                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-1 text-gray-500">
                    <MapPin className="h-4 w-4" />
                    <span>{partner.city}, {partner.province}</span>
                    {partner.distance && (
                      <span className="ml-2">({partner.distance}km)</span>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-2 mt-3">
                  {partner.acceptsNfcPayments && (
                    <Badge variant="outline" className="text-xs">
                      NFC Payments
                    </Badge>
                  )}
                </div>

                <div className="flex gap-2 mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedPartner(partner)}
                    className="flex-1"
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View Details
                  </Button>
                  <Button size="sm" className="flex-1" asChild>
                    <Link href={`/partners/${partner.id}`}>
                      <ExternalLink className="h-4 w-4 mr-1" />
                      Visit Page
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Partner Details Dialog */}
      <Dialog
        open={!!selectedPartner}
        onOpenChange={(open) => !open && setSelectedPartner(null)}
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedPartner && getPartnerIcon(selectedPartner.partnerType)}
              {selectedPartner?.businessName}
            </DialogTitle>
            <DialogDescription>
              Detailed information about this partner
            </DialogDescription>
          </DialogHeader>

          {selectedPartner && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">Business Information</h4>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="text-gray-500">Type:</span>
                      <Badge className="ml-2" variant={getTypeBadge(selectedPartner.partnerType) as any}>
                        {selectedPartner.partnerType}
                      </Badge>
                    </div>
                    <div>
                      <span className="text-gray-500">Location:</span>
                      <span className="ml-2">{selectedPartner.city}, {selectedPartner.province}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Rating:</span>
                      <span className="ml-2">{selectedPartner.rating} ({selectedPartner.totalReviews} reviews)</span>
                    </div>
                    {selectedPartner.distance && (
                      <div>
                        <span className="text-gray-500">Distance:</span>
                        <span className="ml-2">{selectedPartner.distance}km away</span>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Features</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      {selectedPartner.isVerified ? (
                        <Badge variant="outline" className="bg-green-100 text-green-800">
                          Verified Partner
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-gray-100 text-gray-800">
                          Unverified
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {selectedPartner.acceptsNfcPayments ? (
                        <Badge variant="outline" className="bg-blue-100 text-blue-800">
                          NFC Payments
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-gray-100 text-gray-800">
                          No NFC
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {selectedPartner.hasActivePromotions ? (
                        <Badge variant="outline" className="bg-purple-100 text-purple-800">
                          Active Promotions
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-gray-100 text-gray-800">
                          No Promotions
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Description</h4>
                <p className="text-sm text-gray-600">{selectedPartner.description}</p>
              </div>

              <div className="flex gap-3">
                <Button className="flex-1" asChild>
                  <Link href={`/partners/${selectedPartner.id}`}>
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Visit Partner Page
                  </Link>
                </Button>
                {selectedPartner.hasActivePromotions && (
                  <Button variant="outline" className="flex-1">
                    View Promotions
                  </Button>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
