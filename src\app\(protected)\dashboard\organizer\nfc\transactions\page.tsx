'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import {
  CreditCard,
  Search,
  MoreHorizontal,
  Download,
  Filter,
  RefreshCw,
  CalendarIcon,
  FileText,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import { format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";

// Define interfaces for our data
interface TransactionItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  totalPrice: number;
  productId: string;
  imagePath?: string | null;
}

interface Transaction {
  id: string;
  cardId: string;
  cardUid: string;
  vendorId: string;
  vendorName: string;
  amount: number;
  status: string;
  timestamp: string;
  processedAt: string | null;
  reference?: string | null;
  notes?: string | null;
  eventId: string;
  eventTitle: string;
  assignedTo: string;
  user?: {
    id: string;
    name?: string | null;
    email?: string | null;
  } | null;
  items: TransactionItem[];
}

interface Vendor {
  id: string;
  businessName: string;
}

interface Event {
  id: string;
  title: string;
}

interface Stats {
  totalAmount: number;
  completedAmount: number;
  failedAmount: number;
  pendingAmount: number;
  refundedAmount: number;
  cancelledAmount: number;
  completedCount: number;
  failedCount: number;
  pendingCount: number;
  refundedCount: number;
  cancelledCount: number;
}

// Mock data for demonstration
const mockTransactions = [
  {
    id: 'tx-1001',
    cardId: 'C-7845',
    vendorId: 'V-123',
    vendorName: 'Food Stall A',
    amount: 25.99,
    status: 'completed',
    timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
    items: [
      { name: 'Burger', quantity: 1, price: 15.99 },
      { name: 'Fries', quantity: 1, price: 5.00 },
      { name: 'Soda', quantity: 1, price: 5.00 }
    ]
  },
  {
    id: 'tx-1002',
    cardId: 'C-2341',
    vendorId: 'V-456',
    vendorName: 'Merchandise Booth',
    amount: 45.50,
    status: 'completed',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    items: [
      { name: 'T-Shirt', quantity: 1, price: 25.00 },
      { name: 'Cap', quantity: 1, price: 15.00 },
      { name: 'Keychain', quantity: 1, price: 5.50 }
    ]
  },
  {
    id: 'tx-1003',
    cardId: 'C-9023',
    vendorId: 'V-789',
    vendorName: 'Beverage Stand',
    amount: 12.00,
    status: 'completed',
    timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
    items: [
      { name: 'Beer', quantity: 2, price: 6.00 }
    ]
  },
  {
    id: 'tx-1004',
    cardId: 'C-5672',
    vendorId: 'V-123',
    vendorName: 'Food Stall A',
    amount: 18.50,
    status: 'failed',
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
    items: [
      { name: 'Pizza Slice', quantity: 2, price: 7.25 },
      { name: 'Water', quantity: 1, price: 4.00 }
    ]
  },
  {
    id: 'tx-1005',
    cardId: 'C-3390',
    vendorId: 'V-456',
    vendorName: 'Merchandise Booth',
    amount: 35.00,
    status: 'completed',
    timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
    items: [
      { name: 'Poster', quantity: 1, price: 15.00 },
      { name: 'Wristband', quantity: 2, price: 10.00 }
    ]
  },
  {
    id: 'tx-1006',
    cardId: 'C-8821',
    vendorId: 'V-789',
    vendorName: 'Beverage Stand',
    amount: 24.00,
    status: 'completed',
    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
    items: [
      { name: 'Cocktail', quantity: 2, price: 12.00 }
    ]
  },
  {
    id: 'tx-1007',
    cardId: 'C-4567',
    vendorId: 'V-123',
    vendorName: 'Food Stall A',
    amount: 32.75,
    status: 'completed',
    timestamp: new Date(Date.now() - 7 * 60 * 60 * 1000).toISOString(),
    items: [
      { name: 'Burger', quantity: 1, price: 15.99 },
      { name: 'Fries', quantity: 1, price: 5.00 },
      { name: 'Wings', quantity: 1, price: 11.76 }
    ]
  },
  {
    id: 'tx-1008',
    cardId: 'C-1122',
    vendorId: 'V-456',
    vendorName: 'Merchandise Booth',
    amount: 50.00,
    status: 'refunded',
    timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
    items: [
      { name: 'Hoodie', quantity: 1, price: 50.00 }
    ]
  },
  {
    id: 'tx-1009',
    cardId: 'C-7788',
    vendorId: 'V-789',
    vendorName: 'Beverage Stand',
    amount: 18.00,
    status: 'completed',
    timestamp: new Date(Date.now() - 9 * 60 * 60 * 1000).toISOString(),
    items: [
      { name: 'Beer', quantity: 3, price: 6.00 }
    ]
  },
  {
    id: 'tx-1010',
    cardId: 'C-9900',
    vendorId: 'V-123',
    vendorName: 'Food Stall A',
    amount: 22.50,
    status: 'completed',
    timestamp: new Date(Date.now() - 10 * 60 * 60 * 1000).toISOString(),
    items: [
      { name: 'Nachos', quantity: 1, price: 12.50 },
      { name: 'Soda', quantity: 2, price: 5.00 }
    ]
  }
];

export default function TransactionsPage() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [vendorFilter, setVendorFilter] = useState('all_vendors');
  const [eventFilter, setEventFilter] = useState('all_events');
  const [dateFilter, setDateFilter] = useState<Date | undefined>(undefined);
  const [expandedTransaction, setExpandedTransaction] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [events, setEvents] = useState<Event[]>([]);
  const [stats, setStats] = useState<Stats>({
    totalAmount: 0,
    completedAmount: 0,
    failedAmount: 0,
    pendingAmount: 0,
    refundedAmount: 0,
    cancelledAmount: 0,
    completedCount: 0,
    failedCount: 0,
    pendingCount: 0,
    refundedCount: 0,
    cancelledCount: 0
  });
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [isRefundDialogOpen, setIsRefundDialogOpen] = useState(false);
  const [refundReason, setRefundReason] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  // Fetch transactions
  const fetchTransactions = async (page = 1) => {
    setIsLoading(true);
    try {
      // Build query parameters
      const queryParams = new URLSearchParams({
        page: page.toString(),
        pageSize: '10'
      });

      if (searchQuery) {
        queryParams.append('search', searchQuery);
      }

      if (statusFilter !== 'all') {
        queryParams.append('status', statusFilter);
      }

      if (vendorFilter !== 'all_vendors') {
        queryParams.append('vendorId', vendorFilter);
      }

      if (eventFilter !== 'all_events') {
        queryParams.append('eventId', eventFilter);
      }

      if (dateFilter) {
        queryParams.append('dateFrom', format(dateFilter, 'yyyy-MM-dd'));
      }

      const response = await fetch(`/api/organizer/nfc/transactions?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();

      setTransactions(data.transactions);
      setTotalCount(data.totalCount);
      setTotalPages(data.totalPages);
      setCurrentPage(data.currentPage);
      setStats(data.stats);

      if (data.filters) {
        setVendors(data.filters.vendors);
        setEvents(data.filters.events);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
      toast({
        title: 'Error',
        description: 'Failed to load transactions. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchTransactions();
  }, []);

  // Handle search
  const handleSearch = () => {
    fetchTransactions(1);
  };

  // Handle filter changes
  const handleStatusChange = (status: string) => {
    setStatusFilter(status);
    fetchTransactions(1);
  };

  const handleVendorChange = (vendorId: string) => {
    setVendorFilter(vendorId);
    fetchTransactions(1);
  };

  const handleEventChange = (eventId: string) => {
    setEventFilter(eventId);
    fetchTransactions(1);
  };

  const handleDateChange = (date: Date | undefined) => {
    setDateFilter(date);
    if (date) {
      fetchTransactions(1);
    }
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > totalPages) return;
    fetchTransactions(newPage);
  };

  // Toggle transaction details
  const toggleTransactionDetails = (txId: string) => {
    setExpandedTransaction(expandedTransaction === txId ? null : txId);
  };

  // Refresh transactions
  const refreshTransactions = () => {
    setIsRefreshing(true);
    fetchTransactions(currentPage);
  };

  // Export transactions
  const exportTransactions = (format: string) => {
    toast({
      title: 'Export Started',
      description: `Exporting transactions as ${format.toUpperCase()}...`,
    });

    // Simulate export delay
    setTimeout(() => {
      toast({
        title: 'Export Complete',
        description: `Transactions have been exported as ${format.toUpperCase()}.`,
      });
    }, 1500);
  };

  // Handle refund transaction
  const handleRefundTransaction = async () => {
    if (!selectedTransaction) return;

    setIsProcessing(true);

    try {
      const response = await fetch('/api/organizer/nfc/transactions', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionId: selectedTransaction.id,
          action: 'refund',
          reason: refundReason,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }

      const data = await response.json();

      toast({
        title: 'Success',
        description: data.message || 'Transaction refunded successfully',
      });

      // Refresh transactions
      fetchTransactions(currentPage);

      // Close dialog and reset form
      setIsRefundDialogOpen(false);
      setRefundReason('');
      setSelectedTransaction(null);
    } catch (error) {
      console.error('Error refunding transaction:', error);
      toast({
        title: 'Refund Failed',
        description: error instanceof Error ? error.message : 'Failed to refund transaction. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Transaction History</h1>
          <p className="text-gray-600 mt-1">
            View and manage all NFC payment transactions
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={refreshTransactions} disabled={isLoading || isRefreshing}>
            {isRefreshing ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Refreshing...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </>
            )}
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Export Options</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => exportTransactions('csv')}>
                <FileText className="mr-2 h-4 w-4" />
                Export as CSV
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => exportTransactions('excel')}>
                <FileText className="mr-2 h-4 w-4" />
                Export as Excel
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => exportTransactions('pdf')}>
                <FileText className="mr-2 h-4 w-4" />
                Export as PDF
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Transactions</CardTitle>
          <CardDescription>
            View and filter all payment transactions across your event
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div className="flex gap-2 w-full md:w-auto">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  placeholder="Search transactions..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <Button variant="outline" onClick={handleSearch} disabled={isLoading}>
                Search
              </Button>
            </div>
            <div className="flex flex-wrap gap-2 w-full md:w-auto">
              <Select value={statusFilter} onValueChange={handleStatusChange}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="FAILED">Failed</SelectItem>
                  <SelectItem value="REFUNDED">Refunded</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                </SelectContent>
              </Select>

              <Select value={vendorFilter} onValueChange={handleVendorChange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by vendor" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all_vendors">All Vendors</SelectItem>
                  {vendors.map(vendor => (
                    <SelectItem key={vendor.id} value={vendor.id}>
                      {vendor.businessName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={eventFilter} onValueChange={handleEventChange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by event" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all_events">All Events</SelectItem>
                  {events.map(event => (
                    <SelectItem key={event.id} value={event.id}>
                      {event.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-[180px] justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateFilter ? format(dateFilter, "PPP") : "Filter by date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={dateFilter}
                    onSelect={handleDateChange}
                    initialFocus
                  />
                  {dateFilter && (
                    <div className="p-3 border-t border-border">
                      <Button
                        variant="ghost"
                        className="w-full justify-center"
                        onClick={() => {
                          setDateFilter(undefined);
                          fetchTransactions(1);
                        }}
                      >
                        Clear Date
                      </Button>
                    </div>
                  )}
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Transaction ID</TableHead>
                  <TableHead>Card ID</TableHead>
                  <TableHead>Vendor</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date & Time</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-10">
                      <div className="flex flex-col items-center justify-center">
                        <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                        <p className="text-sm text-gray-500">Loading transactions...</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : transactions.length > 0 ? (
                  transactions.map(tx => (
                    <React.Fragment key={tx.id}>
                      <TableRow className={expandedTransaction === tx.id ? 'bg-gray-50' : ''}>
                        <TableCell className="font-medium">{tx.id}</TableCell>
                        <TableCell>{tx.cardId}</TableCell>
                        <TableCell>{tx.vendorName}</TableCell>
                        <TableCell>K{tx.amount.toFixed(2)}</TableCell>
                        <TableCell>
                          <Badge variant={
                            tx.status === 'COMPLETED' ? 'default' :
                            tx.status === 'REFUNDED' ? 'secondary' :
                            tx.status === 'PENDING' ? 'outline' :
                            tx.status === 'CANCELLED' ? 'secondary' :
                            'destructive'
                          }>
                            {tx.status.toLowerCase()}
                          </Badge>
                        </TableCell>
                        <TableCell>{new Date(tx.timestamp).toLocaleString()}</TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            onClick={() => toggleTransactionDetails(tx.id)}
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                      {expandedTransaction === tx.id && (
                        <TableRow className="bg-gray-50">
                          <TableCell colSpan={7} className="p-4">
                            <div className="space-y-4">
                              <div>
                                <h4 className="font-medium mb-2">Transaction Details</h4>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                  <div>
                                    <p className="text-sm text-gray-500">Transaction ID</p>
                                    <p className="font-medium">{tx.id}</p>
                                  </div>
                                  <div>
                                    <p className="text-sm text-gray-500">Card ID</p>
                                    <p className="font-medium">{tx.cardId} ({tx.cardUid})</p>
                                  </div>
                                  <div>
                                    <p className="text-sm text-gray-500">Vendor</p>
                                    <p className="font-medium">{tx.vendorName} ({tx.vendorId})</p>
                                  </div>
                                  <div>
                                    <p className="text-sm text-gray-500">Amount</p>
                                    <p className="font-medium">K{tx.amount.toFixed(2)}</p>
                                  </div>
                                  <div>
                                    <p className="text-sm text-gray-500">Status</p>
                                    <Badge variant={
                                      tx.status === 'COMPLETED' ? 'default' :
                                      tx.status === 'REFUNDED' ? 'secondary' :
                                      tx.status === 'PENDING' ? 'outline' :
                                      tx.status === 'CANCELLED' ? 'secondary' :
                                      'destructive'
                                    }>
                                      {tx.status.toLowerCase()}
                                    </Badge>
                                  </div>
                                  <div>
                                    <p className="text-sm text-gray-500">Date & Time</p>
                                    <p className="font-medium">{new Date(tx.timestamp).toLocaleString()}</p>
                                  </div>
                                  <div>
                                    <p className="text-sm text-gray-500">Event</p>
                                    <p className="font-medium">{tx.eventTitle}</p>
                                  </div>
                                  <div>
                                    <p className="text-sm text-gray-500">Card Assigned To</p>
                                    <p className="font-medium">{tx.assignedTo}</p>
                                  </div>
                                  {tx.reference && (
                                    <div>
                                      <p className="text-sm text-gray-500">Reference</p>
                                      <p className="font-medium">{tx.reference}</p>
                                    </div>
                                  )}
                                  {tx.notes && (
                                    <div>
                                      <p className="text-sm text-gray-500">Notes</p>
                                      <p className="font-medium">{tx.notes}</p>
                                    </div>
                                  )}
                                </div>
                              </div>

                              <div>
                                <h4 className="font-medium mb-2">Items</h4>
                                <Table>
                                  <TableHeader>
                                    <TableRow>
                                      <TableHead>Item</TableHead>
                                      <TableHead className="text-right">Quantity</TableHead>
                                      <TableHead className="text-right">Price</TableHead>
                                      <TableHead className="text-right">Total</TableHead>
                                    </TableRow>
                                  </TableHeader>
                                  <TableBody>
                                    {tx.items.map((item) => (
                                      <TableRow key={item.id}>
                                        <TableCell>{item.name}</TableCell>
                                        <TableCell className="text-right">{item.quantity}</TableCell>
                                        <TableCell className="text-right">K{item.price.toFixed(2)}</TableCell>
                                        <TableCell className="text-right">K{item.totalPrice.toFixed(2)}</TableCell>
                                      </TableRow>
                                    ))}
                                    <TableRow>
                                      <TableCell colSpan={3} className="text-right font-medium">Total</TableCell>
                                      <TableCell className="text-right font-medium">K{tx.amount.toFixed(2)}</TableCell>
                                    </TableRow>
                                  </TableBody>
                                </Table>
                              </div>

                              <div className="flex justify-end gap-2">
                                <Button variant="outline" size="sm">
                                  <FileText className="mr-2 h-4 w-4" />
                                  View Receipt
                                </Button>
                                {tx.status === 'COMPLETED' && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      setSelectedTransaction(tx);
                                      setIsRefundDialogOpen(true);
                                    }}
                                  >
                                    <XCircle className="mr-2 h-4 w-4" />
                                    Refund Transaction
                                  </Button>
                                )}
                              </div>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </React.Fragment>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-6 text-gray-500">
                      No transactions found matching your search criteria
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row justify-between items-center gap-4">
          <div className="text-sm text-gray-500">
            {isLoading ? (
              <span>Loading...</span>
            ) : (
              <span>
                Showing {transactions.length} of {totalCount} transactions
              </span>
            )}
          </div>

          {totalPages > 1 && (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1 || isLoading}
              >
                Previous
              </Button>
              <span className="text-sm text-gray-500">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages || isLoading}
              >
                Next
              </Button>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Refund Dialog */}
      <Dialog open={isRefundDialogOpen} onOpenChange={setIsRefundDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Refund Transaction</DialogTitle>
            <DialogDescription>
              Are you sure you want to refund this transaction? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          {selectedTransaction && (
            <div className="space-y-4 py-2">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Transaction ID</p>
                  <p className="font-medium">{selectedTransaction.id}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Amount</p>
                  <p className="font-medium">K{selectedTransaction.amount.toFixed(2)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Vendor</p>
                  <p className="font-medium">{selectedTransaction.vendorName}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Card</p>
                  <p className="font-medium">{selectedTransaction.cardId}</p>
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="refund-reason" className="text-sm font-medium">
                  Reason for Refund
                </label>
                <Textarea
                  id="refund-reason"
                  placeholder="Enter reason for refund"
                  value={refundReason}
                  onChange={(e) => setRefundReason(e.target.value)}
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsRefundDialogOpen(false)}
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button
              onClick={handleRefundTransaction}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                'Confirm Refund'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
