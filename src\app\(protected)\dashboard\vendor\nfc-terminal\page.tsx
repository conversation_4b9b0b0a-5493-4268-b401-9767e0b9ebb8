'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import NFCTransactionProcessor from '@/components/vendor/nfc/NFCTransactionProcessor';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { Loader2, ArrowLeft, Store, Calendar, MapPin } from 'lucide-react';

interface Event {
  id: string;
  title: string;
  startDate: string;
  endDate: string;
  venue: string;
  city: string;
}

interface Product {
  id: string;
  name: string;
  price: number;
  stockQuantity: number;
  imagePath?: string;
}

interface VendorProfile {
  id: string;
  businessName: string;
}

export default function VendorNFCTerminalPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const eventId = searchParams.get('eventId') || '';
  
  console.log('NFC Terminal Page - Event ID:', eventId);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [event, setEvent] = useState<Event | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [vendorProfile, setVendorProfile] = useState<VendorProfile | null>(null);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);

        if (!eventId) {
          throw new Error('Event ID is required');
        }

        // Fetch vendor profile
        const profileResponse = await fetch('/api/vendors/profile');

        if (!profileResponse.ok) {
          if (profileResponse.status === 404) {
            router.push('/dashboard/vendor/create-profile');
            return;
          }
          throw new Error('Failed to fetch vendor profile');
        }

        const profileData = await profileResponse.json();
        setVendorProfile(profileData);

        // Create mock event data
        const mockEvent = {
          id: eventId,
          title: "Event " + eventId.substring(0, 8),
          startDate: new Date().toISOString(),
          endDate: new Date(Date.now() + 86400000).toISOString(),
          venue: "Test Venue",
          city: "Test City"
        };
        
        setEvent(mockEvent);

        // Create mock products
        const mockProducts = [
          {
            id: 'mock-product-1',
            name: 'Sample Product 1',
            price: 25.99,
            stockQuantity: 100,
            imagePath: '/images/products/sample1.jpg'
          },
          {
            id: 'mock-product-2',
            name: 'Sample Product 2',
            price: 15.50,
            stockQuantity: 50,
            imagePath: '/images/products/sample2.jpg'
          }
        ];
        
        setProducts(mockProducts);

      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
        toast({
          title: 'Error',
          description: 'Failed to load required data',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [eventId, router]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading...</span>
      </div>
    );
  }

  if (error || !event || !vendorProfile) {
    return (
      <Card className="w-full max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle>Error</CardTitle>
        </CardHeader>
        <CardContent>
          <p>{error || 'Failed to load required data'}</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => router.push('/dashboard/vendor/events')}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Events
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/dashboard/vendor/events')}
            className="mb-2"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Events
          </Button>
          <h1 className="text-3xl font-bold">{event.title}</h1>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 mt-2 text-gray-600">
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-1" />
              <span>
                {new Date(event.startDate).toLocaleDateString()} - {new Date(event.endDate).toLocaleDateString()}
              </span>
            </div>
            <div className="flex items-center">
              <MapPin className="h-4 w-4 mr-1" />
              <span>{event.venue}, {event.city}</span>
            </div>
          </div>
        </div>

        <div className="flex items-center bg-blue-50 px-4 py-2 rounded-md">
          <Store className="h-5 w-5 text-blue-600 mr-2" />
          <div>
            <p className="text-blue-800 font-medium">{vendorProfile.businessName}</p>
            <p className="text-sm text-blue-600">Vendor Mode</p>
          </div>
        </div>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>NFC Payment Terminal</CardTitle>
          <CardDescription>
            Process customer purchases using NFC cards
          </CardDescription>
        </CardHeader>
        <CardContent>
          <NFCTransactionProcessor
            eventId={eventId}
            vendorId={vendorProfile.id}
            products={products}
          />
        </CardContent>
      </Card>
    </div>
  );
}
