import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth';
import { db } from '@/lib/prisma';
import { AttendeeMatchingService } from '@/lib/attendee-matching';
import { hasFeature } from '@/config/elite-pricing';
import { EliteTier } from '@prisma/client';

export const dynamic = 'force-dynamic';

/**
 * GET /api/attendee-matching?eventId=[eventId]&limit=[limit]&criteria=[criteria]
 * Get attendee matches and networking suggestions
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const eventId = searchParams.get('eventId');
    const limit = parseInt(searchParams.get('limit') || '10');
    const criteriaParam = searchParams.get('criteria');

    if (!eventId) {
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 });
    }

    // Verify user has Elite Communication subscription
    const subscription = await db.eliteCommunication.findFirst({
      where: {
        userId: session.user.id,
        eventId,
        isActive: true
      }
    });

    if (!subscription) {
      return NextResponse.json({ error: 'No active Elite Communication subscription' }, { status: 403 });
    }

    // Check if user has access to advanced matching
    if (!hasFeature(subscription.tier, 'advancedMatching')) {
      return NextResponse.json({ 
        error: 'Advanced attendee matching requires Elite Pro subscription',
        requiredTier: 'ELITE_PRO'
      }, { status: 403 });
    }

    // Parse custom criteria if provided
    let customCriteria;
    if (criteriaParam) {
      try {
        customCriteria = JSON.parse(criteriaParam);
      } catch (error) {
        return NextResponse.json({ error: 'Invalid criteria format' }, { status: 400 });
      }
    }

    // Initialize matching service
    const matchingService = new AttendeeMatchingService(customCriteria);

    // Find matches
    const matches = await matchingService.findMatches(session.user.id, eventId, limit);

    // Generate networking suggestions
    const suggestions = matchingService.generateNetworkingSuggestions(matches);

    // Get user's profile for context
    const userProfile = await db.attendeeProfile.findFirst({
      where: {
        userId: session.user.id,
        eventId
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    });

    // Track matching analytics
    await trackMatchingAnalytics(session.user.id, eventId, matches.length);

    return NextResponse.json({
      success: true,
      matches,
      suggestions,
      userProfile,
      analytics: {
        totalMatches: matches.length,
        highCompatibilityMatches: matches.filter(m => m.compatibilityScore > 0.8).length,
        industryMatches: matches.filter(m => m.industryMatch).length,
        averageCompatibility: matches.length > 0 
          ? matches.reduce((sum, m) => sum + m.compatibilityScore, 0) / matches.length 
          : 0
      }
    });

  } catch (error) {
    console.error('Error in attendee matching:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/attendee-matching/feedback
 * Submit feedback on match quality
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      eventId,
      matchedUserId,
      rating,
      feedback,
      actionTaken,
      connectionMade
    } = body;

    if (!eventId || !matchedUserId || rating === undefined) {
      return NextResponse.json({ 
        error: 'Event ID, matched user ID, and rating are required' 
      }, { status: 400 });
    }

    // Verify user has subscription
    const subscription = await db.eliteCommunication.findFirst({
      where: {
        userId: session.user.id,
        eventId,
        isActive: true
      }
    });

    if (!subscription) {
      return NextResponse.json({ error: 'No active subscription' }, { status: 403 });
    }

    // Store feedback for algorithm improvement
    await db.matchingFeedback.create({
      data: {
        userId: session.user.id,
        eventId,
        matchedUserId,
        rating: Math.max(1, Math.min(5, rating)), // Ensure 1-5 range
        feedback: feedback || null,
        actionTaken: actionTaken || null,
        connectionMade: connectionMade || false
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Feedback submitted successfully'
    });

  } catch (error) {
    console.error('Error submitting matching feedback:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/attendee-matching/analytics?eventId=[eventId]
 * Get matching analytics and insights
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const eventId = searchParams.get('eventId');

    if (!eventId) {
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 });
    }

    // Verify user has Elite Pro subscription for analytics
    const subscription = await db.eliteCommunication.findFirst({
      where: {
        userId: session.user.id,
        eventId,
        isActive: true
      }
    });

    if (!subscription || subscription.tier !== EliteTier.ELITE_PRO) {
      return NextResponse.json({ 
        error: 'Analytics require Elite Pro subscription' 
      }, { status: 403 });
    }

    // Get matching analytics
    const analytics = await getMatchingAnalytics(session.user.id, eventId);

    return NextResponse.json({
      success: true,
      analytics
    });

  } catch (error) {
    console.error('Error fetching matching analytics:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper functions
async function trackMatchingAnalytics(userId: string, eventId: string, matchCount: number) {
  try {
    await db.matchingAnalytics.upsert({
      where: {
        userId_eventId: {
          userId,
          eventId
        }
      },
      update: {
        lastMatchingDate: new Date(),
        totalMatchesGenerated: {
          increment: matchCount
        },
        matchingSessionCount: {
          increment: 1
        }
      },
      create: {
        userId,
        eventId,
        totalMatchesGenerated: matchCount,
        matchingSessionCount: 1,
        lastMatchingDate: new Date()
      }
    });
  } catch (error) {
    console.error('Error tracking matching analytics:', error);
  }
}

async function getMatchingAnalytics(userId: string, eventId: string) {
  try {
    const analytics = await db.matchingAnalytics.findUnique({
      where: {
        userId_eventId: {
          userId,
          eventId
        }
      }
    });

    const feedback = await db.matchingFeedback.findMany({
      where: {
        userId,
        eventId
      }
    });

    const connections = await db.message.count({
      where: {
        senderId: userId,
        eventId,
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
        }
      }
    });

    return {
      totalMatches: analytics?.totalMatchesGenerated || 0,
      matchingSessions: analytics?.matchingSessionCount || 0,
      lastMatching: analytics?.lastMatchingDate,
      averageRating: feedback.length > 0 
        ? feedback.reduce((sum, f) => sum + f.rating, 0) / feedback.length 
        : 0,
      connectionsMade: connections,
      feedbackCount: feedback.length,
      successRate: feedback.length > 0 
        ? feedback.filter(f => f.connectionMade).length / feedback.length 
        : 0
    };
  } catch (error) {
    console.error('Error getting matching analytics:', error);
    return null;
  }
}
