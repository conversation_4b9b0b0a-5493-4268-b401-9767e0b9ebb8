/**
 * Seed API Tiers
 * 
 * This script seeds the API tiers into the database.
 * Run with: node scripts/seed-api-tiers.js
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// API tiers configuration
const API_TIERS = {
  FREE: {
    name: 'Free',
    requestsPerMinute: 10,
    requestsPerDay: 100,
    features: ['Basic event data', 'Limited search']
  },
  BASIC: {
    name: 'Basic',
    requestsPerMinute: 60,
    requestsPerDay: 5000,
    features: ['Full event data', 'Advanced search', 'Categories and types']
  },
  PROFESSIONAL: {
    name: 'Professional',
    requestsPerMinute: 300,
    requestsPerDay: 20000,
    features: ['Everything in Basic', 'Analytics data', 'Ticket availability']
  },
  ENTERPRISE: {
    name: 'Enterprise',
    requestsPerMinute: 1000,
    requestsPerDay: -1, // Unlimited
    features: ['Everything in Professional', 'Custom endpoints', 'SLA guarantees']
  }
};

// Enhanced API tiers with pricing and UI information
const apiTiersWithPricing = {
  ...API_TIERS,
  pricing: {
    monthly: {
      FREE: 0,
      BASIC: 29,
      PROFESSIONAL: 99,
      ENTERPRISE: 499
    },
    yearly: {
      FREE: 0,
      BASIC: 279,
      PROFESSIONAL: 949,
      ENTERPRISE: 4789
    }
  },
  features: {
    FREE: [
      'Basic event data',
      'Limited search capabilities',
      'Community support',
      '10 requests per minute',
      '100 requests per day'
    ],
    BASIC: [
      'Full event data',
      'Advanced search capabilities',
      'Categories and types filtering',
      'Email support',
      '60 requests per minute',
      '5,000 requests per day'
    ],
    PROFESSIONAL: [
      'Everything in Basic',
      'Analytics data access',
      'Ticket availability',
      'Organizer API access',
      'Priority support',
      '300 requests per minute',
      '20,000 requests per day'
    ],
    ENTERPRISE: [
      'Everything in Professional',
      'Custom endpoints',
      'SLA guarantees',
      'Dedicated support',
      '1,000 requests per minute',
      'Unlimited requests per day'
    ]
  },
  descriptions: {
    FREE: 'Basic access for developers',
    BASIC: 'For small applications',
    PROFESSIONAL: 'For growing businesses',
    ENTERPRISE: 'For large organizations'
  },
  icons: {
    FREE: 'Zap',
    BASIC: 'Database',
    PROFESSIONAL: 'BarChart4',
    ENTERPRISE: 'Shield'
  },
  gradients: {
    FREE: 'from-gray-50 to-gray-100 border-gray-200',
    BASIC: 'from-blue-50 to-blue-100 border-blue-200',
    PROFESSIONAL: 'from-purple-50 to-purple-100 border-purple-200',
    ENTERPRISE: 'from-indigo-50 to-indigo-100 border-indigo-200'
  },
  buttonVariants: {
    FREE: 'outline',
    BASIC: 'outline',
    PROFESSIONAL: 'default',
    ENTERPRISE: 'outline'
  },
  ctaText: {
    FREE: 'Get Started',
    BASIC: 'Subscribe',
    PROFESSIONAL: 'Subscribe',
    ENTERPRISE: 'Contact Sales'
  },
  popular: 'PROFESSIONAL'
};

async function seedApiTiers() {
  try {
    console.log('Seeding API tiers...');

    // Check if API tiers already exist in the database
    const existingTiers = await prisma.systemSetting.findUnique({
      where: { key: 'api_tiers' }
    });

    if (existingTiers) {
      // Update existing tiers
      await prisma.systemSetting.update({
        where: { key: 'api_tiers' },
        data: {
          value: apiTiersWithPricing,
          updatedAt: new Date()
        }
      });
      console.log('Updated existing API tiers');
    } else {
      // Create new tiers
      await prisma.systemSetting.create({
        data: {
          key: 'api_tiers',
          value: apiTiersWithPricing,
          updatedAt: new Date()
        }
      });
      console.log('Created new API tiers');
    }

    console.log('API tiers seeded successfully');
  } catch (error) {
    console.error('Error seeding API tiers:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
seedApiTiers();
