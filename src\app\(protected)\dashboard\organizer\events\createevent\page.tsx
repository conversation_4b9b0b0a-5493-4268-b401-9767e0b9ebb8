"use client"

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useLoadScript } from "@react-google-maps/api";
import { EventFormData } from "@/types/events";
import { useCurrentRole } from "@/hooks/use-current-role";
import { AlertCircle, CheckCircle2 } from "lucide-react";
import { VerificationStatusAlert } from "@/components/organizer/verification-status-alert";
import EventCreationWizard from "@/components/ui/eventform/EventCreationWizard";

const CreateEventPage: React.FC = () => {
  const router = useRouter();
  const role = useCurrentRole();
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [verificationMessage, setVerificationMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const { isLoaded } = useLoadScript({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || "",
    libraries: ["places"],
  });

  const handleSubmit = async (formData: EventFormData) => {
    setIsLoading(true);
    setError("");
    setSuccess("");

    try {
      // Create FormData object
      const submitData = new FormData();

      // Add basic fields
      submitData.append("title", formData.title);
      submitData.append("description", formData.description);
      submitData.append("eventType", formData.eventType);
      submitData.append("category", formData.category);
      submitData.append("startDate", formData.startDate);
      submitData.append("endDate", formData.endDate);
      submitData.append("startTime", formData.startTime);
      submitData.append("endTime", formData.endTime);
      submitData.append("location", formData.location);
      if (formData.venue) {
        submitData.append("venue", formData.venue);
      }

      // Add venue capacity if available
      if (formData.venueCapacity) {
        console.log('Adding venue capacity:', formData.venueCapacity);
        submitData.append("venueCapacity", formData.venueCapacity.toString());
      }

      // Add draft flag if present
      if (formData.saveAsDraft) {
        submitData.append("saveAsDraft", "true");
        console.log('Saving event as draft');
      }

      // Add virtual platform if it's a virtual or hybrid event
      if ((formData.eventType === 'ONLINE' || formData.eventType === 'HYBRID') && formData.virtualPlatform) {
        submitData.append("virtualPlatform", formData.virtualPlatform);
      }

      // Add virtual link if available
      if (formData.virtualLink) {
        submitData.append("virtualLink", formData.virtualLink);
      }

      // Add image if available
      if (formData.image) {
        submitData.append("image", formData.image);
      }

      // Add ticket information
      if (formData.ticketTypes && formData.ticketTypes.length > 0) {
        submitData.append("ticketTypes", JSON.stringify(formData.ticketTypes));
      }

      // Add event settings if available
      if (formData.eventSettings) {
        submitData.append("eventSettings", JSON.stringify(formData.eventSettings));
      }

      // Add SEO settings if available
      if (formData.seoSettings) {
        submitData.append("seoSettings", JSON.stringify(formData.seoSettings));
      }

      // Add promo codes if available
      if (formData.promoCodes && formData.promoCodes.length > 0) {
        submitData.append("promoCodes", JSON.stringify(formData.promoCodes));
      }

      // Add parking management data if available
      if (formData.parkingManagement) {
        console.log('Adding parking management data:', formData.parkingManagement);
        submitData.append("parkingManagement", JSON.stringify(formData.parkingManagement));
      }

      // Add age restriction data if available
      if (formData.ageRestriction) {
        console.log('Adding age restriction data:', formData.ageRestriction);
        submitData.append("ageRestriction", JSON.stringify(formData.ageRestriction));
      }

      // Add sponsors if available
      console.log('Sponsors data before check:', formData.sponsors);

      // Create a dedicated sponsors array to ensure proper format
      let sponsorsArray = [];
      console.log('Raw sponsors data type:', typeof formData.sponsors);
      console.log('Raw sponsors data:', formData.sponsors);

      if (Array.isArray(formData.sponsors)) {
        console.log('Sponsors is already an array');
        // Create a new array to avoid reference issues
        sponsorsArray = [...formData.sponsors];
      } else if (formData.sponsors && typeof formData.sponsors === 'object') {
        console.log('Sponsors is a single object, converting to array');
        // Handle single sponsor object
        sponsorsArray = [formData.sponsors];
      }

      console.log('Sponsors array after conversion:', sponsorsArray);
      console.log('Sponsors array length after conversion:', sponsorsArray.length);
      console.log('Sponsors array JSON after conversion:', JSON.stringify(sponsorsArray));

      // Validate and clean sponsors data
      const cleanedSponsors = sponsorsArray.map(sponsor => ({
        id: sponsor.id || crypto.randomUUID(),
        name: sponsor.name || '',
        logo: sponsor.logo || '',
        website: sponsor.website || '',
        tier: sponsor.tier || 'BRONZE',
        amount: typeof sponsor.amount === 'number' ? sponsor.amount : parseFloat(sponsor.amount?.toString() || '0')
      }));

      console.log('Cleaned sponsors data:', cleanedSponsors);
      console.log('Sponsors JSON:', JSON.stringify(cleanedSponsors));

      // IMPORTANT: Add sponsors data as a JSON string
      console.log('Adding sponsors data as JSON string:', JSON.stringify(cleanedSponsors));
      submitData.append("sponsors", JSON.stringify(cleanedSponsors));

      // IMPORTANT: Also add a dedicated field for sponsors data to ensure it's received
      submitData.append("sponsorsData", JSON.stringify(cleanedSponsors));

      // IMPORTANT: Add a special field with the raw sponsors data
      submitData.append("rawSponsors", JSON.stringify(cleanedSponsors));

      // Add each sponsor individually
      cleanedSponsors.forEach((sponsor, index) => {
        console.log(`Adding sponsor ${index}:`, sponsor);
        submitData.append(`sponsor_${index}_name`, sponsor.name);
        submitData.append(`sponsor_${index}_tier`, sponsor.tier);
        submitData.append(`sponsor_${index}_logo`, sponsor.logo);
        submitData.append(`sponsor_${index}_website`, sponsor.website);
        submitData.append(`sponsor_${index}_amount`, sponsor.amount.toString());
      });

      // Add a special field with the count
      submitData.append("sponsorsCount", cleanedSponsors.length.toString());

      // Add a special field to indicate sponsors were processed
      submitData.append("hasSponsors", cleanedSponsors.length > 0 ? "true" : "false");

      // Add a special field for a single sponsor
      if (cleanedSponsors.length === 1) {
        console.log('Adding single sponsor data');
        submitData.append("singleSponsor", JSON.stringify(cleanedSponsors[0]));
        submitData.append("hasSingleSponsor", "true");
      }

      // Log form data for debugging
      console.log('Submitting form data:', {
        title: formData.title,
        description: formData.description,
        eventType: formData.eventType,
        category: formData.category,
        startDate: formData.startDate,
        endDate: formData.endDate,
        startTime: formData.startTime,
        endTime: formData.endTime,
        location: formData.location,
        venue: formData.venue,
        // Don't log the image for privacy
        hasImage: !!formData.image,
        hasTicketTypes: !!(formData.ticketTypes && formData.ticketTypes.length > 0),
      });

      // Log the form data being sent
      console.log('Submitting event data:', {
        title: formData.title,
        description: formData.description?.substring(0, 50) + '...',
        eventType: formData.eventType,
        category: formData.category,
        startDate: formData.startDate,
        endDate: formData.endDate,
        startTime: formData.startTime,
        endTime: formData.endTime,
        location: formData.location,
        venue: formData.venue,
        virtualPlatform: formData.virtualPlatform,
        isFree: formData.isFree,
        hasTicketTypes: !!(formData.ticketTypes && formData.ticketTypes.length > 0),
        hasParkingManagement: !!formData.parkingManagement,
        hasAgeRestriction: !!formData.ageRestriction,
        hasSponsors: !!(formData.sponsors && formData.sponsors.length > 0),
        venueCapacity: formData.venueCapacity,
      });

      // Log the FormData entries for debugging
      console.log('FormData entries:');
      Array.from(submitData.entries()).forEach(([key, value]) => {
        if (value instanceof File) {
          console.log(`${key}: [File] ${value.name} (${value.size} bytes)`);
        } else {
          console.log(`${key}: ${value.toString().substring(0, 100)}${value.toString().length > 100 ? '...' : ''}`);
        }
      });

      // Send the request to the API
      const response = await fetch("/api/dashboard/organiser/events/create", {
        method: "POST",
        body: submitData,
      });

      // Log response status for debugging
      console.log('API response status:', response.status);
      console.log('API response headers:', Object.fromEntries(response.headers.entries()));

      // Get the response text first to ensure we can see the raw response
      const responseText = await response.text();
      console.log('API response text:', responseText);

      // Try to parse the response as JSON
      let data;
      try {
        data = JSON.parse(responseText);
        console.log('API response data:', data);
      } catch (parseError) {
        console.error('Error parsing response JSON:', parseError);
        throw new Error('Invalid response format from server');
      }

      if (!response.ok) {
        // Handle validation errors
        if (response.status === 400 && data.errors) {
          console.error('Validation errors:', data.errors);
          let errorMessage = 'Validation failed';

          // Check if data.errors is an object with values
          if (data.errors && typeof data.errors === 'object' && Object.keys(data.errors).length > 0) {
            errorMessage = Object.values(data.errors).join(", ");
          } else if (data.error) {
            // If there's a general error message, use that
            errorMessage = data.error;
          }

          throw new Error(errorMessage);
        }
        throw new Error(data.error || data.details || "Failed to create event");
      }

      // Success
      if (formData.saveAsDraft) {
        setSuccess("Event saved as draft successfully!");
      } else {
        setSuccess("Event created successfully!");
      }

      // Set verification message if provided
      if (data.verificationMessage) {
        setVerificationMessage(data.verificationMessage);
      }

      // If we have an event ID, redirect to the event's seating page
      if (data.event && data.event.id) {
        console.log('Event created with ID:', data.event.id);
        // Redirect to the event's seating page
        router.push(`/dashboard/organizer/events/${data.event.id}/seating`);
      } else {
        // Redirect to events list page after a short delay
        setTimeout(() => {
          router.push(`/dashboard/organizer/events/myEvents`);
        }, 1500);
      }

    } catch (error: any) {
      console.error("Error creating event:", error);

      // Log the error stack trace if available
      if (error.stack) {
        console.error("Error stack trace:", error.stack);
      }

      // Provide more detailed error messages
      let errorMessage = "Failed to create event";

      if (error.message) {
        // Extract the most useful part of the error message
        if (error.message.includes("validation") || error.message.includes("required")) {
          errorMessage = error.message;
        } else if (error.message.includes("Authentication") || error.message.includes("Unauthorized")) {
          errorMessage = "You need to be logged in as an organizer to create events";
        } else if (error.message.includes("duplicate") || error.message.includes("already exists") || error.message.includes("same title")) {
          errorMessage = "An event with this title already exists. Please choose a different title.";
        } else if (error.message.includes("Invalid response format")) {
          errorMessage = "Server returned an invalid response. Please try again or contact support.";
        } else if (error.message.includes("Failed to fetch") || error.message.includes("NetworkError")) {
          errorMessage = "Network error. Please check your internet connection and try again.";
        } else {
          errorMessage = error.message;
        }
      }

      setError(errorMessage);

      // Log additional diagnostic information
      console.log('Form data that caused the error:', {
        title: formData.title,
        eventType: formData.eventType,
        category: formData.category,
        startDate: formData.startDate,
        endDate: formData.endDate,
        hasImage: !!formData.image,
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center min-h-[76vh]">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (role !== "ORGANIZER") {
    return (
      <div className="max-w-7xl mx-auto p-6 min-h-screen">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center">
          <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
          <p className="text-red-700">You are not permitted to view this page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 py-4 sm:py-6 min-h-screen">
      {/* Status Messages */}
      {/* Verification Status Alert */}
      <VerificationStatusAlert />

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-3 flex items-center">
          <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-3 flex items-center">
          <CheckCircle2 className="w-5 h-5 text-green-500 mr-2" />
          <p className="text-green-700">{success}</p>
        </div>
      )}

      {verificationMessage && (
        <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-3 flex items-center">
          <AlertCircle className="w-5 h-5 text-blue-500 mr-2" />
          <p className="text-blue-700">{verificationMessage}</p>
        </div>
      )}

      {/* Event Creation Wizard */}
      <EventCreationWizard
        onSubmit={handleSubmit}
        isLoading={isLoading}
      />
    </div>
  );
};

export default CreateEventPage;
