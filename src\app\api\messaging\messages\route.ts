import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth';
import { db } from '@/lib/prisma';
import { EliteTier, MessageType } from '@prisma/client';
import { hasFeature } from '@/config/elite-pricing';

export const dynamic = 'force-dynamic';

/**
 * GET /api/messaging/messages?eventId=[eventId]&recipientId=[recipientId]&chatRoomId=[chatRoomId]&page=[page]&limit=[limit]
 * Get message history for direct messages or chat rooms
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const eventId = searchParams.get('eventId');
    const recipientId = searchParams.get('recipientId');
    const chatRoomId = searchParams.get('chatRoomId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');

    if (!eventId) {
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 });
    }

    // Verify user has Elite Communication subscription
    const subscription = await db.eliteCommunication.findFirst({
      where: {
        userId: session.user.id,
        eventId,
        isActive: true
      }
    });

    if (!subscription) {
      return NextResponse.json({ error: 'No active Elite Communication subscription' }, { status: 403 });
    }

    // Check if user has messaging permissions
    if (!hasFeature(subscription.tier, 'directMessaging')) {
      return NextResponse.json({ error: 'Messaging not available in your tier' }, { status: 403 });
    }

    let whereClause: any = {
      eventId
    };

    if (chatRoomId) {
      // Chat room messages
      whereClause.chatRoomId = chatRoomId;
      
      // Verify user has access to chat room
      const chatRoom = await db.chatRoom.findFirst({
        where: {
          id: chatRoomId,
          eventId,
          OR: [
            { createdById: session.user.id },
            {
              members: {
                some: {
                  userId: session.user.id
                }
              }
            }
          ]
        }
      });

      if (!chatRoom) {
        return NextResponse.json({ error: 'Chat room not found or access denied' }, { status: 404 });
      }
    } else if (recipientId) {
      // Direct messages
      whereClause.OR = [
        {
          senderId: session.user.id,
          recipientId: recipientId
        },
        {
          senderId: recipientId,
          recipientId: session.user.id
        }
      ];
      whereClause.chatRoomId = null;
    } else {
      return NextResponse.json({ error: 'Either recipientId or chatRoomId is required' }, { status: 400 });
    }

    const messages = await db.message.findMany({
      where: whereClause,
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            image: true
          }
        },
        recipient: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: (page - 1) * limit,
      take: limit
    });

    const totalCount = await db.message.count({
      where: whereClause
    });

    return NextResponse.json({
      messages: messages.reverse(), // Reverse to show oldest first
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching messages:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/messaging/messages
 * Send a new message
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      eventId,
      recipientId,
      chatRoomId,
      content,
      messageType = 'TEXT',
      fileUrl,
      fileName,
      fileSize
    } = body;

    if (!eventId || !content) {
      return NextResponse.json({ error: 'Event ID and content are required' }, { status: 400 });
    }

    if (!recipientId && !chatRoomId) {
      return NextResponse.json({ error: 'Either recipientId or chatRoomId is required' }, { status: 400 });
    }

    // Verify user has Elite Communication subscription
    const subscription = await db.eliteCommunication.findFirst({
      where: {
        userId: session.user.id,
        eventId,
        isActive: true
      }
    });

    if (!subscription) {
      return NextResponse.json({ error: 'No active Elite Communication subscription' }, { status: 403 });
    }

    // Check if user has messaging permissions
    if (!hasFeature(subscription.tier, 'directMessaging')) {
      return NextResponse.json({ error: 'Messaging not available in your tier' }, { status: 403 });
    }

    // Validate file sharing permissions
    if (messageType === 'FILE' && !hasFeature(subscription.tier, 'fileSharing')) {
      return NextResponse.json({ error: 'File sharing not available in your tier' }, { status: 403 });
    }

    // Validate chat room access if applicable
    if (chatRoomId) {
      const chatRoom = await db.chatRoom.findFirst({
        where: {
          id: chatRoomId,
          eventId,
          OR: [
            { createdById: session.user.id },
            {
              members: {
                some: {
                  userId: session.user.id
                }
              }
            }
          ]
        }
      });

      if (!chatRoom) {
        return NextResponse.json({ error: 'Chat room not found or access denied' }, { status: 404 });
      }

      // Check if user has access to exclusive chat rooms
      if (chatRoom.roomType === 'ELITE_PRO_EXCLUSIVE' && !hasFeature(subscription.tier, 'exclusiveChatRooms')) {
        return NextResponse.json({ error: 'Access to Elite Pro chat rooms not available in your tier' }, { status: 403 });
      }
    }

    // Validate recipient access for direct messages
    if (recipientId) {
      const recipientSubscription = await db.eliteCommunication.findFirst({
        where: {
          userId: recipientId,
          eventId,
          isActive: true
        }
      });

      if (!recipientSubscription) {
        return NextResponse.json({ error: 'Recipient does not have active subscription' }, { status: 400 });
      }

      // Check recipient's message preferences
      const recipientProfile = await db.attendeeProfile.findFirst({
        where: {
          userId: recipientId,
          eventId
        }
      });

      if (recipientProfile?.allowMessages === 'NONE') {
        return NextResponse.json({ error: 'Recipient has disabled messages' }, { status: 403 });
      }

      if (recipientProfile?.allowMessages === 'ELITE_ONLY' && subscription.tier === EliteTier.BASIC) {
        return NextResponse.json({ error: 'Recipient only accepts messages from Elite members' }, { status: 403 });
      }
    }

    // Create the message
    const message = await db.message.create({
      data: {
        senderId: session.user.id,
        recipientId: recipientId || null,
        eventId,
        chatRoomId: chatRoomId || null,
        content,
        messageType: messageType as MessageType,
        fileUrl: fileUrl || null,
        fileName: fileName || null,
        fileSize: fileSize || null
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            image: true
          }
        },
        recipient: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      message
    });

  } catch (error) {
    console.error('Error sending message:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/messaging/messages/[messageId]
 * Mark message as read
 */
export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const messageId = searchParams.get('messageId');

    if (!messageId) {
      return NextResponse.json({ error: 'Message ID is required' }, { status: 400 });
    }

    // Find the message and verify user is the recipient
    const message = await db.message.findFirst({
      where: {
        id: messageId,
        OR: [
          { recipientId: session.user.id },
          {
            chatRoom: {
              members: {
                some: {
                  userId: session.user.id
                }
              }
            }
          }
        ]
      }
    });

    if (!message) {
      return NextResponse.json({ error: 'Message not found' }, { status: 404 });
    }

    // Update read status
    const updatedMessage = await db.message.update({
      where: { id: messageId },
      data: { readAt: new Date() }
    });

    return NextResponse.json({
      success: true,
      message: updatedMessage
    });

  } catch (error) {
    console.error('Error marking message as read:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
