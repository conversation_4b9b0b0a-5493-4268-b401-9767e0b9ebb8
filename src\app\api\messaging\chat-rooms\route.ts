import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth';
import { db } from '@/lib/prisma';
import { ChatRoomType, EliteTier } from '@prisma/client';
import { hasFeature } from '@/config/elite-pricing';

export const dynamic = 'force-dynamic';

/**
 * GET /api/messaging/chat-rooms?eventId=[eventId]
 * Get available chat rooms for an event
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const eventId = searchParams.get('eventId');

    if (!eventId) {
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 });
    }

    // Verify user has Elite Communication subscription
    const subscription = await db.eliteCommunication.findFirst({
      where: {
        userId: session.user.id,
        eventId,
        isActive: true
      }
    });

    if (!subscription) {
      return NextResponse.json({ error: 'No active Elite Communication subscription' }, { status: 403 });
    }

    // Build where clause based on user's tier
    let whereClause: any = {
      eventId,
      OR: [
        { roomType: 'GENERAL' },
        { createdById: session.user.id }
      ]
    };

    // Add Elite-specific rooms if user has access
    if (hasFeature(subscription.tier, 'exclusiveChatRooms')) {
      whereClause.OR.push(
        { roomType: 'ELITE_EXCLUSIVE' },
        { roomType: 'ELITE_PRO_EXCLUSIVE' }
      );
    } else if (subscription.tier === EliteTier.ELITE) {
      whereClause.OR.push({ roomType: 'ELITE_EXCLUSIVE' });
    }

    const chatRooms = await db.chatRoom.findMany({
      where: whereClause,
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            image: true
          }
        },
        members: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true
              }
            }
          }
        },
        _count: {
          select: {
            messages: true,
            members: true
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    // Filter out rooms user doesn't have access to
    const accessibleRooms = chatRooms.filter(room => {
      if (room.roomType === 'ELITE_PRO_EXCLUSIVE' && subscription.tier !== EliteTier.ELITE_PRO) {
        return false;
      }
      if (room.roomType === 'ELITE_EXCLUSIVE' && subscription.tier === EliteTier.BASIC) {
        return false;
      }
      return true;
    });

    return NextResponse.json({
      chatRooms: accessibleRooms
    });

  } catch (error) {
    console.error('Error fetching chat rooms:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/messaging/chat-rooms
 * Create a new chat room
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      eventId,
      name,
      description,
      roomType = 'GENERAL',
      isPrivate = false,
      maxMembers
    } = body;

    if (!eventId || !name) {
      return NextResponse.json({ error: 'Event ID and name are required' }, { status: 400 });
    }

    // Verify user has Elite Communication subscription
    const subscription = await db.eliteCommunication.findFirst({
      where: {
        userId: session.user.id,
        eventId,
        isActive: true
      }
    });

    if (!subscription) {
      return NextResponse.json({ error: 'No active Elite Communication subscription' }, { status: 403 });
    }

    // Check if user can create exclusive chat rooms
    if ((roomType === 'ELITE_EXCLUSIVE' || roomType === 'ELITE_PRO_EXCLUSIVE') && 
        !hasFeature(subscription.tier, 'exclusiveChatRooms')) {
      return NextResponse.json({ error: 'Creating exclusive chat rooms not available in your tier' }, { status: 403 });
    }

    // Validate room type permissions
    if (roomType === 'ELITE_PRO_EXCLUSIVE' && subscription.tier !== EliteTier.ELITE_PRO) {
      return NextResponse.json({ error: 'Only Elite Pro members can create Elite Pro exclusive rooms' }, { status: 403 });
    }

    // Create the chat room
    const chatRoom = await db.chatRoom.create({
      data: {
        eventId,
        name,
        description: description || null,
        roomType: roomType as ChatRoomType,
        isPrivate,
        maxMembers: maxMembers || null,
        createdById: session.user.id
      },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    });

    // Add creator as a member
    await db.chatRoomMember.create({
      data: {
        chatRoomId: chatRoom.id,
        userId: session.user.id,
        role: 'ADMIN'
      }
    });

    return NextResponse.json({
      success: true,
      chatRoom
    });

  } catch (error) {
    console.error('Error creating chat room:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/messaging/chat-rooms/[roomId]/join
 * Join a chat room
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const roomId = searchParams.get('roomId');
    const action = searchParams.get('action');

    if (!roomId) {
      return NextResponse.json({ error: 'Room ID is required' }, { status: 400 });
    }

    // Find the chat room
    const chatRoom = await db.chatRoom.findUnique({
      where: { id: roomId },
      include: {
        members: true,
        _count: {
          select: {
            members: true
          }
        }
      }
    });

    if (!chatRoom) {
      return NextResponse.json({ error: 'Chat room not found' }, { status: 404 });
    }

    // Verify user has subscription for this event
    const subscription = await db.eliteCommunication.findFirst({
      where: {
        userId: session.user.id,
        eventId: chatRoom.eventId,
        isActive: true
      }
    });

    if (!subscription) {
      return NextResponse.json({ error: 'No active Elite Communication subscription' }, { status: 403 });
    }

    if (action === 'join') {
      // Check if user has access to this room type
      if (chatRoom.roomType === 'ELITE_PRO_EXCLUSIVE' && subscription.tier !== EliteTier.ELITE_PRO) {
        return NextResponse.json({ error: 'Elite Pro membership required' }, { status: 403 });
      }

      if (chatRoom.roomType === 'ELITE_EXCLUSIVE' && subscription.tier === EliteTier.BASIC) {
        return NextResponse.json({ error: 'Elite membership required' }, { status: 403 });
      }

      // Check if room is full
      if (chatRoom.maxMembers && chatRoom._count.members >= chatRoom.maxMembers) {
        return NextResponse.json({ error: 'Chat room is full' }, { status: 400 });
      }

      // Check if user is already a member
      const existingMember = chatRoom.members.find(member => member.userId === session.user.id);
      if (existingMember) {
        return NextResponse.json({ error: 'Already a member of this room' }, { status: 400 });
      }

      // Add user to room
      await db.chatRoomMember.create({
        data: {
          chatRoomId: roomId,
          userId: session.user.id,
          role: 'MEMBER'
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Successfully joined chat room'
      });

    } else if (action === 'leave') {
      // Remove user from room
      await db.chatRoomMember.deleteMany({
        where: {
          chatRoomId: roomId,
          userId: session.user.id
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Successfully left chat room'
      });

    } else {
      return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

  } catch (error) {
    console.error('Error managing chat room membership:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
