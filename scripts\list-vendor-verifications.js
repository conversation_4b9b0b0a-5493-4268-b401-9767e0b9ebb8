const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function listVendorVerifications() {
  try {
    // Get all vendor verifications
    const verifications = await prisma.vendorVerification.findMany({
      include: {
        user: {
          select: {
            name: true,
            email: true,
            vendorProfile: {
              select: {
                businessName: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`Total vendor verifications: ${verifications.length}`);
    
    if (verifications.length > 0) {
      console.log('\nAll verifications:');
      verifications.forEach((v, i) => {
        console.log(`${i + 1}. ID: ${v.id}`);
        console.log(`   Status: ${v.status}`);
        console.log(`   User: ${v.user.name || 'Unknown'} (${v.user.email || 'No email'})`);
        console.log(`   Business: ${v.user.vendorProfile?.businessName || 'No business name'}`);
        console.log(`   Created: ${v.createdAt}`);
        console.log('---');
      });
    } else {
      console.log('No vendor verifications found in the database.');
    }
    
    // Count by status
    const pendingCount = verifications.filter(v => v.status === 'PENDING').length;
    const approvedCount = verifications.filter(v => v.status === 'APPROVED').length;
    const rejectedCount = verifications.filter(v => v.status === 'REJECTED').length;
    
    console.log('\nStatus summary:');
    console.log(`Pending: ${pendingCount}`);
    console.log(`Approved: ${approvedCount}`);
    console.log(`Rejected: ${rejectedCount}`);
    
  } catch (error) {
    console.error('Error listing vendor verifications:', error);
  } finally {
    await prisma.$disconnect();
  }
}

listVendorVerifications()
  .then(() => console.log('Done!'))
  .catch((error) => console.error('Script failed:', error));
