import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import AdminPartnerVerificationsClient from '@/components/admin/partners/verifications-client';

// Define session type
type UserSession = {
  user?: {
    id?: string;
    role?: string;
  };
};

export default async function AdminPartnerVerificationsPage() {
  const session = await getSession() as UserSession;

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only admins can access this page
  if (session.user.role !== 'ADMIN' && session.user.role !== 'SUPERADMIN') {
    redirect('/dashboard');
  }

  return (
    <div className="container mx-auto py-8">
      <Suspense fallback={<div className="animate-pulse">Loading verifications...</div>}>
        <AdminPartnerVerificationsClient />
      </Suspense>
    </div>
  );
}
