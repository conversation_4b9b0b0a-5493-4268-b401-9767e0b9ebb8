const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Array of notification types
const notificationTypes = [
  'EVENT_SUBMITTED',
  'EVENT_APPROVED',
  'EVENT_REJECTED',
  'EVENT_PUBLISHED',
  'TICKET_PURCHASED',
  'TICKET_REFUNDED',
  'VENDOR_APPLICATION',
  'VENDOR_APPROVED',
  'VENDOR_REJECTED',
  'FEATURING_EXPIRING_SOON',
  'FEATURING_EXPIRED',
  'EVENT_FEATURED',
  'PAYMENT_RECEIVED',
  'PAYMENT_FAILED',
  'TEAM_INVITATION',
  'TEAM_JOINED',
  'SYSTEM'
];

// Sample notification messages for each type
const notificationMessages = {
  'EVENT_SUBMITTED': 'Your event "{eventName}" has been submitted for review.',
  'EVENT_APPROVED': 'Your event "{eventName}" has been approved and is ready to be published.',
  'EVENT_REJECTED': 'Your event "{eventName}" has been rejected. Please check the comments for more information.',
  'EVENT_PUBLISHED': 'Your event "{eventName}" has been published and is now visible to users.',
  'TICKET_PURCHASED': 'A ticket for "{eventName}" has been purchased by {userName}.',
  'TICKET_REFUNDED': 'A ticket refund has been processed for "{eventName}".',
  'VENDOR_APPLICATION': '{vendorName} has applied to be a vendor at your event "{eventName}".',
  'VENDOR_APPROVED': 'Your vendor application for "{eventName}" has been approved.',
  'VENDOR_REJECTED': 'Your vendor application for "{eventName}" has been rejected.',
  'FEATURING_EXPIRING_SOON': 'Your event "{eventName}" will stop being featured in 3 days.',
  'FEATURING_EXPIRED': 'Your event "{eventName}" is no longer featured.',
  'EVENT_FEATURED': 'Your event "{eventName}" is now featured on our platform.',
  'PAYMENT_RECEIVED': 'Payment of {amount} has been received for "{eventName}".',
  'PAYMENT_FAILED': 'Payment for "{eventName}" has failed. Please update your payment method.',
  'TEAM_INVITATION': 'You have been invited to join the team for "{eventName}".',
  'TEAM_JOINED': '{userName} has joined your team for "{eventName}".',
  'SYSTEM': 'System maintenance scheduled for {date}. The platform may be unavailable for a short period.'
};

// Sample event names
const eventNames = [
  'Summer Music Festival',
  'Tech Conference 2025',
  'Food & Wine Expo',
  'Charity Gala Dinner',
  'Art Exhibition Opening',
  'Business Networking Event',
  'Sports Tournament',
  'Comedy Night',
  'Workshop Series',
  'Product Launch'
];

// Sample user names
const userNames = [
  'John Smith',
  'Jane Doe',
  'Michael Johnson',
  'Sarah Williams',
  'David Brown',
  'Emily Davis',
  'Robert Wilson',
  'Jennifer Taylor',
  'William Anderson',
  'Elizabeth Thomas'
];

// Sample vendor names
const vendorNames = [
  'Tasty Treats Catering',
  'Sound & Lighting Pro',
  'Event Decor Specialists',
  'Premium Beverages',
  'Photo Booth Fun',
  'Craft Market Vendors',
  'Food Truck Delights',
  'Merchandise Corner',
  'Tech Gadgets Stand',
  'Artisan Crafts'
];

// Sample amounts
const amounts = [
  '$50.00',
  '$75.50',
  '$100.00',
  '$250.00',
  '$500.00',
  '$1,000.00',
  '$1,500.00',
  '$2,000.00',
  '$5,000.00',
  '$10,000.00'
];

// Sample dates
const dates = [
  'January 15, 2025',
  'February 28, 2025',
  'March 10, 2025',
  'April 22, 2025',
  'May 5, 2025',
  'June 18, 2025',
  'July 4, 2025',
  'August 30, 2025',
  'September 15, 2025',
  'October 31, 2025'
];

// Function to get a random item from an array
function getRandomItem(array) {
  return array[Math.floor(Math.random() * array.length)];
}

// Function to format a notification message with random values
function formatMessage(type) {
  let message = notificationMessages[type];
  
  if (message.includes('{eventName}')) {
    message = message.replace('{eventName}', getRandomItem(eventNames));
  }
  
  if (message.includes('{userName}')) {
    message = message.replace('{userName}', getRandomItem(userNames));
  }
  
  if (message.includes('{vendorName}')) {
    message = message.replace('{vendorName}', getRandomItem(vendorNames));
  }
  
  if (message.includes('{amount}')) {
    message = message.replace('{amount}', getRandomItem(amounts));
  }
  
  if (message.includes('{date}')) {
    message = message.replace('{date}', getRandomItem(dates));
  }
  
  return message;
}

// Main function to generate notifications
async function generateNotifications() {
  try {
    // Get all users
    const users = await prisma.user.findMany({
      select: {
        id: true,
        role: true,
      },
    });
    
    if (users.length === 0) {
      console.error('No users found in the database');
      return;
    }
    
    console.log(`Found ${users.length} users`);
    
    // Create notifications for each user
    const notificationsToCreate = [];
    
    for (const user of users) {
      // Generate between 5 and 15 notifications per user
      const numNotifications = Math.floor(Math.random() * 11) + 5;
      
      for (let i = 0; i < numNotifications; i++) {
        // Determine if notification should be read or unread (70% chance of being read)
        const isRead = Math.random() < 0.7;
        
        // Get a random notification type
        const type = getRandomItem(notificationTypes);
        
        // Format the message
        const message = formatMessage(type);
        
        // Create a random date within the last 30 days
        const createdAt = new Date();
        createdAt.setDate(createdAt.getDate() - Math.floor(Math.random() * 30));
        
        // Add notification to the array
        notificationsToCreate.push({
          userId: user.id,
          type,
          message,
          isRead,
          createdAt,
          updatedAt: new Date(),
        });
      }
    }
    
    // Bulk create notifications
    const result = await prisma.notification.createMany({
      data: notificationsToCreate,
      skipDuplicates: true,
    });
    
    console.log(`Successfully created ${result.count} test notifications`);
  } catch (error) {
    console.error('Error generating notifications:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
generateNotifications()
  .then(() => console.log('Done!'))
  .catch((error) => console.error('Script failed:', error));
