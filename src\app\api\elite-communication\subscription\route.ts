import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/prisma';
import { EliteTier, EliteSubscriptionType } from '@prisma/client';
import { ELITE_PRICING_TIERS } from '@/config/elite-pricing';

export const dynamic = 'force-dynamic';

/**
 * GET /api/elite-communication/subscription?eventId=[eventId]
 * Get user's Elite Communication subscription for an event
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const eventId = searchParams.get('eventId');

    if (!eventId) {
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 });
    }

    // Check if user has a subscription for this event
    const subscription = await db.eliteCommunication.findUnique({
      where: {
        userId_eventId: {
          userId: session.user.id,
          eventId: eventId
        }
      },
      include: {
        event: {
          select: {
            id: true,
            title: true,
            startDate: true,
            endDate: true
          }
        }
      }
    });

    return NextResponse.json({
      subscription: subscription || null,
      tier: subscription?.tier || EliteTier.BASIC,
      isActive: subscription?.isActive || false
    });

  } catch (error) {
    console.error('Error fetching Elite Communication subscription:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/elite-communication/subscription
 * Create or upgrade Elite Communication subscription
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { eventId, tier, subscriptionType, paymentIntentId } = body;

    if (!eventId || !tier) {
      return NextResponse.json(
        { error: 'Event ID and tier are required' },
        { status: 400 }
      );
    }

    // Validate tier
    if (!Object.values(EliteTier).includes(tier)) {
      return NextResponse.json({ error: 'Invalid tier' }, { status: 400 });
    }

    // Validate event exists
    const event = await db.event.findUnique({
      where: { id: eventId },
      select: { id: true, title: true, startDate: true, endDate: true }
    });

    if (!event) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 });
    }

    // Get pricing information
    const pricingTier = ELITE_PRICING_TIERS.find(t => t.tier === tier);
    if (!pricingTier) {
      return NextResponse.json({ error: 'Invalid pricing tier' }, { status: 400 });
    }

    // Calculate expiration date
    let expiresAt: Date | undefined;
    if (subscriptionType === EliteSubscriptionType.PER_EVENT) {
      // Expires 7 days after event ends
      expiresAt = new Date(event.endDate);
      expiresAt.setDate(expiresAt.getDate() + 7);
    } else if (subscriptionType === EliteSubscriptionType.MONTHLY) {
      expiresAt = new Date();
      expiresAt.setMonth(expiresAt.getMonth() + 1);
    } else if (subscriptionType === EliteSubscriptionType.ANNUAL) {
      expiresAt = new Date();
      expiresAt.setFullYear(expiresAt.getFullYear() + 1);
    }

    // Create or update subscription
    const subscription = await db.eliteCommunication.upsert({
      where: {
        userId_eventId: {
          userId: session.user.id,
          eventId: eventId
        }
      },
      update: {
        tier,
        subscriptionType,
        isActive: true,
        expiresAt,
        purchasePrice: subscriptionType === EliteSubscriptionType.PER_EVENT 
          ? pricingTier.pricePerEvent 
          : subscriptionType === EliteSubscriptionType.MONTHLY 
            ? pricingTier.monthlyPrice 
            : pricingTier.annualPrice,
        updatedAt: new Date()
      },
      create: {
        userId: session.user.id,
        eventId,
        tier,
        subscriptionType,
        isActive: true,
        expiresAt,
        purchasePrice: subscriptionType === EliteSubscriptionType.PER_EVENT 
          ? pricingTier.pricePerEvent 
          : subscriptionType === EliteSubscriptionType.MONTHLY 
            ? pricingTier.monthlyPrice 
            : pricingTier.annualPrice
      }
    });

    // Create attendee profile if it doesn't exist
    await db.attendeeProfile.upsert({
      where: {
        userId_eventId: {
          userId: session.user.id,
          eventId: eventId
        }
      },
      update: {},
      create: {
        userId: session.user.id,
        eventId,
        isDiscoverable: tier !== EliteTier.BASIC,
        privacyLevel: tier === EliteTier.BASIC ? 'PUBLIC' : 'ELITE_ONLY',
        allowMessages: tier === EliteTier.BASIC ? 'NONE' : 'ELITE_ONLY'
      }
    });

    return NextResponse.json({
      success: true,
      subscription,
      message: `Successfully upgraded to ${tier} tier`
    });

  } catch (error) {
    console.error('Error creating Elite Communication subscription:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/elite-communication/subscription
 * Cancel Elite Communication subscription
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const eventId = searchParams.get('eventId');

    if (!eventId) {
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 });
    }

    // Deactivate subscription
    await db.eliteCommunication.updateMany({
      where: {
        userId: session.user.id,
        eventId: eventId
      },
      data: {
        isActive: false,
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Subscription cancelled successfully'
    });

  } catch (error) {
    console.error('Error cancelling Elite Communication subscription:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
