'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Key, BarChart3, AlertTriangle, Code, FileCode, BookOpen, ExternalLink } from 'lucide-react';

export default function DeveloperDashboardPage() {
  return (
    <div className="container mx-auto py-4">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold mb-2">Developer Portal</h1>
          <p className="text-gray-500">Manage your API keys and access developer resources</p>
        </div>
        <Link href="/dashboard/api-keys">
          <Button className="mt-4 md:mt-0">
            <Key className="mr-2 h-4 w-4" />
            Manage API Keys
          </Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <Key className="h-4 w-4 mr-2 text-blue-500" />
              API Keys
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-sm text-gray-500">Active API keys</p>
          </CardContent>
          <CardFooter>
            <Link href="/dashboard/api-keys" className="text-sm text-blue-500 hover:underline">
              View all keys
            </Link>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <BarChart3 className="h-4 w-4 mr-2 text-green-500" />
              API Usage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,248</div>
            <p className="text-sm text-gray-500">API calls this month</p>
          </CardContent>
          <CardFooter>
            <Link href="/dashboard/api-analytics" className="text-sm text-blue-500 hover:underline">
              View analytics
            </Link>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <AlertTriangle className="h-4 w-4 mr-2 text-amber-500" />
              Alerts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-sm text-gray-500">Active alerts</p>
          </CardContent>
          <CardFooter>
            <Link href="/dashboard/api-alerts" className="text-sm text-blue-500 hover:underline">
              View alerts
            </Link>
          </CardFooter>
        </Card>
      </div>

      <Tabs defaultValue="documentation" className="mb-8">
        <TabsList>
          <TabsTrigger value="documentation">Documentation</TabsTrigger>
          <TabsTrigger value="sdks">SDKs</TabsTrigger>
          <TabsTrigger value="examples">Examples</TabsTrigger>
        </TabsList>
        <TabsContent value="documentation">
          <Card>
            <CardHeader>
              <CardTitle>API Documentation</CardTitle>
              <CardDescription>
                Comprehensive guides and reference for our API
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="bg-blue-100 p-3 rounded-lg">
                        <BookOpen className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="font-medium mb-1">Getting Started</h3>
                        <p className="text-sm text-gray-500 mb-3">
                          Learn the basics of our API and how to make your first request
                        </p>
                        <Button variant="outline" size="sm" asChild>
                          <Link href="/api/docs" target="_blank">
                            Read Guide
                            <ExternalLink className="ml-2 h-3 w-3" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="bg-purple-100 p-3 rounded-lg">
                        <FileCode className="h-6 w-6 text-purple-600" />
                      </div>
                      <div>
                        <h3 className="font-medium mb-1">API Reference</h3>
                        <p className="text-sm text-gray-500 mb-3">
                          Complete reference for all endpoints, parameters, and responses
                        </p>
                        <Button variant="outline" size="sm" asChild>
                          <Link href="/api/docs" target="_blank">
                            View Reference
                            <ExternalLink className="ml-2 h-3 w-3" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="sdks">
          <Card>
            <CardHeader>
              <CardTitle>Software Development Kits</CardTitle>
              <CardDescription>
                Official SDKs for popular programming languages
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="bg-yellow-100 p-3 rounded-lg">
                        <Code className="h-6 w-6 text-yellow-600" />
                      </div>
                      <div>
                        <h3 className="font-medium mb-1">JavaScript</h3>
                        <p className="text-sm text-gray-500 mb-3">
                          For Node.js and browser applications
                        </p>
                        <Button variant="outline" size="sm" asChild>
                          <Link href="https://github.com/example/js-sdk" target="_blank">
                            View on GitHub
                            <ExternalLink className="ml-2 h-3 w-3" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="bg-blue-100 p-3 rounded-lg">
                        <Code className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="font-medium mb-1">Python</h3>
                        <p className="text-sm text-gray-500 mb-3">
                          For Python 3.6+ applications
                        </p>
                        <Button variant="outline" size="sm" asChild>
                          <Link href="https://github.com/example/python-sdk" target="_blank">
                            View on GitHub
                            <ExternalLink className="ml-2 h-3 w-3" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="bg-red-100 p-3 rounded-lg">
                        <Code className="h-6 w-6 text-red-600" />
                      </div>
                      <div>
                        <h3 className="font-medium mb-1">Ruby</h3>
                        <p className="text-sm text-gray-500 mb-3">
                          For Ruby on Rails and other Ruby applications
                        </p>
                        <Button variant="outline" size="sm" asChild>
                          <Link href="https://github.com/example/ruby-sdk" target="_blank">
                            View on GitHub
                            <ExternalLink className="ml-2 h-3 w-3" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="examples">
          <Card>
            <CardHeader>
              <CardTitle>Code Examples</CardTitle>
              <CardDescription>
                Sample code for common API operations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Fetch Events</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <pre className="bg-gray-50 p-4 rounded-md overflow-x-auto text-sm">
                      <code>{`// JavaScript Example
const apiKey = 'your_api_key';

fetch('https://api.example.com/events', {
  headers: {
    'Authorization': \`Bearer \${apiKey}\`,
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));`}</code>
                    </pre>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Create Event</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <pre className="bg-gray-50 p-4 rounded-md overflow-x-auto text-sm">
                      <code>{`// JavaScript Example
const apiKey = 'your_api_key';
const eventData = {
  title: 'My New Event',
  description: 'Event description',
  startDate: '2024-12-31',
  location: 'New York, NY'
};

fetch('https://api.example.com/events', {
  method: 'POST',
  headers: {
    'Authorization': \`Bearer \${apiKey}\`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(eventData)
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));`}</code>
                    </pre>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
