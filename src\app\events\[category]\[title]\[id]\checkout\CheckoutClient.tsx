'use client';

import React, { useState } from 'react';
import { CheckoutContainer } from '@/components/checkout/CheckoutContainer';

interface TicketType {
  id: string;
  type: string;
  displayName?: string;
  price: number;
  description?: string;
  isAvailable: boolean;
  maxPerOrder?: number;
  totalSeats: number;
}

interface CheckoutClientProps {
  id: string; // Event ID
  availableTickets: TicketType[];
}

export default function CheckoutClient({ id, availableTickets }: CheckoutClientProps) {
  const [isCheckoutComplete, setIsCheckoutComplete] = useState(false);

  // Transform the ticket data to match the CheckoutContainer interface
  const transformedTickets = availableTickets.map(ticket => ({
    id: ticket.id,
    type: ticket.type,
    price: ticket.price,
    isAvailable: ticket.isAvailable,
    totalSeats: ticket.totalSeats,
    description: ticket.description || `${ticket.type} ticket`
  }));

  // Mock event details - in a real app, this would come from props or API
  const eventDetails = {
    id: id,
    title: 'Event Title', // This should be passed from parent
    startDate: new Date().toISOString(),
    startTime: '19:00',
    endTime: '22:00',
    venue: 'Event Venue',
    location: 'Event Location',
    imagePath: null
  };

  const handleCheckoutComplete = (orderData: any) => {
    console.log('Checkout completed:', orderData);
    setIsCheckoutComplete(true);
    
    // Here you would typically:
    // 1. Send order data to your API
    // 2. Show success message
    // 3. Redirect to confirmation page
    // 4. Send confirmation email
  };

  if (isCheckoutComplete) {
    return (
      <div className="p-8 text-center">
        <div className="max-w-md mx-auto">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Order Complete!
          </h3>
          <p className="text-gray-600 mb-6">
            Your tickets have been successfully purchased. You'll receive a confirmation email shortly.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  if (!availableTickets || availableTickets.length === 0) {
    return (
      <div className="p-8 text-center">
        <div className="max-w-md mx-auto">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            No Tickets Available
          </h3>
          <p className="text-gray-600 mb-6">
            There are currently no tickets available for this event.
          </p>
          <button
            onClick={() => window.history.back()}
            className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <CheckoutContainer
        eventDetails={eventDetails}
        availableTickets={transformedTickets}
        onCheckoutComplete={handleCheckoutComplete}
      />
    </div>
  );
}
