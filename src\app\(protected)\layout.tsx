import { Navbar } from "../../components/ui/dashboardui/navbar";
import { Toaster } from "sonner";
import { Sidebar } from "../../components/ui/dashboardui/sidebar";
import { redirect } from "next/navigation";
import { getSession } from "@/auth";
import { AuthSessionProvider } from "@/components/providers/session-provider";
import { UserSync } from "@/components/auth/user-sync";
import { RoleCookieSync } from "@/components/auth/role-cookie-sync";
import { SubscriptionProvider } from "@/contexts/subscription-context";

export default async function ProtectedLayout({ children }: { children: React.ReactNode }) {
  // Get the session using our custom getSession function
  const session = await getSession();

  // If there's no session, redirect to login
  if (!session) {
    redirect('/auth/login');
  }

  return (
    <AuthSessionProvider session={session}>
      <SubscriptionProvider>
        <div className="min-h-screen bg-gray-50">
          {/* User synchronization components */}
          <UserSync />
          <RoleCookieSync />

          {/* Navigation progress bar is now handled at the root layout level */}
          <Navbar />
          <div className="flex h-[calc(100vh-4rem)]">
            <div className="hidden md:block w-64 flex-shrink-0">
              <Sidebar />
            </div>
            <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
              {children}
            </main>
          </div>
          <Toaster richColors closeButton />
        </div>
      </SubscriptionProvider>
    </AuthSessionProvider>
  );
}
