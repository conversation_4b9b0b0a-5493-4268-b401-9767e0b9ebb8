-- CreateEnum
CREATE TYPE "PartnerType" AS ENUM ('HOTEL', 'RESTAURANT', 'BAR', 'NIGHTCLUB');

-- Create<PERSON>num
CREATE TYPE "PartnershipTier" AS ENUM ('BASIC', 'PREMIUM', 'ELITE');

-- CreateTable
CREATE TABLE "Partner" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "businessName" TEXT NOT NULL,
    "partnerType" "PartnerType" NOT NULL,
    "tier" "PartnershipTier" NOT NULL DEFAULT 'BASIC',
    "description" TEXT,
    "address" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "province" TEXT NOT NULL,
    "postalCode" TEXT,
    "country" TEXT NOT NULL DEFAULT 'Zambia',
    "latitude" DOUBLE PRECISION,
    "longitude" DOUBLE PRECISION,
    "contactName" TEXT NOT NULL,
    "contactEmail" TEXT NOT NULL,
    "contactPhone" TEXT NOT NULL,
    "website" TEXT,
    "logo" TEXT,
    "bannerImage" TEXT,
    "galleryImages" JSONB,
    "socialLinks" JSONB,
    "businessHours" JSONB,
    "amenities" TEXT[],
    "priceRange" TEXT,
    "rating" DOUBLE PRECISION,
    "totalReviews" INTEGER NOT NULL DEFAULT 0,
    "isVerified" BOOLEAN NOT NULL DEFAULT false,
    "verifiedAt" TIMESTAMP(3),
    "featured" BOOLEAN NOT NULL DEFAULT false,
    "acceptsNfcPayments" BOOLEAN NOT NULL DEFAULT false,
    "nfcTerminalId" TEXT,
    "commissionRate" DOUBLE PRECISION NOT NULL DEFAULT 5.0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Partner_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PartnerPromotion" (
    "id" TEXT NOT NULL,
    "partnerId" TEXT NOT NULL,
    "eventId" TEXT,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "discountValue" DOUBLE PRECISION,
    "discountType" TEXT,
    "promoCode" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "maxUses" INTEGER,
    "currentUses" INTEGER NOT NULL DEFAULT 0,
    "imageUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PartnerPromotion_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PartnerReview" (
    "id" TEXT NOT NULL,
    "partnerId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "rating" DOUBLE PRECISION NOT NULL,
    "comment" TEXT,
    "isPublished" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PartnerReview_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PartnerNFCTransaction" (
    "id" TEXT NOT NULL,
    "partnerId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "cardId" TEXT,
    "deviceId" TEXT,
    "amount" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'ZMW',
    "status" "TransactionStatus" NOT NULL DEFAULT 'PENDING',
    "reference" TEXT,
    "receiptUrl" TEXT,
    "notes" TEXT,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "processedAt" TIMESTAMP(3),
    "loyaltyPoints" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "PartnerNFCTransaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PartnerNFCTransactionItem" (
    "id" TEXT NOT NULL,
    "transactionId" TEXT NOT NULL,
    "menuItemId" TEXT,
    "name" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "unitPrice" DOUBLE PRECISION NOT NULL,
    "totalPrice" DOUBLE PRECISION NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PartnerNFCTransactionItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MenuItem" (
    "id" TEXT NOT NULL,
    "partnerId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "category" TEXT NOT NULL,
    "price" DOUBLE PRECISION NOT NULL,
    "imageUrl" TEXT,
    "isAvailable" BOOLEAN NOT NULL DEFAULT true,
    "allergens" TEXT[],
    "nutritionalInfo" JSONB,
    "preparationTime" INTEGER,
    "isPopular" BOOLEAN NOT NULL DEFAULT false,
    "isVegetarian" BOOLEAN NOT NULL DEFAULT false,
    "isVegan" BOOLEAN NOT NULL DEFAULT false,
    "isGlutenFree" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MenuItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LoyaltyProgram" (
    "id" TEXT NOT NULL,
    "partnerId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "pointsPerCurrency" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "pointsExpiration" INTEGER,
    "tiers" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LoyaltyProgram_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LoyaltyProgramMember" (
    "id" TEXT NOT NULL,
    "programId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "points" INTEGER NOT NULL DEFAULT 0,
    "tier" TEXT NOT NULL DEFAULT 'STANDARD',
    "joinedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "lastActivity" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LoyaltyProgramMember_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EventPartner" (
    "id" TEXT NOT NULL,
    "eventId" TEXT NOT NULL,
    "partnerId" TEXT NOT NULL,
    "partnerType" TEXT NOT NULL,
    "specialOffer" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EventPartner_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Partner_userId_key" ON "Partner"("userId");

-- CreateIndex
CREATE INDEX "Partner_partnerType_idx" ON "Partner"("partnerType");

-- CreateIndex
CREATE INDEX "Partner_tier_idx" ON "Partner"("tier");

-- CreateIndex
CREATE INDEX "Partner_city_idx" ON "Partner"("city");

-- CreateIndex
CREATE INDEX "Partner_featured_idx" ON "Partner"("featured");

-- CreateIndex
CREATE INDEX "Partner_isVerified_idx" ON "Partner"("isVerified");

-- CreateIndex
CREATE INDEX "Partner_acceptsNfcPayments_idx" ON "Partner"("acceptsNfcPayments");

-- CreateIndex
CREATE INDEX "PartnerPromotion_partnerId_idx" ON "PartnerPromotion"("partnerId");

-- CreateIndex
CREATE INDEX "PartnerPromotion_eventId_idx" ON "PartnerPromotion"("eventId");

-- CreateIndex
CREATE INDEX "PartnerPromotion_isActive_idx" ON "PartnerPromotion"("isActive");

-- CreateIndex
CREATE INDEX "PartnerPromotion_startDate_idx" ON "PartnerPromotion"("startDate");

-- CreateIndex
CREATE INDEX "PartnerPromotion_endDate_idx" ON "PartnerPromotion"("endDate");

-- CreateIndex
CREATE INDEX "PartnerReview_partnerId_idx" ON "PartnerReview"("partnerId");

-- CreateIndex
CREATE INDEX "PartnerReview_userId_idx" ON "PartnerReview"("userId");

-- CreateIndex
CREATE INDEX "PartnerReview_rating_idx" ON "PartnerReview"("rating");

-- CreateIndex
CREATE INDEX "PartnerNFCTransaction_partnerId_idx" ON "PartnerNFCTransaction"("partnerId");

-- CreateIndex
CREATE INDEX "PartnerNFCTransaction_userId_idx" ON "PartnerNFCTransaction"("userId");

-- CreateIndex
CREATE INDEX "PartnerNFCTransaction_cardId_idx" ON "PartnerNFCTransaction"("cardId");

-- CreateIndex
CREATE INDEX "PartnerNFCTransaction_deviceId_idx" ON "PartnerNFCTransaction"("deviceId");

-- CreateIndex
CREATE INDEX "PartnerNFCTransaction_status_idx" ON "PartnerNFCTransaction"("status");

-- CreateIndex
CREATE INDEX "PartnerNFCTransaction_createdAt_idx" ON "PartnerNFCTransaction"("createdAt");

-- CreateIndex
CREATE INDEX "PartnerNFCTransactionItem_transactionId_idx" ON "PartnerNFCTransactionItem"("transactionId");

-- CreateIndex
CREATE INDEX "PartnerNFCTransactionItem_menuItemId_idx" ON "PartnerNFCTransactionItem"("menuItemId");

-- CreateIndex
CREATE INDEX "MenuItem_partnerId_idx" ON "MenuItem"("partnerId");

-- CreateIndex
CREATE INDEX "MenuItem_category_idx" ON "MenuItem"("category");

-- CreateIndex
CREATE INDEX "MenuItem_isAvailable_idx" ON "MenuItem"("isAvailable");

-- CreateIndex
CREATE INDEX "MenuItem_isPopular_idx" ON "MenuItem"("isPopular");

-- CreateIndex
CREATE UNIQUE INDEX "LoyaltyProgram_partnerId_key" ON "LoyaltyProgram"("partnerId");

-- CreateIndex
CREATE INDEX "LoyaltyProgram_partnerId_idx" ON "LoyaltyProgram"("partnerId");

-- CreateIndex
CREATE UNIQUE INDEX "LoyaltyProgramMember_programId_userId_key" ON "LoyaltyProgramMember"("programId", "userId");

-- CreateIndex
CREATE INDEX "LoyaltyProgramMember_programId_idx" ON "LoyaltyProgramMember"("programId");

-- CreateIndex
CREATE INDEX "LoyaltyProgramMember_userId_idx" ON "LoyaltyProgramMember"("userId");

-- CreateIndex
CREATE INDEX "LoyaltyProgramMember_tier_idx" ON "LoyaltyProgramMember"("tier");

-- CreateIndex
CREATE UNIQUE INDEX "EventPartner_eventId_partnerId_key" ON "EventPartner"("eventId", "partnerId");

-- CreateIndex
CREATE INDEX "EventPartner_eventId_idx" ON "EventPartner"("eventId");

-- CreateIndex
CREATE INDEX "EventPartner_partnerId_idx" ON "EventPartner"("partnerId");

-- CreateIndex
CREATE INDEX "EventPartner_partnerType_idx" ON "EventPartner"("partnerType");

-- AddForeignKey
ALTER TABLE "Partner" ADD CONSTRAINT "Partner_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PartnerPromotion" ADD CONSTRAINT "PartnerPromotion_partnerId_fkey" FOREIGN KEY ("partnerId") REFERENCES "Partner"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PartnerPromotion" ADD CONSTRAINT "PartnerPromotion_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "Event"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PartnerReview" ADD CONSTRAINT "PartnerReview_partnerId_fkey" FOREIGN KEY ("partnerId") REFERENCES "Partner"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PartnerReview" ADD CONSTRAINT "PartnerReview_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PartnerNFCTransaction" ADD CONSTRAINT "PartnerNFCTransaction_partnerId_fkey" FOREIGN KEY ("partnerId") REFERENCES "Partner"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PartnerNFCTransaction" ADD CONSTRAINT "PartnerNFCTransaction_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PartnerNFCTransactionItem" ADD CONSTRAINT "PartnerNFCTransactionItem_transactionId_fkey" FOREIGN KEY ("transactionId") REFERENCES "PartnerNFCTransaction"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PartnerNFCTransactionItem" ADD CONSTRAINT "PartnerNFCTransactionItem_menuItemId_fkey" FOREIGN KEY ("menuItemId") REFERENCES "MenuItem"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MenuItem" ADD CONSTRAINT "MenuItem_partnerId_fkey" FOREIGN KEY ("partnerId") REFERENCES "Partner"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LoyaltyProgram" ADD CONSTRAINT "LoyaltyProgram_partnerId_fkey" FOREIGN KEY ("partnerId") REFERENCES "Partner"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LoyaltyProgramMember" ADD CONSTRAINT "LoyaltyProgramMember_programId_fkey" FOREIGN KEY ("programId") REFERENCES "LoyaltyProgram"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LoyaltyProgramMember" ADD CONSTRAINT "LoyaltyProgramMember_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EventPartner" ADD CONSTRAINT "EventPartner_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "Event"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EventPartner" ADD CONSTRAINT "EventPartner_partnerId_fkey" FOREIGN KEY ("partnerId") REFERENCES "Partner"("id") ON DELETE CASCADE ON UPDATE CASCADE;
