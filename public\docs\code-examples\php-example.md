# PHP API Examples

This guide provides examples of how to use the QuickTime Events API with PHP.

## Prerequisites

- PHP 7.4 or higher
- `curl` extension enabled
- Composer (recommended for using Guzzle)

## Authentication

```php
<?php
// Using native PHP cURL
$apiKey = 'your-api-key';
$baseUrl = 'https://your-domain.com/api';

// Set up common headers
$headers = [
    'X-API-Key: ' . $apiKey,
    'Content-Type: application/json',
    'Accept: application/json'
];
```

## Get Published Events

```php
<?php
// Using native PHP cURL
function getPublishedEvents($apiKey, $baseUrl) {
    $url = $baseUrl . '/events/published';
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'X-API-Key: ' . $apiKey,
        'Accept: application/json'
    ]);
    
    $response = curl_exec($ch);
    $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($statusCode === 200) {
        return json_decode($response, true);
    } else {
        echo "Error: " . $statusCode . PHP_EOL;
        echo $response . PHP_EOL;
        return null;
    }
}

// Usage
$apiKey = 'your-api-key';
$baseUrl = 'https://your-domain.com/api';

$events = getPublishedEvents($apiKey, $baseUrl);

if ($events) {
    echo "Found " . count($events) . " events" . PHP_EOL;
    foreach ($events as $event) {
        echo "Event: " . $event['title'] . PHP_EOL;
        echo "Date: " . $event['startDate'] . PHP_EOL;
        echo "Location: " . $event['location'] . PHP_EOL;
        echo "---" . PHP_EOL;
    }
}
```

## Using Guzzle (Recommended)

```php
<?php
// Using Guzzle HTTP client (recommended)
// Install with: composer require guzzlehttp/guzzle

require 'vendor/autoload.php';

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

$apiKey = 'your-api-key';
$baseUrl = 'https://your-domain.com/api';

$client = new Client([
    'base_uri' => $baseUrl,
    'headers' => [
        'X-API-Key' => $apiKey,
        'Content-Type' => 'application/json',
        'Accept' => 'application/json'
    ]
]);

try {
    $response = $client->get('/events/published');
    $events = json_decode($response->getBody(), true);
    
    echo "Found " . count($events) . " events" . PHP_EOL;
    foreach ($events as $event) {
        echo "Event: " . $event['title'] . PHP_EOL;
        echo "Date: " . $event['startDate'] . PHP_EOL;
        echo "Location: " . $event['location'] . PHP_EOL;
        echo "---" . PHP_EOL;
    }
} catch (RequestException $e) {
    echo "Error: " . $e->getMessage() . PHP_EOL;
}
```

## Create an Event

```php
<?php
// Using Guzzle HTTP client
require 'vendor/autoload.php';

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

function createEvent($apiKey, $baseUrl, $eventData) {
    $client = new Client([
        'base_uri' => $baseUrl,
        'headers' => [
            'X-API-Key' => $apiKey,
            'Content-Type' => 'application/json',
            'Accept' => 'application/json'
        ]
    ]);
    
    try {
        $response = $client->post('/events/create', [
            'json' => $eventData
        ]);
        
        return json_decode($response->getBody(), true);
    } catch (RequestException $e) {
        echo "Error: " . $e->getMessage() . PHP_EOL;
        if ($e->hasResponse()) {
            echo $e->getResponse()->getBody() . PHP_EOL;
        }
        return null;
    }
}

// Usage
$apiKey = 'your-api-key';
$baseUrl = 'https://your-domain.com/api';

// Create an event starting tomorrow
$tomorrow = date('c', strtotime('+1 day'));
$dayAfter = date('c', strtotime('+2 days'));

$eventData = [
    'title' => 'PHP Meetup',
    'description' => 'A meetup for PHP enthusiasts',
    'startDate' => $tomorrow,
    'endDate' => $dayAfter,
    'location' => 'San Francisco',
    'venue' => 'Tech Hub',
    'category' => 'TECHNOLOGY',
    'eventType' => 'WORKSHOP'
];

$newEvent = createEvent($apiKey, $baseUrl, $eventData);
if ($newEvent) {
    echo "Event created with ID: " . $newEvent['id'] . PHP_EOL;
}
```

## Get Event Details

```php
<?php
// Using Guzzle HTTP client
require 'vendor/autoload.php';

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

function getEventDetails($apiKey, $baseUrl, $eventId) {
    $client = new Client([
        'base_uri' => $baseUrl,
        'headers' => [
            'X-API-Key' => $apiKey,
            'Accept' => 'application/json'
        ]
    ]);
    
    try {
        $response = $client->get('/eventdetails/' . $eventId);
        return json_decode($response->getBody(), true);
    } catch (RequestException $e) {
        echo "Error: " . $e->getMessage() . PHP_EOL;
        if ($e->hasResponse()) {
            echo $e->getResponse()->getBody() . PHP_EOL;
        }
        return null;
    }
}

// Usage
$apiKey = 'your-api-key';
$baseUrl = 'https://your-domain.com/api';
$eventId = 'your-event-id';

$event = getEventDetails($apiKey, $baseUrl, $eventId);
if ($event) {
    echo "Event: " . $event['title'] . PHP_EOL;
    echo "Description: " . $event['description'] . PHP_EOL;
    echo "Date: " . $event['startDate'] . " to " . $event['endDate'] . PHP_EOL;
    echo "Location: " . $event['location'] . ", " . $event['venue'] . PHP_EOL;
    echo "Category: " . $event['category'] . PHP_EOL;
    echo "Type: " . $event['eventType'] . PHP_EOL;
}
```

## Handling Rate Limits

```php
<?php
// Using Guzzle HTTP client
require 'vendor/autoload.php';

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

function makeRequestWithRateLimitHandling($apiKey, $baseUrl, $endpoint, $method = 'GET', $data = null) {
    $client = new Client([
        'base_uri' => $baseUrl,
        'headers' => [
            'X-API-Key' => $apiKey,
            'Content-Type' => 'application/json',
            'Accept' => 'application/json'
        ]
    ]);
    
    $maxRetries = 5;
    $retries = 0;
    
    while ($retries < $maxRetries) {
        try {
            $options = [];
            if ($data && ($method === 'POST' || $method === 'PUT')) {
                $options['json'] = $data;
            }
            
            $response = $client->request($method, $endpoint, $options);
            
            // Check for rate limit headers
            $headers = $response->getHeaders();
            if (isset($headers['X-RateLimit-Limit'][0]) && isset($headers['X-RateLimit-Remaining'][0])) {
                $limit = $headers['X-RateLimit-Limit'][0];
                $remaining = $headers['X-RateLimit-Remaining'][0];
                echo "Rate limit: {$remaining}/{$limit} requests remaining" . PHP_EOL;
            }
            
            return [
                'success' => true,
                'status' => $response->getStatusCode(),
                'data' => json_decode($response->getBody(), true),
                'headers' => $headers
            ];
        } catch (RequestException $e) {
            // If rate limited (429 Too Many Requests)
            if ($e->hasResponse() && $e->getResponse()->getStatusCode() === 429) {
                $retries++;
                
                // Get retry delay from headers or default to 60 seconds
                $retryAfter = 60;
                if ($e->getResponse()->hasHeader('Retry-After')) {
                    $retryAfter = (int) $e->getResponse()->getHeader('Retry-After')[0];
                }
                
                echo "Rate limit exceeded. Retrying in {$retryAfter} seconds..." . PHP_EOL;
                sleep($retryAfter);
                continue;
            }
            
            // For other errors, retry with exponential backoff
            $retries++;
            if ($retries === $maxRetries) {
                return [
                    'success' => false,
                    'error' => $e->getMessage(),
                    'status' => $e->hasResponse() ? $e->getResponse()->getStatusCode() : null,
                    'data' => $e->hasResponse() ? json_decode($e->getResponse()->getBody(), true) : null
                ];
            }
            
            $waitTime = pow(2, $retries);
            echo "Request failed. Retrying in {$waitTime} seconds..." . PHP_EOL;
            sleep($waitTime);
        }
    }
    
    return [
        'success' => false,
        'error' => 'Max retries exceeded'
    ];
}

// Usage
$apiKey = 'your-api-key';
$baseUrl = 'https://your-domain.com/api';

$result = makeRequestWithRateLimitHandling($apiKey, $baseUrl, '/events/published');
if ($result['success']) {
    $events = $result['data'];
    echo "Found " . count($events) . " events" . PHP_EOL;
} else {
    echo "Error: " . $result['error'] . PHP_EOL;
}
```

## Complete Example: Event Manager Class

```php
<?php
// EventAPIClient.php
require 'vendor/autoload.php';

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class EventAPIClient {
    private $apiKey;
    private $baseUrl;
    private $client;
    
    public function __construct($apiKey, $baseUrl = 'https://your-domain.com/api') {
        $this->apiKey = $apiKey;
        $this->baseUrl = $baseUrl;
        
        $this->client = new Client([
            'base_uri' => $baseUrl,
            'headers' => [
                'X-API-Key' => $apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ]
        ]);
    }
    
    public function makeRequest($endpoint, $method = 'GET', $data = null, $params = null) {
        $maxRetries = 5;
        $retries = 0;
        
        while ($retries < $maxRetries) {
            try {
                $options = [];
                
                if ($params) {
                    $options['query'] = $params;
                }
                
                if ($data && ($method === 'POST' || $method === 'PUT')) {
                    $options['json'] = $data;
                }
                
                $response = $this->client->request($method, $endpoint, $options);
                
                // Check for rate limit headers
                $headers = $response->getHeaders();
                if (isset($headers['X-RateLimit-Limit'][0]) && isset($headers['X-RateLimit-Remaining'][0])) {
                    $limit = $headers['X-RateLimit-Limit'][0];
                    $remaining = $headers['X-RateLimit-Remaining'][0];
                    echo "Rate limit: {$remaining}/{$limit} requests remaining" . PHP_EOL;
                }
                
                return [
                    'success' => true,
                    'status' => $response->getStatusCode(),
                    'data' => json_decode($response->getBody(), true),
                    'headers' => $headers
                ];
            } catch (RequestException $e) {
                // If rate limited (429 Too Many Requests)
                if ($e->hasResponse() && $e->getResponse()->getStatusCode() === 429) {
                    $retries++;
                    
                    // Get retry delay from headers or default to 60 seconds
                    $retryAfter = 60;
                    if ($e->getResponse()->hasHeader('Retry-After')) {
                        $retryAfter = (int) $e->getResponse()->getHeader('Retry-After')[0];
                    }
                    
                    echo "Rate limit exceeded. Retrying in {$retryAfter} seconds..." . PHP_EOL;
                    sleep($retryAfter);
                    continue;
                }
                
                // For other errors, retry with exponential backoff
                $retries++;
                if ($retries === $maxRetries) {
                    return [
                        'success' => false,
                        'error' => $e->getMessage(),
                        'status' => $e->hasResponse() ? $e->getResponse()->getStatusCode() : null,
                        'data' => $e->hasResponse() ? json_decode($e->getResponse()->getBody(), true) : null
                    ];
                }
                
                $waitTime = pow(2, $retries);
                echo "Request failed. Retrying in {$waitTime} seconds..." . PHP_EOL;
                sleep($waitTime);
            }
        }
        
        return [
            'success' => false,
            'error' => 'Max retries exceeded'
        ];
    }
    
    public function getPublishedEvents($page = 1, $limit = 10) {
        $result = $this->makeRequest('/events/published', 'GET', null, [
            'page' => $page,
            'limit' => $limit
        ]);
        
        return $result['success'] ? $result['data'] : null;
    }
    
    public function getEventDetails($eventId) {
        $result = $this->makeRequest("/eventdetails/{$eventId}");
        return $result['success'] ? $result['data'] : null;
    }
    
    public function createEvent($eventData) {
        $result = $this->makeRequest('/events/create', 'POST', $eventData);
        return $result['success'] ? $result['data'] : null;
    }
    
    public function updateEvent($eventId, $eventData) {
        $result = $this->makeRequest("/events/{$eventId}", 'PUT', $eventData);
        return $result['success'] ? $result['data'] : null;
    }
    
    public function deleteEvent($eventId) {
        $result = $this->makeRequest("/events/{$eventId}", 'DELETE');
        return $result['success'];
    }
}

// Example usage
$client = new EventAPIClient('your-api-key');

// Get published events
$events = $client->getPublishedEvents();
if ($events) {
    echo "Found " . count($events) . " events" . PHP_EOL;
    
    // Show first 3 events
    for ($i = 0; $i < min(3, count($events)); $i++) {
        echo "- " . $events[$i]['title'] . " (" . $events[$i]['startDate'] . ")" . PHP_EOL;
    }
}

// Create a new event
$tomorrow = date('c', strtotime('+1 day'));
$dayAfter = date('c', strtotime('+2 days'));

$newEventData = [
    'title' => 'PHP API Workshop',
    'description' => 'Learn how to use APIs with PHP',
    'startDate' => $tomorrow,
    'endDate' => $dayAfter,
    'location' => 'Online',
    'venue' => 'Zoom',
    'category' => 'TECHNOLOGY',
    'eventType' => 'WORKSHOP'
];

$newEvent = $client->createEvent($newEventData);
if ($newEvent) {
    echo "Created new event: " . $newEvent['title'] . " (ID: " . $newEvent['id'] . ")" . PHP_EOL;
    
    // Get the event details
    $eventDetails = $client->getEventDetails($newEvent['id']);
    if ($eventDetails) {
        echo "Event details: " . json_encode($eventDetails, JSON_PRETTY_PRINT) . PHP_EOL;
    }
    
    // Update the event
    $updateData = [
        'title' => 'Updated: PHP API Workshop',
        'description' => 'Updated description: Learn how to use APIs with PHP'
    ];
    $updatedEvent = $client->updateEvent($newEvent['id'], $updateData);
    if ($updatedEvent) {
        echo "Updated event title to: " . $updatedEvent['title'] . PHP_EOL;
    }
    
    // Delete the event
    if ($client->deleteEvent($newEvent['id'])) {
        echo "Deleted event with ID: " . $newEvent['id'] . PHP_EOL;
    }
}
```
