import { notFound } from 'next/navigation';
import { db } from '@/lib/prisma';
import CheckoutClient from './CheckoutClient';

interface PageProps {
  params: Promise<{
    category: string;
    title: string;
    id: string;
  }>;
}

export default async function CheckoutPage({ params }: PageProps) {
  const resolvedParams = await params;
  const { id } = resolvedParams;

  try {
    // Fetch event and ticket data
    const event = await db.event.findUnique({
      where: { 
        id,
        status: 'Published'
      },
      include: {
        tickets: {
          where: {
            isAvailable: true
          }
        }
      }
    });

    if (!event) {
      notFound();
    }

    return (
      <CheckoutClient
        id={event.id}
        availableTickets={event.tickets}
      />
    );

  } catch (error) {
    console.error('Error fetching checkout data:', error);
    notFound();
  }
}
