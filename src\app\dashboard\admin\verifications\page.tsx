'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useCurrentRole } from '@/hooks/use-current-role';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Eye, 
  FileText, 
  Building, 
  Mail, 
  Phone,
  MapPin,
  Calendar,
  RefreshCw
} from 'lucide-react';

interface Verification {
  id: string;
  userId: string;
  businessName: string;
  businessType: string;
  registrationNumber: string;
  taxPayerIdNumber: string;
  phoneNumber: string;
  alternativeEmail: string | null;
  website: string | null;
  physicalAddress: string;
  city: string;
  province: string;
  postalCode: string | null;
  idDocumentPath: string;
  idDocumentType: string;
  businessLicensePath: string;
  taxCertificatePath: string | null;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  createdAt: string;
  updatedAt: string;
  rejectionReason: string | null;
  verifiedAt: string | null;
  user: {
    name: string | null;
    email: string | null;
  };
}

// Mock data for demonstration
const mockVerifications: Verification[] = [
  {
    id: '1',
    userId: 'user1',
    businessName: 'Acme Events',
    businessType: 'Company',
    registrationNumber: 'REG123456',
    taxPayerIdNumber: 'TPIN987654',
    phoneNumber: '+260 97 1234567',
    alternativeEmail: '<EMAIL>',
    website: 'https://acmeevents.com',
    physicalAddress: '123 Main Street',
    city: 'Lusaka',
    province: 'Lusaka',
    postalCode: '10101',
    idDocumentPath: '/uploads/verification/id-sample.jpg',
    idDocumentType: 'national_id',
    businessLicensePath: '/uploads/verification/license-sample.jpg',
    taxCertificatePath: '/uploads/verification/tax-sample.jpg',
    status: 'PENDING',
    createdAt: '2023-06-15T10:30:00Z',
    updatedAt: '2023-06-15T10:30:00Z',
    rejectionReason: null,
    verifiedAt: null,
    user: {
      name: 'John Doe',
      email: '<EMAIL>',
    }
  },
  {
    id: '2',
    userId: 'user2',
    businessName: 'Elite Conferences',
    businessType: 'Company',
    registrationNumber: 'REG789012',
    taxPayerIdNumber: 'TPIN456789',
    phoneNumber: '+260 97 7654321',
    alternativeEmail: '<EMAIL>',
    website: 'https://eliteconferences.com',
    physicalAddress: '456 Business Ave',
    city: 'Ndola',
    province: 'Copperbelt',
    postalCode: '20101',
    idDocumentPath: '/uploads/verification/id-sample2.jpg',
    idDocumentType: 'passport',
    businessLicensePath: '/uploads/verification/license-sample2.jpg',
    taxCertificatePath: '/uploads/verification/tax-sample2.jpg',
    status: 'APPROVED',
    createdAt: '2023-06-10T14:20:00Z',
    updatedAt: '2023-06-12T09:15:00Z',
    rejectionReason: null,
    verifiedAt: '2023-06-12T09:15:00Z',
    user: {
      name: 'Jane Smith',
      email: '<EMAIL>',
    }
  }
];

export default function DashboardAdminVerificationsPage() {
  const router = useRouter();
  const role = useCurrentRole();
  const [activeTab, setActiveTab] = useState('pending');
  const [verifications, setVerifications] = useState<Verification[]>(mockVerifications);
  const [loading, setLoading] = useState(false);

  // Check if user has admin access
  if (role !== 'ADMIN' && role !== 'SUPERADMIN') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">You need admin privileges to view this page.</p>
          <Button onClick={() => router.push('/dashboard')}>Go to Dashboard</Button>
        </div>
      </div>
    );
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'APPROVED':
        return <Badge variant="outline" className="text-green-600 border-green-600"><CheckCircle className="w-3 h-3 mr-1" />Approved</Badge>;
      case 'REJECTED':
        return <Badge variant="outline" className="text-red-600 border-red-600"><XCircle className="w-3 h-3 mr-1" />Rejected</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const filteredVerifications = verifications.filter(verification => {
    switch (activeTab) {
      case 'pending':
        return verification.status === 'PENDING';
      case 'approved':
        return verification.status === 'APPROVED';
      case 'rejected':
        return verification.status === 'REJECTED';
      default:
        return true;
    }
  });

  const handleViewDetails = (verificationId: string) => {
    router.push(`/admin/verifications/${verificationId}`);
  };

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Verification Management</h1>
          <p className="text-gray-600 mt-1">Review and manage organizer verification requests</p>
        </div>
        <Button 
          onClick={() => setLoading(true)} 
          disabled={loading}
          variant="outline"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="pending">
            Pending ({verifications.filter(v => v.status === 'PENDING').length})
          </TabsTrigger>
          <TabsTrigger value="approved">
            Approved ({verifications.filter(v => v.status === 'APPROVED').length})
          </TabsTrigger>
          <TabsTrigger value="rejected">
            Rejected ({verifications.filter(v => v.status === 'REJECTED').length})
          </TabsTrigger>
          <TabsTrigger value="all">
            All ({verifications.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-6">
          <div className="grid gap-4">
            {filteredVerifications.length === 0 ? (
              <Card>
                <CardContent className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No verifications found</h3>
                    <p className="text-gray-500">No verification requests match the current filter.</p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              filteredVerifications.map((verification) => (
                <Card key={verification.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          <Building className="w-5 h-5" />
                          {verification.businessName}
                        </CardTitle>
                        <CardDescription className="mt-1">
                          {verification.businessType} • Submitted {new Date(verification.createdAt).toLocaleDateString()}
                        </CardDescription>
                      </div>
                      {getStatusBadge(verification.status)}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                      <div className="flex items-center gap-2">
                        <Mail className="w-4 h-4 text-gray-500" />
                        <span className="text-sm">{verification.user.email}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="w-4 h-4 text-gray-500" />
                        <span className="text-sm">{verification.phoneNumber}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-gray-500" />
                        <span className="text-sm">{verification.city}, {verification.province}</span>
                      </div>
                    </div>
                    <div className="flex justify-end">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleViewDetails(verification.id)}
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        View Details
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
