/**
 * API Key Testing Script
 * 
 * This script tests API keys with different rate limits and permissions.
 * It makes multiple requests to different endpoints to verify that:
 * 1. Rate limiting is working correctly
 * 2. Permission checks are enforced
 * 3. Usage analytics are being recorded
 * 
 * Usage:
 * node test-api-keys.js <api-key> <endpoint>
 * 
 * Examples:
 * node test-api-keys.js your-api-key http://localhost:3000/api/test
 * node test-api-keys.js your-api-key http://localhost:3000/api/external/events
 */

const fetch = require('node-fetch');

// Get command line arguments
const apiKey = process.argv[2];
const endpoint = process.argv[3] || 'http://localhost:3000/api/test';
const requestCount = parseInt(process.argv[4]) || 20;
const delayMs = parseInt(process.argv[5]) || 100;

if (!apiKey) {
  console.error('Please provide an API key as the first argument');
  process.exit(1);
}

console.log(`Testing API key: ${apiKey}`);
console.log(`Endpoint: ${endpoint}`);
console.log(`Making ${requestCount} requests with ${delayMs}ms delay between requests`);
console.log('---------------------------------------------------');

// Function to make a request with the API key
async function makeRequest(index) {
  try {
    const startTime = Date.now();
    const response = await fetch(endpoint, {
      headers: {
        'X-API-Key': apiKey
      }
    });
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    // Get rate limit headers
    const rateLimit = response.headers.get('X-RateLimit-Limit');
    const rateLimitRemaining = response.headers.get('X-RateLimit-Remaining');
    const rateLimitReset = response.headers.get('X-RateLimit-Reset');
    
    // Format the reset time
    const resetTime = rateLimitReset ? new Date(parseInt(rateLimitReset) * 1000).toLocaleTimeString() : 'N/A';
    
    // Get response data
    const data = await response.text();
    const status = response.status;
    
    console.log(`Request ${index + 1}/${requestCount} - Status: ${status} - Time: ${responseTime}ms`);
    
    if (rateLimit) {
      console.log(`  Rate Limit: ${rateLimit} - Remaining: ${rateLimitRemaining} - Reset: ${resetTime}`);
    }
    
    if (status !== 200) {
      console.log(`  Error: ${data}`);
    }
    
    return { success: status === 200, status, data };
  } catch (error) {
    console.error(`  Error making request: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Function to delay execution
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Main function to run the tests
async function runTests() {
  console.log('Starting tests...');
  
  let successCount = 0;
  let failureCount = 0;
  let rateLimitedCount = 0;
  
  for (let i = 0; i < requestCount; i++) {
    const result = await makeRequest(i);
    
    if (result.success) {
      successCount++;
    } else {
      failureCount++;
      if (result.status === 429) {
        rateLimitedCount++;
      }
    }
    
    // Add delay between requests
    if (i < requestCount - 1) {
      await delay(delayMs);
    }
  }
  
  console.log('---------------------------------------------------');
  console.log('Test Results:');
  console.log(`  Total Requests: ${requestCount}`);
  console.log(`  Successful: ${successCount}`);
  console.log(`  Failed: ${failureCount}`);
  console.log(`  Rate Limited: ${rateLimitedCount}`);
  console.log('---------------------------------------------------');
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
});
