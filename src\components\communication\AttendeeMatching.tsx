'use client';

import React, { useState, useEffect } from 'react';
import { 
  Users, 
  Heart, 
  MessageCircle, 
  Calendar,
  TrendingUp,
  Sparkles,
  RefreshCw,
  Settings,
  Star,
  Target,
  Lightbulb,
  ArrowRight,
  Crown
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { EliteTier } from '@prisma/client';
import { AttendeeMatch, NetworkingSuggestion } from '@/lib/attendee-matching';
import { toast } from 'sonner';

interface AttendeeMatchingProps {
  eventId: string;
  currentUser: {
    id: string;
    name?: string;
    image?: string;
  };
  userTier: EliteTier;
  onStartDirectMessage?: (recipientId: string) => void;
  onScheduleMeeting?: (recipientId: string) => void;
}

interface MatchingCriteria {
  industryWeight: number;
  interestsWeight: number;
  roleWeight: number;
  companyWeight: number;
  networkingGoalsWeight: number;
  geographicWeight: number;
  tierWeight: number;
}

const DEFAULT_CRITERIA: MatchingCriteria = {
  industryWeight: 25,
  interestsWeight: 30,
  roleWeight: 20,
  companyWeight: 10,
  networkingGoalsWeight: 10,
  geographicWeight: 3,
  tierWeight: 2
};

export default function AttendeeMatching({
  eventId,
  currentUser,
  userTier,
  onStartDirectMessage,
  onScheduleMeeting
}: AttendeeMatchingProps) {
  const [matches, setMatches] = useState<AttendeeMatch[]>([]);
  const [suggestions, setSuggestions] = useState<NetworkingSuggestion[]>([]);
  const [analytics, setAnalytics] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [criteria, setCriteria] = useState<MatchingCriteria>(DEFAULT_CRITERIA);
  const [showCriteriaDialog, setShowCriteriaDialog] = useState(false);
  const [selectedMatch, setSelectedMatch] = useState<AttendeeMatch | null>(null);

  useEffect(() => {
    loadMatches();
  }, [eventId]);

  const loadMatches = async () => {
    try {
      setIsLoading(true);
      const criteriaParam = encodeURIComponent(JSON.stringify({
        industryWeight: criteria.industryWeight / 100,
        interestsWeight: criteria.interestsWeight / 100,
        roleWeight: criteria.roleWeight / 100,
        companyWeight: criteria.companyWeight / 100,
        networkingGoalsWeight: criteria.networkingGoalsWeight / 100,
        geographicWeight: criteria.geographicWeight / 100,
        tierWeight: criteria.tierWeight / 100
      }));

      const response = await fetch(`/api/attendee-matching?eventId=${eventId}&limit=20&criteria=${criteriaParam}`);
      const data = await response.json();

      if (response.ok) {
        setMatches(data.matches);
        setSuggestions(data.suggestions);
        setAnalytics(data.analytics);
      } else {
        toast.error(data.error || 'Failed to load matches');
      }
    } catch (error) {
      console.error('Error loading matches:', error);
      toast.error('Failed to load matches');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMatchFeedback = async (matchedUserId: string, rating: number, feedback?: string, actionTaken?: string) => {
    try {
      const response = await fetch('/api/attendee-matching/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          eventId,
          matchedUserId,
          rating,
          feedback,
          actionTaken,
          connectionMade: rating >= 4
        })
      });

      if (response.ok) {
        toast.success('Feedback submitted successfully');
      } else {
        const data = await response.json();
        toast.error(data.error || 'Failed to submit feedback');
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      toast.error('Failed to submit feedback');
    }
  };

  const getCompatibilityColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600 bg-green-100';
    if (score >= 0.6) return 'text-blue-600 bg-blue-100';
    if (score >= 0.4) return 'text-yellow-600 bg-yellow-100';
    return 'text-gray-600 bg-gray-100';
  };

  const getCompatibilityLabel = (score: number) => {
    if (score >= 0.8) return 'Excellent Match';
    if (score >= 0.6) return 'Good Match';
    if (score >= 0.4) return 'Potential Match';
    return 'Low Match';
  };

  const getSuggestionIcon = (type: NetworkingSuggestion['type']) => {
    switch (type) {
      case 'direct_message': return <MessageCircle className="h-4 w-4" />;
      case 'meeting_request': return <Calendar className="h-4 w-4" />;
      case 'group_introduction': return <Users className="h-4 w-4" />;
      case 'skill_exchange': return <TrendingUp className="h-4 w-4" />;
      default: return <Lightbulb className="h-4 w-4" />;
    }
  };

  const getSuggestionColor = (priority: NetworkingSuggestion['priority']) => {
    switch (priority) {
      case 'high': return 'border-red-200 bg-red-50';
      case 'medium': return 'border-yellow-200 bg-yellow-50';
      case 'low': return 'border-blue-200 bg-blue-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  if (userTier !== EliteTier.ELITE_PRO) {
    return (
      <div className="text-center py-12">
        <Crown className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Elite Pro Feature Required
        </h3>
        <p className="text-gray-600 mb-6">
          Upgrade to Elite Pro to access advanced attendee matching and networking suggestions.
        </p>
        <Button className="bg-gradient-to-r from-purple-600 to-yellow-600 hover:from-purple-700 hover:to-yellow-700">
          <Crown className="h-4 w-4 mr-2" />
          Upgrade to Elite Pro
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Smart Networking</h2>
          <p className="text-gray-600">AI-powered attendee matching based on your profile and interests</p>
        </div>
        <div className="flex items-center space-x-2">
          <Dialog open={showCriteriaDialog} onOpenChange={setShowCriteriaDialog}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Customize
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Matching Criteria</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label>Industry Similarity ({criteria.industryWeight}%)</Label>
                  <Slider
                    value={[criteria.industryWeight]}
                    onValueChange={([value]) => setCriteria(prev => ({ ...prev, industryWeight: value }))}
                    max={50}
                    step={5}
                    className="mt-2"
                  />
                </div>
                <div>
                  <Label>Common Interests ({criteria.interestsWeight}%)</Label>
                  <Slider
                    value={[criteria.interestsWeight]}
                    onValueChange={([value]) => setCriteria(prev => ({ ...prev, interestsWeight: value }))}
                    max={50}
                    step={5}
                    className="mt-2"
                  />
                </div>
                <div>
                  <Label>Role Compatibility ({criteria.roleWeight}%)</Label>
                  <Slider
                    value={[criteria.roleWeight]}
                    onValueChange={([value]) => setCriteria(prev => ({ ...prev, roleWeight: value }))}
                    max={50}
                    step={5}
                    className="mt-2"
                  />
                </div>
                <div>
                  <Label>Company Diversity ({criteria.companyWeight}%)</Label>
                  <Slider
                    value={[criteria.companyWeight]}
                    onValueChange={([value]) => setCriteria(prev => ({ ...prev, companyWeight: value }))}
                    max={30}
                    step={5}
                    className="mt-2"
                  />
                </div>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setCriteria(DEFAULT_CRITERIA)}>
                    Reset
                  </Button>
                  <Button onClick={() => {
                    setShowCriteriaDialog(false);
                    loadMatches();
                  }}>
                    Apply
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
          
          <Button onClick={loadMatches} disabled={isLoading} size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Analytics Overview */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Target className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Matches</p>
                  <p className="text-2xl font-bold">{analytics.totalMatches}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Star className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="text-sm text-gray-600">High Quality</p>
                  <p className="text-2xl font-bold">{analytics.highCompatibilityMatches}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Industry Peers</p>
                  <p className="text-2xl font-bold">{analytics.industryMatches}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">Avg. Score</p>
                  <p className="text-2xl font-bold">{Math.round(analytics.averageCompatibility * 100)}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="matches" className="space-y-4">
        <TabsList>
          <TabsTrigger value="matches">
            <Users className="h-4 w-4 mr-2" />
            Matches ({matches.length})
          </TabsTrigger>
          <TabsTrigger value="suggestions">
            <Lightbulb className="h-4 w-4 mr-2" />
            Suggestions ({suggestions.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="matches" className="space-y-4">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[1, 2, 3, 4, 5, 6].map(i => (
                <div key={i} className="animate-pulse">
                  <div className="h-48 bg-gray-200 rounded-lg"></div>
                </div>
              ))}
            </div>
          ) : matches.length === 0 ? (
            <div className="text-center py-12">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                No Matches Found
              </h3>
              <p className="text-gray-600 mb-4">
                Try adjusting your matching criteria or complete your profile for better results.
              </p>
              <Button onClick={loadMatches}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {matches.map((match) => (
                <Card key={match.attendee.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-12 w-12">
                          <AvatarImage src={match.attendee.user?.image} />
                          <AvatarFallback>
                            {match.attendee.user?.name?.charAt(0) || 'U'}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h3 className="font-semibold">{match.attendee.displayName || match.attendee.user?.name}</h3>
                          <p className="text-sm text-gray-600">{match.attendee.role}</p>
                          <p className="text-xs text-gray-500">{match.attendee.company}</p>
                        </div>
                      </div>
                      <Badge className={`${getCompatibilityColor(match.compatibilityScore)} border-0`}>
                        {Math.round(match.compatibilityScore * 100)}%
                      </Badge>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-3">
                    <div>
                      <p className="text-sm font-medium text-gray-900 mb-1">
                        {getCompatibilityLabel(match.compatibilityScore)}
                      </p>
                      <Progress value={match.compatibilityScore * 100} className="h-2" />
                    </div>
                    
                    {match.commonInterests.length > 0 && (
                      <div>
                        <p className="text-xs text-gray-600 mb-1">Common Interests:</p>
                        <div className="flex flex-wrap gap-1">
                          {match.commonInterests.slice(0, 3).map((interest, idx) => (
                            <Badge key={idx} variant="secondary" className="text-xs">
                              {interest}
                            </Badge>
                          ))}
                          {match.commonInterests.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{match.commonInterests.length - 3}
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}
                    
                    {match.matchReasons.length > 0 && (
                      <div>
                        <p className="text-xs text-gray-600 mb-1">Why this match:</p>
                        <p className="text-xs text-gray-800">{match.matchReasons[0]}</p>
                      </div>
                    )}
                    
                    <div className="flex space-x-2 pt-2">
                      <Button 
                        size="sm" 
                        className="flex-1"
                        onClick={() => {
                          if (onStartDirectMessage) {
                            onStartDirectMessage(match.attendee.userId);
                            handleMatchFeedback(match.attendee.userId, 4, undefined, 'messaged');
                          }
                        }}
                      >
                        <MessageCircle className="h-3 w-3 mr-1" />
                        Message
                      </Button>
                      
                      {onScheduleMeeting && (
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => {
                            onScheduleMeeting(match.attendee.userId);
                            handleMatchFeedback(match.attendee.userId, 5, undefined, 'meeting_requested');
                          }}
                        >
                          <Calendar className="h-3 w-3 mr-1" />
                          Meet
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="suggestions" className="space-y-4">
          {suggestions.length === 0 ? (
            <div className="text-center py-12">
              <Lightbulb className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                No Suggestions Available
              </h3>
              <p className="text-gray-600">
                Get more matches to receive personalized networking suggestions.
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {suggestions.map((suggestion, idx) => (
                <Card key={idx} className={`${getSuggestionColor(suggestion.priority)} border-l-4`}>
                  <CardContent className="p-4">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-1">
                        {getSuggestionIcon(suggestion.type)}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{suggestion.title}</h4>
                        <p className="text-sm text-gray-600 mt-1">{suggestion.description}</p>
                        <Button size="sm" className="mt-3">
                          {suggestion.actionText}
                          <ArrowRight className="h-3 w-3 ml-1" />
                        </Button>
                      </div>
                      <Badge variant={suggestion.priority === 'high' ? 'destructive' : 'secondary'}>
                        {suggestion.priority}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
