import Link from 'next/link';
import { generateEventUrl } from '@/lib/utils/events';

interface EventLinkProps {
  event: {
    id: string;
    title: string;
    category: string;
  };
  children: React.ReactNode;
  className?: string;
  [key: string]: any; // Allow other props to be passed through
}

/**
 * A wrapper component for creating links to event detail pages
 * Automatically generates SEO-friendly URLs
 */
export default function EventLink({ event, children, ...props }: EventLinkProps) {
  const href = generateEventUrl(event);
  
  return (
    <Link href={href} {...props}>
      {children}
    </Link>
  );
}
