'use client';

import { useEffect, useState } from 'react';

/**
 * Database Initializer Component
 * 
 * This component ensures the database is initialized when the app starts.
 * It calls the server initialization API endpoint which includes Elite Communication seeding.
 */
export function DatabaseInitializer({ children }: { children: React.ReactNode }) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function initializeDatabase() {
      try {
        console.log('🚀 Initializing database with Elite Communication System...');
        
        const response = await fetch('/api/system/init', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const data = await response.json();

        if (data.success) {
          console.log('✅ Database initialization completed successfully');
          console.log('💎 Elite Communication System is ready with sample data');
          setIsInitialized(true);
        } else {
          console.warn('⚠️ Database initialization completed with warnings:', data.message);
          setError(data.message || 'Initialization completed with warnings');
          setIsInitialized(true); // Continue anyway
        }
      } catch (error) {
        console.error('❌ Database initialization failed:', error);
        setError(error instanceof Error ? error.message : 'Unknown error');
        setIsInitialized(true); // Continue anyway to not block the app
      } finally {
        setIsInitializing(false);
      }
    }

    // Only run in development or if explicitly enabled
    if (process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_AUTO_SEED === 'true') {
      initializeDatabase();
    } else {
      setIsInitializing(false);
      setIsInitialized(true);
    }
  }, []);

  // Show loading state during initialization
  if (isInitializing) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center p-8 bg-white rounded-lg shadow-lg max-w-md mx-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">
            🚀 Initializing Elite Communication System
          </h2>
          <p className="text-gray-600 text-sm">
            Setting up database with sample users, events, and networking features...
          </p>
          <div className="mt-4 text-xs text-gray-500">
            This only happens during development startup
          </div>
        </div>
      </div>
    );
  }

  // Show error state if initialization failed
  if (error && !isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-pink-100">
        <div className="text-center p-8 bg-white rounded-lg shadow-lg max-w-md mx-4">
          <div className="text-red-500 text-4xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">
            Database Initialization Warning
          </h2>
          <p className="text-gray-600 text-sm mb-4">
            {error}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
          <div className="mt-4 text-xs text-gray-500">
            The app will continue to work, but some sample data may be missing
          </div>
        </div>
      </div>
    );
  }

  // Render children once initialization is complete
  return <>{children}</>;
}

/**
 * Development-only Database Initializer
 * 
 * This version only runs in development mode and shows a minimal loading state
 */
export function DevDatabaseInitializer({ children }: { children: React.ReactNode }) {
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    async function init() {
      if (process.env.NODE_ENV === 'development') {
        try {
          console.log('🔧 Development mode: Ensuring database is seeded...');
          
          const response = await fetch('/api/system/init');
          const data = await response.json();
          
          if (data.success) {
            console.log('✅ Elite Communication System ready for development');
          } else {
            console.warn('⚠️ Database initialization warning:', data.message);
          }
        } catch (error) {
          console.warn('⚠️ Could not verify database initialization:', error);
        }
      }
      
      setIsReady(true);
    }

    init();
  }, []);

  if (!isReady && process.env.NODE_ENV === 'development') {
    return (
      <div className="fixed top-4 right-4 z-50">
        <div className="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          <span className="text-sm">Initializing Elite Communication...</span>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
