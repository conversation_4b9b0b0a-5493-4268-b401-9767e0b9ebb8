// Partner Type Enum
enum PartnerType {
  HOTEL
  RESTAURANT
  BAR
  NIGHTCLUB
}

// Partnership Tier Enum
enum PartnershipTier {
  BASIC
  PREMIUM
  ELITE
}

// Partner Model
model Partner {
  id                  String          @id @default(cuid())
  userId              String          @unique
  businessName        String
  partnerType         PartnerType
  tier                PartnershipTier @default(BASIC)
  description         String?
  address             String
  city                String
  province            String
  postalCode          String?
  country             String          @default("Zambia")
  latitude            Float?
  longitude           Float?
  contactName         String
  contactEmail        String
  contactPhone        String
  website             String?
  logo                String?
  bannerImage         String?
  galleryImages       Json?
  socialLinks         Json?
  businessHours       Json?
  amenities           String[]
  priceRange          String?
  rating              Float?
  totalReviews        Int             @default(0)
  isVerified          Boolean         @default(false)
  verifiedAt          DateTime?
  featured            Boolean         @default(false)
  acceptsNfcPayments  Boolean         @default(false)
  nfcTerminalId       String?
  commissionRate      Float           @default(5.0)
  createdAt           DateTime        @default(now())
  updatedAt           DateTime        @updatedAt
  user                User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  promotions          PartnerPromotion[]
  reviews             PartnerReview[]
  nfcTransactions     PartnerNFCTransaction[]
  loyaltyProgram      LoyaltyProgram?
  eventPartnerships   EventPartner[]
  menuItems           MenuItem[]

  @@index([partnerType])
  @@index([tier])
  @@index([city])
  @@index([featured])
  @@index([isVerified])
  @@index([acceptsNfcPayments])
}

// Partner Promotion Model
model PartnerPromotion {
  id              String      @id @default(cuid())
  partnerId       String
  eventId         String?
  title           String
  description     String
  startDate       DateTime
  endDate         DateTime
  discountValue   Float?
  discountType    String?     // "PERCENTAGE", "FIXED", etc.
  promoCode       String?
  isActive        Boolean     @default(true)
  maxUses         Int?
  currentUses     Int         @default(0)
  imageUrl        String?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
  partner         Partner     @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  event           Event?      @relation(fields: [eventId], references: [id])

  @@index([partnerId])
  @@index([eventId])
  @@index([isActive])
  @@index([startDate])
  @@index([endDate])
}

// Partner Review Model
model PartnerReview {
  id          String   @id @default(cuid())
  partnerId   String
  userId      String
  rating      Float
  comment     String?
  isPublished Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  partner     Partner  @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  user        User     @relation(fields: [userId], references: [id])

  @@index([partnerId])
  @@index([userId])
  @@index([rating])
}

// Partner NFC Transaction Model
model PartnerNFCTransaction {
  id              String            @id @default(cuid())
  partnerId       String
  userId          String
  cardId          String?
  deviceId        String?
  amount          Float
  currency        String            @default("ZMW")
  status          TransactionStatus @default(PENDING)
  reference       String?
  receiptUrl      String?
  notes           String?
  metadata        Json?
  createdAt       DateTime          @default(now())
  processedAt     DateTime?
  partner         Partner           @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  user            User              @relation(fields: [userId], references: [id])
  items           PartnerNFCTransactionItem[]
  loyaltyPoints   Int               @default(0)

  @@index([partnerId])
  @@index([userId])
  @@index([cardId])
  @@index([deviceId])
  @@index([status])
  @@index([createdAt])
}

// Partner NFC Transaction Item Model
model PartnerNFCTransactionItem {
  id            String                @id @default(cuid())
  transactionId String
  menuItemId    String?
  name          String
  quantity      Int
  unitPrice     Float
  totalPrice    Float
  createdAt     DateTime              @default(now())
  transaction   PartnerNFCTransaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  menuItem      MenuItem?             @relation(fields: [menuItemId], references: [id])

  @@index([transactionId])
  @@index([menuItemId])
}

// Menu Item Model
model MenuItem {
  id                String                    @id @default(cuid())
  partnerId         String
  name              String
  description       String?
  category          String
  price             Float
  imageUrl          String?
  isAvailable       Boolean                   @default(true)
  allergens         String[]
  nutritionalInfo   Json?
  preparationTime   Int?                      // in minutes
  isPopular         Boolean                   @default(false)
  isVegetarian      Boolean                   @default(false)
  isVegan           Boolean                   @default(false)
  isGlutenFree      Boolean                   @default(false)
  createdAt         DateTime                  @default(now())
  updatedAt         DateTime                  @updatedAt
  partner           Partner                   @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  transactionItems  PartnerNFCTransactionItem[]

  @@index([partnerId])
  @@index([category])
  @@index([isAvailable])
  @@index([isPopular])
}

// Loyalty Program Model
model LoyaltyProgram {
  id                String   @id @default(cuid())
  partnerId         String   @unique
  name              String
  description       String?
  pointsPerCurrency Float    @default(1.0)  // e.g., 1 point per ZMW
  pointsExpiration  Int?     // days until points expire, null for no expiration
  tiers             Json?    // JSON array of tier definitions
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  partner           Partner  @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  members           LoyaltyProgramMember[]

  @@index([partnerId])
}

// Loyalty Program Member Model
model LoyaltyProgramMember {
  id              String        @id @default(cuid())
  programId       String
  userId          String
  points          Int           @default(0)
  tier            String        @default("STANDARD")
  joinedAt        DateTime      @default(now())
  lastActivity    DateTime?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  program         LoyaltyProgram @relation(fields: [programId], references: [id], onDelete: Cascade)
  user            User          @relation(fields: [userId], references: [id])

  @@unique([programId, userId])
  @@index([programId])
  @@index([userId])
  @@index([tier])
}

// Event Partner Model
model EventPartner {
  id              String   @id @default(cuid())
  eventId         String
  partnerId       String
  partnerType     String   // "OFFICIAL_HOTEL", "RECOMMENDED_RESTAURANT", etc.
  specialOffer    String?
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  event           Event    @relation(fields: [eventId], references: [id], onDelete: Cascade)
  partner         Partner  @relation(fields: [partnerId], references: [id], onDelete: Cascade)

  @@unique([eventId, partnerId])
  @@index([eventId])
  @@index([partnerId])
  @@index([partnerType])
}
