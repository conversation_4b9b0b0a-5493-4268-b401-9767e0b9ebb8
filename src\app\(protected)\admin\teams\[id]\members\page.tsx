'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  Users,
  Search,
  UserPlus,
  MoreHorizontal,
  Trash2,
  Edit,
  ArrowLeft,
  Shield,
  Mail
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';

// Interfaces
interface User {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
}

interface TeamMember {
  id: string;
  userId: string;
  teamId: string;
  role: string;
  user: User;
}

interface Team {
  id: string;
  name: string;
  description: string | null;
  ownerId: string;
  owner: User;
  members: TeamMember[];
  _count: {
    members: number;
    events: number;
    invitations: number;
  };
}

interface Invitation {
  id: string;
  email: string;
  role: string;
  status: string;
  expiresAt: string;
  token: string;
  invitedBy: User;
}

interface PageProps {
  params: { id: string };
}

export default function AdminTeamMembersPage({ params }: PageProps) {
  const router = useRouter();
  const [team, setTeam] = useState<Team | null>(null);
  const [members, setMembers] = useState<TeamMember[]>([]);
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [filteredMembers, setFilteredMembers] = useState<TeamMember[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  // Add member state
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState('ORGANIZER_EDITOR');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Edit member state
  const [memberToEdit, setMemberToEdit] = useState<TeamMember | null>(null);
  const [newRole, setNewRole] = useState('');
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Remove member state
  const [memberToRemove, setMemberToRemove] = useState<TeamMember | null>(null);
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false);

  // Cancel invitation state
  const [invitationToCancel, setInvitationToCancel] = useState<Invitation | null>(null);
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false);

  // Resend invitation state
  const [isResendingInvitation, setIsResendingInvitation] = useState(false);

  // Fetch team and members
  useEffect(() => {
    const fetchTeamData = async () => {
      try {
        setIsLoading(true);

        // Fetch team details
        const teamResponse = await fetch(`/api/admin/teams/${params.id}`);

        if (!teamResponse.ok) {
          throw new Error('Failed to fetch team details');
        }

        const teamData = await teamResponse.json();
        setTeam(teamData.team);
        setMembers(teamData.team.members);
        setFilteredMembers(teamData.team.members);

        // Fetch invitations
        const invitationsResponse = await fetch(`/api/admin/teams/${params.id}/invitations`);

        if (invitationsResponse.ok) {
          const invitationsData = await invitationsResponse.json();
          setInvitations(invitationsData.invitations);
        }
      } catch (error) {
        console.error('Error fetching team data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load team data. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchTeamData();
  }, [params.id]);

  // Filter members based on search query
  useEffect(() => {
    if (!members) return;

    const filtered = members.filter(member =>
      member.user.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      member.user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      member.role.toLowerCase().includes(searchQuery.toLowerCase())
    );

    setFilteredMembers(filtered);
  }, [members, searchQuery]);

  // Handle invite member
  const handleInviteMember = async () => {
    if (!inviteEmail.trim()) {
      toast({
        title: 'Error',
        description: 'Email is required',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const response = await fetch(`/api/admin/teams/${params.id}/members`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: inviteEmail,
          role: inviteRole,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }

      const data = await response.json();

      toast({
        title: 'Success',
        description: data.message || 'Invitation sent successfully',
      });

      // If the user was added directly (not invited)
      if (data.member) {
        setMembers(prevMembers => [...prevMembers, data.member]);
      }

      // If an invitation was created
      if (data.invitation) {
        setInvitations(prevInvitations => [...prevInvitations, data.invitation]);
      }

      // Reset form and close dialog
      setInviteEmail('');
      setInviteRole('ORGANIZER_EDITOR');
      setIsAddDialogOpen(false);
    } catch (error) {
      console.error('Error inviting member:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to invite member',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle edit member role
  const handleEditMemberRole = async () => {
    if (!memberToEdit) return;

    try {
      setIsSubmitting(true);

      const response = await fetch(`/api/admin/teams/${params.id}/members/${memberToEdit.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          role: newRole,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }

      const data = await response.json();

      toast({
        title: 'Success',
        description: 'Member role updated successfully',
      });

      // Update the member in the list
      setMembers(prevMembers =>
        prevMembers.map(member =>
          member.id === memberToEdit.id ? data.member : member
        )
      );

      // Reset state and close dialog
      setMemberToEdit(null);
      setNewRole('');
      setIsEditDialogOpen(false);
    } catch (error) {
      console.error('Error updating member role:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update member role',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle remove member
  const handleRemoveMember = async () => {
    if (!memberToRemove) return;

    try {
      setIsSubmitting(true);

      const response = await fetch(`/api/admin/teams/${params.id}/members/${memberToRemove.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }

      toast({
        title: 'Success',
        description: 'Member removed successfully',
      });

      // Remove the member from the list
      setMembers(prevMembers =>
        prevMembers.filter(member => member.id !== memberToRemove.id)
      );

      // Reset state and close dialog
      setMemberToRemove(null);
      setIsRemoveDialogOpen(false);
    } catch (error) {
      console.error('Error removing member:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to remove member',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel invitation
  const handleCancelInvitation = async () => {
    if (!invitationToCancel) return;

    try {
      setIsSubmitting(true);

      const response = await fetch(`/api/admin/teams/invitations/${invitationToCancel.token}/cancel`, {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }

      toast({
        title: 'Success',
        description: 'Invitation cancelled successfully',
      });

      // Remove the invitation from the list
      setInvitations(prevInvitations =>
        prevInvitations.filter(invitation => invitation.id !== invitationToCancel.id)
      );

      // Reset state and close dialog
      setInvitationToCancel(null);
      setIsCancelDialogOpen(false);
    } catch (error) {
      console.error('Error cancelling invitation:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to cancel invitation',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle resend invitation
  const handleResendInvitation = async (invitation: Invitation) => {
    try {
      setIsResendingInvitation(true);

      const response = await fetch(`/api/admin/teams/invitations/${invitation.token}/resend`, {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }

      toast({
        title: 'Success',
        description: 'Invitation resent successfully',
      });

      // Update the invitation in the list with new expiration date
      const data = await response.json();
      setInvitations(prevInvitations =>
        prevInvitations.map(inv =>
          inv.id === invitation.id ? { ...inv, expiresAt: data.invitation.expiresAt } : inv
        )
      );
    } catch (error) {
      console.error('Error resending invitation:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to resend invitation',
        variant: 'destructive',
      });
    } finally {
      setIsResendingInvitation(false);
    }
  };

  // Helper function to get user initials
  const getUserInitials = (name: string | null): string => {
    if (!name) return '??';
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Helper function to get role badge
  const getRoleBadge = (role: string) => {
    let variant: 'default' | 'secondary' | 'outline' | 'destructive' = 'default';

    switch (role) {
      case 'ORGANIZER_ADMIN':
        variant = 'destructive';
        break;
      case 'ORGANIZER_MANAGER':
        variant = 'default';
        break;
      case 'ORGANIZER_EDITOR':
        variant = 'secondary';
        break;
      default:
        variant = 'outline';
    }

    return (
      <Badge variant={variant} className="mr-2">
        {role.replace('ORGANIZER_', '')}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center mb-8">
          <Skeleton className="h-8 w-8 mr-2" />
          <Skeleton className="h-8 w-40" />
        </div>
        <Skeleton className="h-12 w-64 mb-8" />
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-40 mb-2" />
            <Skeleton className="h-4 w-24" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-center justify-between p-4 rounded-lg border">
                  <div className="flex items-center">
                    <Skeleton className="h-10 w-10 rounded-full mr-3" />
                    <div>
                      <Skeleton className="h-4 w-40 mb-2" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Skeleton className="h-8 w-20 mr-2" />
                    <Skeleton className="h-8 w-8 rounded-full" />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!team) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-2">Team Not Found</h2>
          <p className="text-gray-500 mb-4">The team you're looking for doesn't exist or you don't have permission to view it.</p>
          <Button asChild>
            <Link href="/admin/teams">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Teams
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <Button variant="outline" asChild className="mb-4">
          <Link href="/admin/teams">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Teams
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">{team.name} - Team Members</h1>
        <p className="text-gray-500 mt-1">
          Manage team members and invitations
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                  <CardTitle>Team Members</CardTitle>
                  <CardDescription>
                    {members.length} {members.length === 1 ? 'member' : 'members'} in this team
                  </CardDescription>
                </div>
                <div className="mt-4 md:mt-0 w-full md:w-64">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search members..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex justify-end mb-4">
                <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                  <DialogTrigger asChild>
                    <Button>
                      <UserPlus className="mr-2 h-4 w-4" />
                      Add Member
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Add Team Member</DialogTitle>
                      <DialogDescription>
                        Add a new member to the team by email
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          type="email"
                          placeholder="Enter email address"
                          value={inviteEmail}
                          onChange={(e) => setInviteEmail(e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="role">Role</Label>
                        <Select value={inviteRole} onValueChange={setInviteRole}>
                          <SelectTrigger id="role">
                            <SelectValue placeholder="Select a role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="ORGANIZER_ADMIN">Admin</SelectItem>
                            <SelectItem value="ORGANIZER_MANAGER">Manager</SelectItem>
                            <SelectItem value="ORGANIZER_EDITOR">Editor</SelectItem>
                            <SelectItem value="ORGANIZER_ANALYST">Analyst</SelectItem>
                            <SelectItem value="ORGANIZER_SUPPORT">Support</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setIsAddDialogOpen(false)} disabled={isSubmitting}>
                        Cancel
                      </Button>
                      <Button onClick={handleInviteMember} disabled={isSubmitting}>
                        {isSubmitting ? 'Adding...' : 'Add Member'}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>

              {members.length === 0 ? (
                <div className="text-center py-6">
                  <Users className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                  <h3 className="text-lg font-medium">No team members</h3>
                  <p className="text-gray-500 mt-2">
                    Add members to collaborate on events
                  </p>
                </div>
              ) : filteredMembers.length === 0 ? (
                <div className="text-center py-6">
                  <Users className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                  <h3 className="text-lg font-medium">No members found</h3>
                  <p className="text-gray-500 mt-2">
                    Try a different search term
                  </p>
                  <Button variant="outline" onClick={() => setSearchQuery('')} className="mt-4">
                    Clear Search
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredMembers.map((member) => (
                    <div key={member.id} className="flex items-center justify-between p-4 rounded-lg border">
                      <div className="flex items-center">
                        <Avatar className="h-10 w-10 mr-3">
                          <AvatarImage src={member.user.image || ''} alt={member.user.name || 'Team member'} />
                          <AvatarFallback>{getUserInitials(member.user.name)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{member.user.name || 'Unnamed User'}</div>
                          <div className="text-sm text-gray-500">{member.user.email}</div>
                        </div>
                      </div>
                      <div className="flex items-center">
                        {getRoleBadge(member.role)}

                        {member.user.id === team.ownerId ? (
                          <Badge variant="outline" className="ml-2">
                            <Shield className="h-3 w-3 mr-1" />
                            Owner
                          </Badge>
                        ) : (
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={() => {
                                  setMemberToEdit(member);
                                  setNewRole(member.role);
                                  setIsEditDialogOpen(true);
                                }}
                              >
                                <Edit className="h-4 w-4 mr-2" />
                                Change Role
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className="text-red-600"
                                onClick={() => {
                                  setMemberToRemove(member);
                                  setIsRemoveDialogOpen(true);
                                }}
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Remove Member
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Pending Invitations</CardTitle>
              <CardDescription>
                {invitations.filter(inv => inv.status === 'PENDING').length} pending invitations
              </CardDescription>
            </CardHeader>
            <CardContent>
              {invitations.filter(inv => inv.status === 'PENDING').length === 0 ? (
                <div className="text-center py-6">
                  <Mail className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                  <h3 className="text-lg font-medium">No pending invitations</h3>
                  <p className="text-gray-500 mt-2">
                    All invitations have been accepted or declined
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {invitations
                    .filter(inv => inv.status === 'PENDING')
                    .map((invitation) => (
                      <div key={invitation.id} className="p-4 rounded-lg border">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <div className="font-medium">{invitation.email}</div>
                            <div className="text-sm text-gray-500">
                              Invited by {invitation.invitedBy.name || invitation.invitedBy.email}
                            </div>
                          </div>
                          {getRoleBadge(invitation.role)}
                        </div>
                        <div className="flex justify-end mt-4 space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleResendInvitation(invitation)}
                            disabled={isResendingInvitation}
                          >
                            <Mail className="h-4 w-4 mr-1" />
                            Resend
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600"
                            onClick={() => {
                              setInvitationToCancel(invitation);
                              setIsCancelDialogOpen(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            Cancel
                          </Button>
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Edit Member Role Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Change Member Role</DialogTitle>
            <DialogDescription>
              Update the role for {memberToEdit?.user.name || memberToEdit?.user.email}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-2">
              <Label htmlFor="role">Role</Label>
              <Select value={newRole} onValueChange={setNewRole}>
                <SelectTrigger id="role">
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ORGANIZER_ADMIN">Admin</SelectItem>
                  <SelectItem value="ORGANIZER_MANAGER">Manager</SelectItem>
                  <SelectItem value="ORGANIZER_EDITOR">Editor</SelectItem>
                  <SelectItem value="ORGANIZER_ANALYST">Analyst</SelectItem>
                  <SelectItem value="ORGANIZER_SUPPORT">Support</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button onClick={handleEditMemberRole} disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Remove Member Confirmation Dialog */}
      <AlertDialog open={isRemoveDialogOpen} onOpenChange={setIsRemoveDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Team Member</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove {memberToRemove?.user.name || memberToRemove?.user.email} from the team?
              They will no longer have access to team resources.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isSubmitting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleRemoveMember();
              }}
              disabled={isSubmitting}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {isSubmitting ? 'Removing...' : 'Remove Member'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Cancel Invitation Confirmation Dialog */}
      <AlertDialog open={isCancelDialogOpen} onOpenChange={setIsCancelDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Cancel Invitation</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to cancel the invitation sent to {invitationToCancel?.email}?
              They will no longer be able to join the team with this invitation.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isSubmitting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleCancelInvitation();
              }}
              disabled={isSubmitting}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {isSubmitting ? 'Cancelling...' : 'Cancel Invitation'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
