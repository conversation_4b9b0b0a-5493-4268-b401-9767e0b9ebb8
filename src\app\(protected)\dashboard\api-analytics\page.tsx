'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Loader2, BarChart3, TrendingUp, Clock, Download, AlertTriangle, Bell, BellOff } from 'lucide-react';
import { toast } from 'sonner';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

interface ApiKeyUsage {
  id: string;
  apiKeyId: string;
  endpoint: string;
  method: string;
  status: number;
  timestamp: string;
  ipAddress: string | null;
  userAgent: string | null;
}

interface ApiKey {
  id: string;
  name: string;
  key: string;
  usageCount: number;
  rateLimit: number;
}

interface ApiUsageStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  topEndpoints: { endpoint: string; count: number }[];
  requestsByMethod: { method: string; count: number }[];
  requestsByDay: { date: string; count: number }[];
}

export default function ApiAnalyticsPage() {
  const [loading, setLoading] = useState(true);
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [selectedApiKeyId, setSelectedApiKeyId] = useState<string>('all');
  const [usageData, setUsageData] = useState<ApiKeyUsage[]>([]);
  const [timeRange, setTimeRange] = useState<'24h' | '7d' | '30d' | 'all'>('7d');
  const [stats, setStats] = useState<ApiUsageStats>({
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    topEndpoints: [],
    requestsByMethod: [],
    requestsByDay: [],
  });

  // State for alerts
  const [alerts, setAlerts] = useState<{
    highErrorRate: boolean;
    unusualTraffic: boolean;
    rateLimitExceeded: boolean;
  }>({
    highErrorRate: false,
    unusualTraffic: false,
    rateLimitExceeded: false
  });

  // State for alert settings
  const [alertSettings, setAlertSettings] = useState<{
    emailAlerts: boolean;
    errorRateThreshold: number;
    trafficSpikeThreshold: number;
    rateLimitThreshold: number;
  }>({
    emailAlerts: false,
    errorRateThreshold: 10, // 10% error rate
    trafficSpikeThreshold: 300, // 300% increase
    rateLimitThreshold: 80, // 80% of rate limit
  });

  // Fetch API keys
  useEffect(() => {
    const fetchApiKeys = async () => {
      try {
        const response = await fetch('/api/api-keys');
        if (!response.ok) {
          throw new Error('Failed to fetch API keys');
        }
        const data = await response.json();
        setApiKeys(data);
        if (data.length > 0) {
          setSelectedApiKeyId(data[0].id);
        }
      } catch (error) {
        console.error('Error fetching API keys:', error);
        toast.error('Failed to fetch API keys');
      } finally {
        setLoading(false);
      }
    };

    fetchApiKeys();
  }, []);

  // Check for unusual activity in the API usage data
  const checkForUnusualActivity = useCallback((stats: ApiUsageStats, usageData: ApiKeyUsage[]) => {
    // Check for high error rate
    const errorRate = stats.totalRequests > 0 ? (stats.failedRequests / stats.totalRequests) * 100 : 0;
    const highErrorRate = errorRate > alertSettings.errorRateThreshold;

    // Check for unusual traffic patterns
    let unusualTraffic = false;
    if (stats.requestsByDay.length > 1) {
      // Calculate average requests per day
      const avgRequests = stats.totalRequests / stats.requestsByDay.length;

      // Check if any day has more than the threshold percentage increase over the average
      unusualTraffic = stats.requestsByDay.some(day => {
        const percentIncrease = ((day.count - avgRequests) / avgRequests) * 100;
        return percentIncrease > alertSettings.trafficSpikeThreshold;
      });
    }

    // Check for rate limit exceeded
    const rateLimitExceeded = usageData.some(log => log.status === 429);

    // Check if any alerts are new
    const newAlerts = {
      highErrorRate,
      unusualTraffic,
      rateLimitExceeded
    };

    // If email alerts are enabled and there are new alerts, send an email
    if (alertSettings.emailAlerts) {
      // Check if any new alerts that weren't present before
      if ((newAlerts.highErrorRate && !alerts.highErrorRate) ||
          (newAlerts.unusualTraffic && !alerts.unusualTraffic) ||
          (newAlerts.rateLimitExceeded && !alerts.rateLimitExceeded)) {

        // In a real implementation, this would call an API endpoint to send an email
        // For now, we'll just show a toast
        toast.info('Alert email would be sent for unusual API activity');

        // You could also call an API endpoint to log the alert
        // fetch('/api/api-alerts', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify({
        //     type: newAlerts.highErrorRate ? 'high_error_rate' :
        //           newAlerts.unusualTraffic ? 'unusual_traffic' : 'rate_limit_exceeded',
        //     severity: 'medium',
        //     message: 'Unusual API activity detected',
        //     details: { stats, newAlerts },
        //     apiKeyId: selectedApiKeyId !== 'all' ? selectedApiKeyId : undefined
        //   })
        // });
      }
    }

    // Update alerts state
    setAlerts(newAlerts);
  }, [alertSettings, alerts]);

  // Fetch usage data when API key or time range changes
  useEffect(() => {
    const fetchUsageData = async () => {
      if (!selectedApiKeyId && selectedApiKeyId !== 'all') return;

      setLoading(true);
      try {
        const url = `/api/api-analytics?apiKeyId=${selectedApiKeyId}&timeRange=${timeRange}`;
        const response = await fetch(url);

        if (!response.ok) {
          throw new Error('Failed to fetch API usage data');
        }

        const data = await response.json();
        setUsageData(data.usageData);
        setStats(data.stats);

        // Check for unusual activity
        checkForUnusualActivity(data.stats, data.usageData);
      } catch (error) {
        console.error('Error fetching API usage data:', error);
        toast.error('Failed to fetch API usage data');
      } finally {
        setLoading(false);
      }
    };

    fetchUsageData();
  }, [selectedApiKeyId, timeRange, checkForUnusualActivity]);

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  // Export analytics data as CSV
  const exportAnalyticsData = () => {
    try {
      // Create CSV content
      let csvContent = 'data:text/csv;charset=utf-8,';

      // Add headers
      csvContent += 'Timestamp,Method,Endpoint,Status,IP Address\n';

      // Add data rows
      usageData.forEach(log => {
        const row = [
          new Date(log.timestamp).toISOString(),
          log.method,
          log.endpoint,
          log.status,
          log.ipAddress || 'Unknown'
        ];

        // Escape commas and quotes in the data
        const escapedRow = row.map(field => {
          const stringField = String(field);
          if (stringField.includes(',') || stringField.includes('"') || stringField.includes('\n')) {
            return `"${stringField.replace(/"/g, '""')}"`;  // Escape quotes by doubling them
          }
          return stringField;
        });

        csvContent += escapedRow.join(',') + '\n';
      });

      // Create a download link
      const encodedUri = encodeURI(csvContent);
      const link = document.createElement('a');
      link.setAttribute('href', encodedUri);
      link.setAttribute('download', `api-analytics-${selectedApiKeyId}-${timeRange}-${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);

      // Trigger download
      link.click();

      // Clean up
      document.body.removeChild(link);

      toast.success('Analytics data exported successfully');
    } catch (error) {
      console.error('Error exporting analytics data:', error);
      toast.error('Failed to export analytics data');
    }
  };

  // Render loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">API Usage Analytics</h1>
        <div className="flex gap-4">
          <Select value={selectedApiKeyId} onValueChange={setSelectedApiKeyId}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Select API Key" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All API Keys</SelectItem>
              {apiKeys.map((apiKey) => (
                <SelectItem key={apiKey.id} value={apiKey.id}>
                  {apiKey.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Time Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="24h">Last 24 Hours</SelectItem>
              <SelectItem value="7d">Last 7 Days</SelectItem>
              <SelectItem value="30d">Last 30 Days</SelectItem>
              <SelectItem value="all">All Time</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            onClick={() => exportAnalyticsData()}
            className="flex items-center gap-2"
          >
            <Download size={16} />
            Export Data
          </Button>
        </div>
      </div>

      {/* Alerts */}
      {(alerts.highErrorRate || alerts.unusualTraffic || alerts.rateLimitExceeded) && (
        <div className="mb-8 bg-amber-50 border border-amber-200 rounded-lg p-4">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5 mr-3" />
            <div>
              <h3 className="font-medium text-amber-800">Unusual API Activity Detected</h3>
              <ul className="mt-2 text-sm text-amber-700 space-y-1">
                {alerts.highErrorRate && (
                  <li className="flex items-center">
                    <span className="h-1.5 w-1.5 rounded-full bg-amber-500 mr-2"></span>
                    High error rate detected: More than 10% of requests are failing
                  </li>
                )}
                {alerts.unusualTraffic && (
                  <li className="flex items-center">
                    <span className="h-1.5 w-1.5 rounded-full bg-amber-500 mr-2"></span>
                    Unusual traffic pattern: Significant spike in request volume
                  </li>
                )}
                {alerts.rateLimitExceeded && (
                  <li className="flex items-center">
                    <span className="h-1.5 w-1.5 rounded-full bg-amber-500 mr-2"></span>
                    Rate limit exceeded: Some requests were throttled
                  </li>
                )}
              </ul>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Total Requests</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <BarChart3 className="h-5 w-5 text-gray-400 mr-2" />
              <span className="text-3xl font-bold">{stats.totalRequests}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Success Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <TrendingUp className="h-5 w-5 text-gray-400 mr-2" />
              <span className="text-3xl font-bold">
                {stats.totalRequests > 0
                  ? Math.round((stats.successfulRequests / stats.totalRequests) * 100)
                  : 0}%
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              {timeRange === '24h' ? 'Requests Today' :
               timeRange === '7d' ? 'Requests This Week' :
               timeRange === '30d' ? 'Requests This Month' : 'Average Requests/Day'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Clock className="h-5 w-5 text-gray-400 mr-2" />
              <span className="text-3xl font-bold">
                {timeRange === 'all' && stats.requestsByDay.length > 0
                  ? Math.round(stats.totalRequests / stats.requestsByDay.length)
                  : stats.requestsByDay.reduce((sum, day) => sum + day.count, 0)}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="endpoints">Top Endpoints</TabsTrigger>
          <TabsTrigger value="methods">Methods</TabsTrigger>
          <TabsTrigger value="logs">Request Logs</TabsTrigger>
          <TabsTrigger value="settings">Alert Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Request Volume Over Time</CardTitle>
              <CardDescription>
                Number of API requests over the selected time period
              </CardDescription>
            </CardHeader>
            <CardContent>
              {stats.requestsByDay.length === 0 ? (
                <div className="h-64 flex items-center justify-center text-gray-500">
                  No data available for the selected time range
                </div>
              ) : (
                <div className="h-64">
                  {/* This would be a chart component in a real implementation */}
                  <div className="h-full flex items-end justify-between">
                    {stats.requestsByDay.map((day, index) => (
                      <div key={index} className="flex flex-col items-center">
                        <div
                          className="bg-blue-500 w-8 rounded-t"
                          style={{
                            height: `${Math.max(5, (day.count / Math.max(...stats.requestsByDay.map(d => d.count))) * 200)}px`
                          }}
                        ></div>
                        <span className="text-xs mt-2">{new Date(day.date).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="endpoints" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Top Endpoints</CardTitle>
              <CardDescription>
                Most frequently accessed API endpoints
              </CardDescription>
            </CardHeader>
            <CardContent>
              {stats.topEndpoints.length === 0 ? (
                <div className="h-64 flex items-center justify-center text-gray-500">
                  No data available for the selected time range
                </div>
              ) : (
                <div className="space-y-4">
                  {stats.topEndpoints.map((endpoint, index) => (
                    <div key={index} className="flex items-center">
                      <div className="w-8 text-gray-500">{index + 1}.</div>
                      <div className="flex-1 font-mono text-sm">{endpoint.endpoint}</div>
                      <div className="w-16 text-right">{endpoint.count}</div>
                      <div className="w-32 ml-4">
                        <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-blue-500 rounded-full"
                            style={{
                              width: `${(endpoint.count / stats.topEndpoints[0].count) * 100}%`
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="methods" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Requests by Method</CardTitle>
              <CardDescription>
                Distribution of API requests by HTTP method
              </CardDescription>
            </CardHeader>
            <CardContent>
              {stats.requestsByMethod.length === 0 ? (
                <div className="h-64 flex items-center justify-center text-gray-500">
                  No data available for the selected time range
                </div>
              ) : (
                <div className="grid grid-cols-4 gap-4">
                  {stats.requestsByMethod.map((method, index) => (
                    <Card key={index}>
                      <CardContent className="pt-6">
                        <div className="text-center">
                          <div className={`
                            inline-block px-3 py-1 rounded text-white font-medium mb-2
                            ${method.method === 'GET' ? 'bg-blue-500' :
                              method.method === 'POST' ? 'bg-green-500' :
                              method.method === 'PUT' ? 'bg-yellow-500' :
                              method.method === 'DELETE' ? 'bg-red-500' : 'bg-gray-500'}
                          `}>
                            {method.method}
                          </div>
                          <div className="text-3xl font-bold">{method.count}</div>
                          <div className="text-sm text-gray-500 mt-1">
                            {Math.round((method.count / stats.totalRequests) * 100)}% of total
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Request Logs</CardTitle>
              <CardDescription>
                Detailed logs of API requests
              </CardDescription>
            </CardHeader>
            <CardContent>
              {usageData.length === 0 ? (
                <div className="h-64 flex items-center justify-center text-gray-500">
                  No data available for the selected time range
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="py-2 px-4 text-left">Timestamp</th>
                        <th className="py-2 px-4 text-left">Method</th>
                        <th className="py-2 px-4 text-left">Endpoint</th>
                        <th className="py-2 px-4 text-left">Status</th>
                        <th className="py-2 px-4 text-left">IP Address</th>
                      </tr>
                    </thead>
                    <tbody>
                      {usageData.map((log) => (
                        <tr key={log.id} className="border-b hover:bg-gray-50">
                          <td className="py-2 px-4">{formatDate(log.timestamp)}</td>
                          <td className="py-2 px-4">
                            <span className={`
                              inline-block px-2 py-1 rounded text-xs text-white
                              ${log.method === 'GET' ? 'bg-blue-500' :
                                log.method === 'POST' ? 'bg-green-500' :
                                log.method === 'PUT' ? 'bg-yellow-500' :
                                log.method === 'DELETE' ? 'bg-red-500' : 'bg-gray-500'}
                            `}>
                              {log.method}
                            </span>
                          </td>
                          <td className="py-2 px-4 font-mono text-sm">{log.endpoint}</td>
                          <td className="py-2 px-4">
                            <span className={`
                              inline-block px-2 py-1 rounded text-xs
                              ${log.status < 300 ? 'bg-green-100 text-green-800' :
                                log.status < 400 ? 'bg-blue-100 text-blue-800' :
                                log.status < 500 ? 'bg-yellow-100 text-yellow-800' :
                                'bg-red-100 text-red-800'}
                            `}>
                              {log.status}
                            </span>
                          </td>
                          <td className="py-2 px-4 text-sm">{log.ipAddress || 'Unknown'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Alert Settings</CardTitle>
              <CardDescription>
                Configure when and how you want to be notified about unusual API activity
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-alerts">Email Alerts</Label>
                    <div className="text-sm text-gray-500">
                      Receive email notifications for unusual API activity
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="email-alerts"
                      checked={alertSettings.emailAlerts}
                      onCheckedChange={(checked) => {
                        setAlertSettings(prev => ({ ...prev, emailAlerts: checked }));
                        toast.success(checked ? 'Email alerts enabled' : 'Email alerts disabled');
                      }}
                    />
                    {alertSettings.emailAlerts ?
                      <Bell className="h-4 w-4 text-gray-500" /> :
                      <BellOff className="h-4 w-4 text-gray-500" />
                    }
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-sm font-medium">Alert Thresholds</h3>

                  <div className="grid gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="error-rate">Error Rate Threshold (%)</Label>
                      <div className="flex items-center gap-2">
                        <input
                          id="error-rate"
                          type="range"
                          min="1"
                          max="50"
                          value={alertSettings.errorRateThreshold}
                          onChange={(e) => setAlertSettings(prev => ({
                            ...prev,
                            errorRateThreshold: parseInt(e.target.value)
                          }))}
                          className="w-full"
                        />
                        <span className="w-12 text-center">{alertSettings.errorRateThreshold}%</span>
                      </div>
                      <div className="text-xs text-gray-500">
                        Alert when error rate exceeds this percentage
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="traffic-spike">Traffic Spike Threshold (%)</Label>
                      <div className="flex items-center gap-2">
                        <input
                          id="traffic-spike"
                          type="range"
                          min="100"
                          max="1000"
                          step="50"
                          value={alertSettings.trafficSpikeThreshold}
                          onChange={(e) => setAlertSettings(prev => ({
                            ...prev,
                            trafficSpikeThreshold: parseInt(e.target.value)
                          }))}
                          className="w-full"
                        />
                        <span className="w-12 text-center">{alertSettings.trafficSpikeThreshold}%</span>
                      </div>
                      <div className="text-xs text-gray-500">
                        Alert when traffic increases by this percentage compared to average
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="rate-limit">Rate Limit Threshold (%)</Label>
                      <div className="flex items-center gap-2">
                        <input
                          id="rate-limit"
                          type="range"
                          min="50"
                          max="100"
                          value={alertSettings.rateLimitThreshold}
                          onChange={(e) => setAlertSettings(prev => ({
                            ...prev,
                            rateLimitThreshold: parseInt(e.target.value)
                          }))}
                          className="w-full"
                        />
                        <span className="w-12 text-center">{alertSettings.rateLimitThreshold}%</span>
                      </div>
                      <div className="text-xs text-gray-500">
                        Alert when rate limit usage exceeds this percentage
                      </div>
                    </div>
                  </div>
                </div>

                <div className="pt-4">
                  <Button
                    onClick={() => {
                      // In a real implementation, this would save the settings to the server
                      toast.success('Alert settings saved');
                    }}
                  >
                    Save Settings
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
