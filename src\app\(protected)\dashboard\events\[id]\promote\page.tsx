'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Skeleton } from '@/components/ui/skeleton';
import PromoteEventForm from '@/components/featured/PromoteEventForm';
import { Card, CardContent } from '@/components/ui/card';
import { AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import ReferralLinkGenerator from '@/components/promotion/ReferralLinkGenerator';
import CrossPromotionManager from '@/components/promotion/CrossPromotionManager';
import PromotionStrategyCreator from '@/components/promotion/PromotionStrategyCreator';

export default function PromoteEventPage() {
  const params = useParams();
  const router = useRouter();
  const eventId = params.id as string;

  const [event, setEvent] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEvent = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/events/${eventId}`);

        if (!response.ok) {
          throw new Error('Failed to fetch event details');
        }

        const data = await response.json();
        setEvent(data);
      } catch (err) {
        console.error('Error fetching event:', err);
        setError('Unable to load event details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchEvent();
  }, [eventId]);

  const handleSuccess = () => {
    router.push(`/dashboard/events/${eventId}`);
  };

  return (
    <div className="container mx-auto py-4">
      <div className="mb-8">
        <h1 className="text-2xl font-bold mb-2">Promote Event</h1>
        <p className="text-gray-600">
          Increase visibility and attract more attendees by promoting your event
        </p>
      </div>

      {loading && (
        <Card>
          <CardContent className="p-6">
            <Skeleton className="h-8 w-3/4 mb-4" />
            <Skeleton className="h-4 w-1/2 mb-8" />
            <div className="space-y-6">
              <div>
                <Skeleton className="h-4 w-1/4 mb-2" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div>
                <Skeleton className="h-4 w-1/4 mb-2" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div>
                <Skeleton className="h-4 w-1/4 mb-2" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div>
                <Skeleton className="h-4 w-1/4 mb-2" />
                <Skeleton className="h-24 w-full" />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {!loading && !error && event && (
        <Tabs defaultValue="featured" className="space-y-6">
          <TabsList className="grid grid-cols-4 w-full max-w-2xl">
            <TabsTrigger value="featured">Featured</TabsTrigger>
            <TabsTrigger value="strategy">Strategy</TabsTrigger>
            <TabsTrigger value="referral">Referrals</TabsTrigger>
            <TabsTrigger value="cross-promote">Cross-Promote</TabsTrigger>
          </TabsList>

          <TabsContent value="featured">
            <PromoteEventForm
              eventId={eventId}
              eventTitle={event.title}
              onSuccess={handleSuccess}
            />
          </TabsContent>

          <TabsContent value="strategy">
            <PromotionStrategyCreator eventId={eventId} eventTitle={event.title} />
          </TabsContent>

          <TabsContent value="referral">
            <ReferralLinkGenerator eventId={eventId} eventTitle={event.title} />
          </TabsContent>

          <TabsContent value="cross-promote">
            <CrossPromotionManager currentEventId={eventId} currentEventTitle={event.title} />
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
