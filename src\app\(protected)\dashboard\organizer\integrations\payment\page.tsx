'use client';

import { useState } from 'react';

// Define types for payment gateways
type PaymentGateway = {
  id: string;
  name: string;
  type: string;
  isConnected: boolean;
  isDefault: boolean;
  status: string;
  lastUsed: string | null;
  accountId: string | null;
  logo: string;
};

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertCircle, Check, Copy, CreditCard, DollarSign, Eye, EyeOff, Info, Plus, Settings, Trash2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { RoleGate } from '@/components/auth/role-gate';
import Link from 'next/link';
import Image from 'next/image';

// Mock payment gateway data
const mockPaymentGateways = [
  {
    id: '1',
    name: 'Stripe',
    type: 'stripe',
    isConnected: true,
    isDefault: true,
    status: 'active',
    lastUsed: '2023-10-20T14:22:10Z',
    accountId: 'acct_**********',
    logo: '/images/stripe-logo.png'
  },
  {
    id: '2',
    name: 'PayPal',
    type: 'paypal',
    isConnected: true,
    isDefault: false,
    status: 'active',
    lastUsed: '2023-10-15T09:30:45Z',
    accountId: 'merchant.**********',
    logo: '/images/paypal-logo.png'
  },
  {
    id: '3',
    name: 'Mobile Money',
    type: 'mobilemoney',
    isConnected: false,
    isDefault: false,
    status: 'inactive',
    lastUsed: null,
    accountId: null,
    logo: '/images/mobile-money-logo.png'
  },
  {
    id: '4',
    name: 'Square',
    type: 'square',
    isConnected: false,
    isDefault: false,
    status: 'inactive',
    lastUsed: null,
    accountId: null,
    logo: '/images/square-logo.png'
  }
];

export default function PaymentGatewaysPage() {
  const [paymentGateways, setPaymentGateways] = useState<PaymentGateway[]>(mockPaymentGateways);
  const [showSecretKey, setShowSecretKey] = useState(false);
  const [stripeForm, setStripeForm] = useState({
    publishableKey: '',
    secretKey: ''
  });
  const [paypalForm, setPaypalForm] = useState({
    clientId: '',
    clientSecret: ''
  });
  // Mobile Money state is now handled in the mobile-money.tsx component

  const handleConnectStripe = () => {
    if (!stripeForm.publishableKey || !stripeForm.secretKey) {
      return;
    }

    // Update Stripe connection status
    setPaymentGateways(paymentGateways.map(gateway =>
      gateway.type === 'stripe'
        ? {
            ...gateway,
            isConnected: true,
            status: 'active',
            accountId: 'acct_' + Math.random().toString(36).substring(2, 12)
          }
        : gateway
    ));

    // Reset form
    setStripeForm({ publishableKey: '', secretKey: '' });
    setShowSecretKey(false);
  };

  const handleConnectPayPal = () => {
    if (!paypalForm.clientId || !paypalForm.clientSecret) {
      return;
    }

    // Update PayPal connection status
    setPaymentGateways(paymentGateways.map(gateway =>
      gateway.type === 'paypal'
        ? {
            ...gateway,
            isConnected: true,
            status: 'active',
            accountId: 'merchant.' + Math.random().toString(36).substring(2, 12)
          }
        : gateway
    ));

    // Reset form
    setPaypalForm({ clientId: '', clientSecret: '' });
  };

  // Mobile Money connection is now handled in the mobile-money.tsx component

  const handleDisconnect = (gatewayId: string) => {
    setPaymentGateways(paymentGateways.map(gateway =>
      gateway.id === gatewayId
        ? { ...gateway, isConnected: false, status: 'inactive', isDefault: false }
        : gateway
    ));

    // If the default gateway was disconnected, set another one as default
    if (paymentGateways.find(g => g.id === gatewayId)?.isDefault) {
      const connectedGateway = paymentGateways.find(g => g.id !== gatewayId && g.isConnected);
      if (connectedGateway) {
        setPaymentGateways(prev => prev.map(gateway =>
          gateway.id === connectedGateway.id
            ? { ...gateway, isDefault: true }
            : gateway
        ));
      }
    }
  };

  const setAsDefault = (gatewayId: string) => {
    setPaymentGateways(paymentGateways.map(gateway =>
      gateway.isConnected
        ? { ...gateway, isDefault: gateway.id === gatewayId }
        : gateway
    ));
  };

  return (
    <RoleGate allowedRole="ORGANIZER">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Payment Gateways</h1>
          <p className="text-gray-500 mt-1">Connect payment providers to accept payments for your events</p>
        </div>

        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Payment Processing</AlertTitle>
          <AlertDescription>
            Connect at least one payment gateway to process payments for your events.
            All payment data is securely handled by the payment provider.
          </AlertDescription>
        </Alert>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {paymentGateways.map(gateway => (
            <Card key={gateway.id} className={gateway.isDefault ? 'border-blue-500' : ''}>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div className="flex items-center">
                    <div className="w-10 h-10 mr-3 relative">
                      <div className="w-10 h-10 bg-gray-100 rounded-md flex items-center justify-center">
                        <CreditCard className="h-6 w-6 text-gray-500" />
                      </div>
                    </div>
                    <div>
                      <CardTitle className="text-lg">{gateway.name}</CardTitle>
                      {gateway.isConnected && (
                        <CardDescription className="mt-1">
                          {gateway.accountId}
                        </CardDescription>
                      )}
                    </div>
                  </div>
                  <Badge variant={gateway.status === 'active' ? "default" : "outline"} className={gateway.status === 'active' ? "bg-green-100 text-green-800" : ""}>
                    {gateway.status === 'active' ? "Connected" : "Not Connected"}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                {gateway.isConnected ? (
                  <div className="text-sm space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-500">Status</span>
                      <span className="font-medium flex items-center">
                        <Check className="h-4 w-4 text-green-500 mr-1" />
                        Active
                      </span>
                    </div>
                    {gateway.lastUsed && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Last Used</span>
                        <span>{new Date(gateway.lastUsed).toLocaleDateString()}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-gray-500">Default</span>
                      <span>{gateway.isDefault ? 'Yes' : 'No'}</span>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-2">
                    <p className="text-gray-500 text-sm mb-2">Not connected</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full"
                      onClick={() => (document.querySelector(`[data-value="${gateway.type}"]`) as HTMLElement)?.click()}
                    >
                      Connect {gateway.name}
                    </Button>
                  </div>
                )}
              </CardContent>
              {gateway.isConnected && (
                <CardFooter className="flex justify-between border-t pt-4">
                  {!gateway.isDefault ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setAsDefault(gateway.id)}
                    >
                      Set as Default
                    </Button>
                  ) : (
                    <Badge variant="outline" className="bg-blue-50">Default Gateway</Badge>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-red-500 hover:text-red-700 hover:bg-red-50"
                    onClick={() => handleDisconnect(gateway.id)}
                  >
                    Disconnect
                  </Button>
                </CardFooter>
              )}
            </Card>
          ))}
        </div>

        <Tabs defaultValue="stripe">
          <TabsList>
            <TabsTrigger value="stripe">Stripe</TabsTrigger>
            <TabsTrigger value="paypal">PayPal</TabsTrigger>
            <TabsTrigger value="mobilemoney">Mobile Money</TabsTrigger>
            <TabsTrigger value="square">Square</TabsTrigger>
          </TabsList>

          <TabsContent value="stripe">
            <Card>
              <CardHeader>
                <CardTitle>Connect Stripe</CardTitle>
                <CardDescription>
                  Accept credit card payments directly on your event pages
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="stripe-publishable-key">Publishable Key</Label>
                    <Input
                      id="stripe-publishable-key"
                      placeholder="pk_test_..."
                      value={stripeForm.publishableKey}
                      onChange={(e) => setStripeForm({...stripeForm, publishableKey: e.target.value})}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="stripe-secret-key">Secret Key</Label>
                    <div className="relative">
                      <Input
                        id="stripe-secret-key"
                        type={showSecretKey ? "text" : "password"}
                        placeholder="sk_test_..."
                        value={stripeForm.secretKey}
                        onChange={(e) => setStripeForm({...stripeForm, secretKey: e.target.value})}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => setShowSecretKey(!showSecretKey)}
                      >
                        {showSecretKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                    <p className="text-xs text-gray-500">
                      Your API keys are stored securely and never shared with third parties
                    </p>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between border-t pt-4">
                <Button variant="outline" onClick={() => window.open('https://dashboard.stripe.com/apikeys', '_blank')}>
                  Get API Keys
                </Button>
                <Button onClick={handleConnectStripe}>
                  Connect Stripe
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="paypal">
            <Card>
              <CardHeader>
                <CardTitle>Connect PayPal</CardTitle>
                <CardDescription>
                  Allow customers to pay with PayPal or PayPal Credit
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="paypal-client-id">Client ID</Label>
                    <Input
                      id="paypal-client-id"
                      placeholder="Client ID from PayPal Developer Dashboard"
                      value={paypalForm.clientId}
                      onChange={(e) => setPaypalForm({...paypalForm, clientId: e.target.value})}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="paypal-client-secret">Client Secret</Label>
                    <Input
                      id="paypal-client-secret"
                      type="password"
                      placeholder="Client Secret from PayPal Developer Dashboard"
                      value={paypalForm.clientSecret}
                      onChange={(e) => setPaypalForm({...paypalForm, clientSecret: e.target.value})}
                    />
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between border-t pt-4">
                <Button variant="outline" onClick={() => window.open('https://developer.paypal.com/dashboard/', '_blank')}>
                  PayPal Developer Dashboard
                </Button>
                <Button onClick={handleConnectPayPal}>
                  Connect PayPal
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="mobilemoney">
            {/* Import the Mobile Money component */}
            <div className="mobile-money-container">
              <h3 className="text-lg font-semibold mb-4">Mobile Money Integration</h3>
              <p className="mb-4">Connect to popular mobile money services like M-Pesa, MTN Mobile Money, Airtel Money and more.</p>

              <Card>
                <CardHeader>
                  <CardTitle>Connect Mobile Money</CardTitle>
                  <CardDescription>
                    Accept payments via mobile money services
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="provider">Select Provider</Label>
                      <Select defaultValue="mpesa">
                        <SelectTrigger id="provider">
                          <SelectValue placeholder="Select provider" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="mpesa">M-Pesa</SelectItem>
                          <SelectItem value="mtn">MTN Mobile Money</SelectItem>
                          <SelectItem value="airtel">Airtel Money</SelectItem>
                          <SelectItem value="orange">Orange Money</SelectItem>
                          <SelectItem value="vodacom">Vodacom M-Pesa</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="api-key">API Key</Label>
                      <Input id="api-key" placeholder="Enter your API key" />
                    </div>

                    <div>
                      <Label htmlFor="shortcode">Business Shortcode/Paybill</Label>
                      <Input id="shortcode" placeholder="Enter your business shortcode" />
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Link href="/dashboard/organizer/integrations/payment/mobile-money">
                    <Button className="w-full">Configure Mobile Money</Button>
                  </Link>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="square">
            <Card>
              <CardHeader>
                <CardTitle>Connect Square</CardTitle>
                <CardDescription>
                  Accept payments with Square&apos;s payment processing
                </CardDescription>
              </CardHeader>
              <CardContent className="py-10 text-center">
                <div className="flex flex-col items-center justify-center">
                  <div className="bg-gray-100 p-4 rounded-full mb-4">
                    <Settings className="h-8 w-8 text-gray-500" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">Coming Soon</h3>
                  <p className="text-gray-500 max-w-md mx-auto mb-6">
                    Square integration is currently in development and will be available soon.
                    Check back later for updates.
                  </p>
                  <Button variant="outline" disabled>
                    Connect Square
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </RoleGate>
  );
}
