'use client';

import React, { useState, useEffect } from 'react';
import { RoleGate } from '@/components/auth/role-gate';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ShoppingBag, 
  Search, 
  Clock, 
  CheckCircle, 
  XCircle, 
  Calendar, 
  Ticket, 
  Download, 
  ExternalLink 
} from 'lucide-react';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { Skeleton } from '@/components/ui/skeleton';

// Types
interface Order {
  id: string;
  event: {
    id: string;
    title: string;
  };
  createdAt: string;
  status: string;
  totalAmount: number;
  tickets: {
    id: string;
    type: string;
    quantity: number;
    price: number;
  }[];
}

export default function UserOrdersPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Fetch user orders
    const fetchOrders = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/user/orders');
        
        if (!response.ok) {
          throw new Error('Failed to fetch orders');
        }
        
        const data = await response.json();
        setOrders(data);
      } catch (error) {
        console.error('Error fetching orders:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrders();
  }, []);

  // Filter orders based on search term and active tab
  const filteredOrders = orders.filter(order => {
    const matchesSearch = 
      order.event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.id.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (activeTab === 'all') return matchesSearch;
    return matchesSearch && order.status.toLowerCase() === activeTab;
  });

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'paid':
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-100 text-red-800">Cancelled</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  return (
    <RoleGate allowedRole="USER">
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold mb-2">My Orders</h1>
            <p className="text-gray-500 dark:text-gray-400">
              View and manage your event ticket orders
            </p>
          </div>
          <Button className="mt-4 md:mt-0" asChild>
            <Link href="/events">
              <Calendar className="mr-2 h-4 w-4" />
              Browse Events
            </Link>
          </Button>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <Input
              placeholder="Search orders..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <Tabs defaultValue="all" className="space-y-4" onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="all" className="flex gap-2">
              <ShoppingBag className="h-4 w-4" /> All Orders
            </TabsTrigger>
            <TabsTrigger value="pending" className="flex gap-2">
              <Clock className="h-4 w-4" /> Pending
            </TabsTrigger>
            <TabsTrigger value="completed" className="flex gap-2">
              <CheckCircle className="h-4 w-4" /> Completed
            </TabsTrigger>
            <TabsTrigger value="cancelled" className="flex gap-2">
              <XCircle className="h-4 w-4" /> Cancelled
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="space-y-6">
            {isLoading ? (
              // Loading state
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <Card key={i}>
                    <CardHeader className="pb-2">
                      <Skeleton className="h-6 w-3/4" />
                      <Skeleton className="h-4 w-1/2 mt-2" />
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex justify-between">
                          <Skeleton className="h-4 w-1/4" />
                          <Skeleton className="h-4 w-1/4" />
                        </div>
                        <div className="flex justify-between">
                          <Skeleton className="h-4 w-1/3" />
                          <Skeleton className="h-4 w-1/5" />
                        </div>
                        <Skeleton className="h-10 w-full" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : filteredOrders.length === 0 ? (
              // Empty state
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                    <ShoppingBag className="h-8 w-8 text-gray-500" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">No orders found</h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">
                    {searchTerm 
                      ? "No orders match your search criteria." 
                      : "You haven't placed any orders yet. Browse events to purchase tickets."}
                  </p>
                  <Button asChild>
                    <Link href="/events">Browse Events</Link>
                  </Button>
                </CardContent>
              </Card>
            ) : (
              // Orders list
              <div className="grid grid-cols-1 gap-6">
                {filteredOrders.map((order) => (
                  <Card key={order.id}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-xl">{order.event.title}</CardTitle>
                          <CardDescription>
                            Order #{order.id.substring(0, 8)} • {format(new Date(order.createdAt), 'PPP')}
                          </CardDescription>
                        </div>
                        {getStatusBadge(order.status)}
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h4 className="text-sm font-medium text-gray-500 mb-1">Tickets</h4>
                            {order.tickets.map((ticket, idx) => (
                              <div key={idx} className="flex justify-between text-sm">
                                <span>{ticket.type} × {ticket.quantity}</span>
                                <span>{formatCurrency(ticket.price * ticket.quantity)}</span>
                              </div>
                            ))}
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-gray-500 mb-1">Order Summary</h4>
                            <div className="flex justify-between font-medium">
                              <span>Total</span>
                              <span>{formatCurrency(order.totalAmount)}</span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex flex-wrap gap-2 pt-4 border-t">
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/dashboard/user/orders/${order.id}`}>
                              <ShoppingBag className="h-4 w-4 mr-2" />
                              Order Details
                            </Link>
                          </Button>
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/dashboard/user/tickets?orderId=${order.id}`}>
                              <Ticket className="h-4 w-4 mr-2" />
                              View Tickets
                            </Link>
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-2" />
                            Download Receipt
                          </Button>
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/events/${order.event.id}`} target="_blank">
                              <ExternalLink className="h-4 w-4 mr-2" />
                              Event Page
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </RoleGate>
  );
}
