'use server';

import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { validatePhoneNumber } from '@/lib/validation/phone-validation';
import fs from 'fs/promises';
import crypto from 'crypto';
import { revalidatePath } from 'next/cache';
import { sendVerificationSubmissionEmail, sendAdminVerificationNotification } from '@/lib/mail';

export async function directVerification(formData: FormData) {
  try {
    // Get current user
    const user = await currentUser();
    if (!user?.id || !user?.email) {
      return { error: 'Unauthorized. Please log in.' };
    }

    // Extract form data
    const businessName = formData.get('businessName') as string;
    const businessType = formData.get('businessType') as string;
    const registrationNumber = formData.get('registrationNumber') as string;
    const taxPayerIdNumber = formData.get('taxPayerIdNumber') as string;
    const phoneNumber = formData.get('phoneNumber') as string;
    const alternativeEmail = formData.get('alternativeEmail') as string;
    const website = formData.get('website') as string;
    const physicalAddress = formData.get('physicalAddress') as string;
    const city = formData.get('city') as string;
    const province = formData.get('province') as string;
    const postalCode = formData.get('postalCode') as string;
    const eventTypes = formData.get('eventTypes') as string;
    const experience = formData.get('experience') as string;
    const previousEvents = formData.get('previousEvents') as string;
    const idDocumentType = formData.get('idDocumentType') as string;

    // Basic validation
    if (!businessName || !businessType || !taxPayerIdNumber || !phoneNumber ||
        !physicalAddress || !city || !province || !eventTypes || !experience) {
      return { error: 'Please fill in all required fields.' };
    }

    // Validate phone number
    const phoneValidation = validatePhoneNumber(phoneNumber);
    if (!phoneValidation.isValid) {
      return { error: phoneValidation.error || 'Invalid phone number. Please include country code (e.g. +1, +44, +260)' };
    }

    // Handle file uploads
    const idDocument = formData.get('idDocument') as File;
    const businessLicense = formData.get('businessLicense') as File;
    const taxCertificate = formData.get('taxCertificate') as File;

    if (!idDocument) {
      return { error: 'ID document is required.' };
    }

    if (!taxCertificate) {
      return { error: 'Tax certificate is required.' };
    }

    // Process file uploads
    let idDocumentPath: string | undefined;
    let businessLicensePath: string | undefined;
    let taxCertificatePath: string | undefined;

    // Ensure directory exists
    try {
      await fs.mkdir('./public/uploads/verification', { recursive: true });
    } catch (error) {
      console.error('Error creating directory:', error);
      // Continue with the process even if directory creation fails
    }

    // Process ID document
    if (idDocument && idDocument.size > 0) {
      try {
        const bytes = await idDocument.arrayBuffer();
        const buffer = Buffer.from(bytes);
        const filename = `${crypto.randomUUID()}-${idDocument.name}`;
        idDocumentPath = `/uploads/verification/${filename}`;
        await fs.writeFile(`./public${idDocumentPath}`, buffer);
      } catch (error) {
        console.error('Error processing ID document:', error);
        idDocumentPath = undefined;
      }
    }

    // Process business license
    if (businessLicense && businessLicense.size > 0) {
      try {
        const bytes = await businessLicense.arrayBuffer();
        const buffer = Buffer.from(bytes);
        const filename = `${crypto.randomUUID()}-${businessLicense.name}`;
        businessLicensePath = `/uploads/verification/${filename}`;
        await fs.writeFile(`./public${businessLicensePath}`, buffer);
      } catch (error) {
        console.error('Error processing business license:', error);
        businessLicensePath = undefined;
      }
    }

    // Process tax certificate
    if (taxCertificate && taxCertificate.size > 0) {
      try {
        const bytes = await taxCertificate.arrayBuffer();
        const buffer = Buffer.from(bytes);
        const filename = `${crypto.randomUUID()}-${taxCertificate.name}`;
        taxCertificatePath = `/uploads/verification/${filename}`;
        await fs.writeFile(`./public${taxCertificatePath}`, buffer);
      } catch (error) {
        console.error('Error processing tax certificate:', error);
        taxCertificatePath = undefined;
      }
    }

    // Check if user exists in the database
    const dbUser = await db.user.findUnique({
      where: { id: user.id },
    });

    if (!dbUser) {
      // User doesn't exist in the database, create it
      try {
        // Generate a random access token
        const accessToken = crypto.randomBytes(32).toString('hex');

        await db.user.create({
          data: {
            id: user.id,
            name: user.name || 'User',
            email: user.email,
            role: 'ORGANIZER',
            accessToken: accessToken,
          },
        });
        console.log('Created missing user in database');
      } catch (userCreateError) {
        console.error('Error creating user:', userCreateError);
        return { error: 'Failed to create user record. Please contact support.' };
      }
    } else if (!dbUser.accessToken) {
      // User exists but doesn't have an access token
      try {
        // Generate a random access token
        const accessToken = crypto.randomBytes(32).toString('hex');

        await db.user.update({
          where: { id: user.id },
          data: { accessToken: accessToken },
        });
        console.log('Added missing access token to user');
      } catch (updateError) {
        console.error('Error updating user access token:', updateError);
        // Continue anyway, this is not critical
      }
    }

    // Check if user is already verified or has a pending verification
    const existingVerification = await db.organizerVerification.findUnique({
      where: { userId: user.id },
    });

    // Create or update verification record
    try {
      if (existingVerification) {
        // Update existing record if it was rejected before
        await db.organizerVerification.update({
          where: { id: existingVerification.id },
          data: {
            businessName,
            businessType,
            registrationNumber,
            taxPayerIdNumber,
            phoneNumber,
            alternativeEmail,
            website,
            physicalAddress,
            city,
            province,
            postalCode,
            idDocumentPath,
            idDocumentType,
            businessLicensePath,
            taxCertificatePath,
            eventTypes,
            experience,
            previousEvents,
            status: 'PENDING',
            rejectionReason: null,
            updatedAt: new Date(),
          },
        });
      } else {
        // Create new verification record
        await db.organizerVerification.create({
          data: {
            userId: user.id,
            businessName,
            businessType,
            registrationNumber,
            taxPayerIdNumber,
            phoneNumber,
            alternativeEmail,
            website,
            physicalAddress,
            city,
            province,
            postalCode,
            idDocumentPath,
            idDocumentType,
            businessLicensePath,
            taxCertificatePath,
            eventTypes,
            experience,
            previousEvents,
            status: 'PENDING',
          },
        });
      }
    } catch (verificationError) {
      console.error('Error creating/updating verification:', verificationError);
      return { error: 'Failed to save verification data. Please try again later.' };
    }

    // Send confirmation email to user
    try {
      await sendVerificationSubmissionEmail(user.email, businessName);
    } catch (emailError) {
      console.error('Error sending verification email:', emailError);
      // Continue even if email fails
    }

    // Send notification to admin
    try {
      await sendAdminVerificationNotification(businessName, user.email);
    } catch (adminEmailError) {
      console.error('Error sending admin notification:', adminEmailError);
      // Continue even if email fails
    }

    // Revalidate the path to update UI
    revalidatePath('/dashboard/organizer/profile');

    return { success: 'Verification submitted successfully!' };
  } catch (error) {
    console.error('Error submitting organizer verification:', error);

    // Check for specific Prisma errors
    if (error && typeof error === 'object' && 'code' in error) {
      const prismaError = error as { code: string, meta?: { field_name?: string } };

      // Foreign key constraint violation
      if (prismaError.code === 'P2003' && prismaError.meta?.field_name?.includes('userId')) {
        return {
          error: 'User account not properly configured. Please contact support.'
        };
      }
    }

    return { error: 'An unexpected error occurred. Please try again.' };
  }
}
