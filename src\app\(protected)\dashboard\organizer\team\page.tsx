'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import { RoleGate } from '@/components/auth/role-gate';
import { Users, UserPlus, Building, Calendar, Plus, Settings, Trash2, Edit, Mail } from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';

// Team interface
interface Team {
  id: string;
  name: string;
  description?: string;
  ownerId: string;
  createdAt: string;
  updatedAt: string;
  owner?: {
    id: string;
    name: string;
    email: string;
    image: string;
  };
  _count: {
    members: number;
    events: number;
  };
}

// Team member interface
interface TeamMember {
  id: string;
  teamId: string;
  userId: string;
  role: string;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    image: string;
  };
}

export default function TeamDashboardPage() {
  const router = useRouter();
  const [ownedTeams, setOwnedTeams] = useState<Team[]>([]);
  const [memberTeams, setMemberTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newTeamName, setNewTeamName] = useState('');
  const [newTeamDescription, setNewTeamDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch teams
  useEffect(() => {
    const fetchTeams = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/teams');

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json();
        console.log('API response:', data);
        setOwnedTeams(data.ownedTeams || []);
        setMemberTeams(data.memberTeams || []);
      } catch (error) {
        console.error('Error fetching teams:', error);
        toast({
          title: 'Error',
          description: 'Failed to load teams. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchTeams();
  }, []);

  // Create a new team
  const handleCreateTeam = async () => {
    if (!newTeamName.trim()) {
      toast({
        title: 'Error',
        description: 'Team name is required',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const response = await fetch('/api/teams', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newTeamName,
          description: newTeamDescription,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }

      const data = await response.json();

      toast({
        title: 'Success',
        description: 'Team created successfully',
      });

      // Add the new team to the list
      setOwnedTeams([data.team, ...ownedTeams]);

      // Reset form and close dialog
      setNewTeamName('');
      setNewTeamDescription('');
      setIsCreateDialogOpen(false);

      // Navigate to the new team page
      router.push(`/dashboard/organizer/team/${data.team.id}`);
    } catch (error) {
      console.error('Error creating team:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create team',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'PPP');
  };

  return (
    <RoleGate allowedRole={['ORGANIZER', 'ADMIN', 'SUPERADMIN']}>
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold">Team Management</h1>
            <p className="text-gray-500 mt-1">
              Create and manage teams to collaborate on events
            </p>
          </div>
          <div className="mt-4 md:mt-0">
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Team
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create a New Team</DialogTitle>
                  <DialogDescription>
                    Create a team to collaborate with other organizers on events
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="team-name">Team Name</Label>
                    <Input
                      id="team-name"
                      placeholder="Enter team name"
                      value={newTeamName}
                      onChange={(e) => setNewTeamName(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="team-description">Description (Optional)</Label>
                    <Input
                      id="team-description"
                      placeholder="Enter team description"
                      value={newTeamDescription}
                      onChange={(e) => setNewTeamDescription(e.target.value)}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsCreateDialogOpen(false)}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button onClick={handleCreateTeam} disabled={isSubmitting}>
                    {isSubmitting ? 'Creating...' : 'Create Team'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <Tabs defaultValue="owned">
          <TabsList className="mb-6">
            <TabsTrigger value="owned">Teams You Own</TabsTrigger>
            <TabsTrigger value="member">Teams You're In</TabsTrigger>
          </TabsList>

          <TabsContent value="owned">
            {loading ? (
              <div className="flex justify-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : ownedTeams.length === 0 ? (
              <Card className="text-center py-12">
                <CardContent>
                  <Building className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                  <h3 className="text-xl font-medium mb-2">No Teams Yet</h3>
                  <p className="text-gray-500 mb-6 max-w-md mx-auto">
                    You haven't created any teams yet. Create a team to collaborate with other organizers on events.
                  </p>
                  <Button onClick={() => setIsCreateDialogOpen(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Your First Team
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {ownedTeams.map((team) => (
                  <Card key={team.id} className="overflow-hidden">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-xl">{team.name}</CardTitle>
                      <CardDescription>
                        Created on {formatDate(team.createdAt)}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="pb-3">
                      <div className="space-y-3">
                        <div className="flex items-center text-sm">
                          <Users className="h-4 w-4 mr-2 text-gray-400" />
                          <span>{team._count.members} members</span>
                        </div>
                        <div className="flex items-center text-sm">
                          <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                          <span>{team._count.events} events</span>
                        </div>
                        {team.description && (
                          <p className="text-sm text-gray-500 mt-2">{team.description}</p>
                        )}
                      </div>
                    </CardContent>
                    <Separator />
                    <CardFooter className="pt-4">
                      <div className="flex justify-between w-full">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/dashboard/organizer/team/${team.id}`}>
                            Manage
                          </Link>
                        </Button>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/dashboard/organizer/team/${team.id}/members`}>
                              <UserPlus className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/dashboard/organizer/team/${team.id}/settings`}>
                              <Settings className="h-4 w-4" />
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="member">
            {loading ? (
              <div className="flex justify-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : memberTeams.length === 0 ? (
              <Card className="text-center py-12">
                <CardContent>
                  <Users className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                  <h3 className="text-xl font-medium mb-2">No Team Memberships</h3>
                  <p className="text-gray-500 mb-6 max-w-md mx-auto">
                    You haven't been added to any teams yet. When someone adds you to their team, it will appear here.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {memberTeams.map((team) => (
                  <Card key={team.id} className="overflow-hidden">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-xl">{team.name}</CardTitle>
                      <CardDescription>
                        Owned by {team.owner?.name || 'Unknown'}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="pb-3">
                      <div className="space-y-3">
                        <div className="flex items-center text-sm">
                          <Users className="h-4 w-4 mr-2 text-gray-400" />
                          <span>{team._count.members} members</span>
                        </div>
                        <div className="flex items-center text-sm">
                          <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                          <span>{team._count.events} events</span>
                        </div>
                        {team.description && (
                          <p className="text-sm text-gray-500 mt-2">{team.description}</p>
                        )}
                      </div>
                    </CardContent>
                    <Separator />
                    <CardFooter className="pt-4">
                      <Button variant="outline" size="sm" asChild className="w-full">
                        <Link href={`/dashboard/organizer/team/${team.id}`}>
                          View Team
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </RoleGate>
  );
}
