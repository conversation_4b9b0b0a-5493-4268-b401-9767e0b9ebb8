/**
 * API Permissions Testing Script
 * 
 * This script tests API keys with different permissions against various endpoints.
 * It verifies that permission checks are enforced correctly.
 * 
 * Usage:
 * node test-permissions.js <api-key>
 * 
 * Example:
 * node test-permissions.js your-api-key
 */

const fetch = require('node-fetch');

// Get command line arguments
const apiKey = process.argv[2];

if (!apiKey) {
  console.error('Please provide an API key as the first argument');
  process.exit(1);
}

// Define endpoints to test
const endpoints = [
  { url: 'http://localhost:3000/api/test', description: 'Test API (Public)' },
  { url: 'http://localhost:3000/api/events/published', description: 'Published Events (Public)' },
  { url: 'http://localhost:3000/api/external/events', description: 'External Events API (Requires read:events)' },
  { url: 'http://localhost:3000/api/events', description: 'Events API (Requires read:events)' },
  { url: 'http://localhost:3000/api/events/create', description: 'Create Event (Requires write:events)' },
  { url: 'http://localhost:3000/api/tickets', description: 'Tickets API (Requires read:tickets)' },
  { url: 'http://localhost:3000/api/orders', description: 'Orders API (Requires read:orders)' },
  { url: 'http://localhost:3000/api/analytics', description: 'Analytics API (Requires read:analytics)' },
];

console.log(`Testing API key: ${apiKey}`);
console.log('---------------------------------------------------');

// Function to make a request with the API key
async function testEndpoint(endpoint) {
  try {
    console.log(`Testing: ${endpoint.description}`);
    console.log(`URL: ${endpoint.url}`);
    
    const response = await fetch(endpoint.url, {
      headers: {
        'X-API-Key': apiKey
      }
    });
    
    const status = response.status;
    let data;
    
    try {
      data = await response.json();
    } catch (e) {
      data = await response.text();
    }
    
    console.log(`Status: ${status}`);
    
    if (status === 200) {
      console.log('Result: SUCCESS - Endpoint accessible');
    } else if (status === 401) {
      console.log('Result: UNAUTHORIZED - API key required');
    } else if (status === 403) {
      console.log('Result: FORBIDDEN - Insufficient permissions');
    } else {
      console.log(`Result: ERROR - Unexpected status code ${status}`);
    }
    
    console.log('---------------------------------------------------');
    
    return { endpoint: endpoint.url, status, success: status === 200 };
  } catch (error) {
    console.error(`Error testing endpoint ${endpoint.url}: ${error.message}`);
    console.log('---------------------------------------------------');
    return { endpoint: endpoint.url, error: error.message, success: false };
  }
}

// Main function to run the tests
async function runTests() {
  console.log('Starting permission tests...');
  
  const results = [];
  
  for (const endpoint of endpoints) {
    const result = await testEndpoint(endpoint);
    results.push(result);
  }
  
  console.log('Permission Test Results:');
  console.log('---------------------------------------------------');
  
  const accessibleEndpoints = results.filter(r => r.success).map(r => r.endpoint);
  const forbiddenEndpoints = results.filter(r => r.status === 403).map(r => r.endpoint);
  const unauthorizedEndpoints = results.filter(r => r.status === 401).map(r => r.endpoint);
  const errorEndpoints = results.filter(r => !r.success && r.status !== 403 && r.status !== 401).map(r => r.endpoint);
  
  console.log('Accessible Endpoints:');
  accessibleEndpoints.forEach(e => console.log(`  - ${e}`));
  
  console.log('\nForbidden Endpoints (Insufficient Permissions):');
  forbiddenEndpoints.forEach(e => console.log(`  - ${e}`));
  
  console.log('\nUnauthorized Endpoints (API Key Required):');
  unauthorizedEndpoints.forEach(e => console.log(`  - ${e}`));
  
  if (errorEndpoints.length > 0) {
    console.log('\nEndpoints with Errors:');
    errorEndpoints.forEach(e => console.log(`  - ${e}`));
  }
  
  console.log('---------------------------------------------------');
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
});
