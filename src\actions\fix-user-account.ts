'use server';

import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import crypto from 'crypto';

/**
 * Fixes the current user's account by ensuring it exists in the database
 * and has all required fields including an access token.
 */
export async function fixUserAccount() {
  try {
    // Get current user from session
    const user = await currentUser();
    if (!user?.id || !user?.email) {
      return { error: 'No authenticated user found' };
    }

    // Check if user exists in the database
    const dbUser = await db.user.findUnique({
      where: { id: user.id },
    });

    if (!dbUser) {
      // Check if a user with the same email already exists
      const existingUserByEmail = user.email ? await db.user.findUnique({
        where: { email: user.email },
      }) : null;

      // If a user with the same email exists, we need to handle this carefully
      if (existingUserByEmail) {
        // User exists with this email but different ID
        console.log(`Found user with same email but different ID: ${existingUserByEmail.id}`);

        try {
          // Update the existing user's ID to match the session ID
          await db.user.update({
            where: { id: existingUserByEmail.id },
            data: { id: user.id },
          });

          return {
            success: true,
            message: 'Updated existing user ID to match session',
            fixed: 'updated_user_id'
          };
        } catch (updateError) {
          console.error('Error updating user ID:', updateError);

          // If we can't update the ID, try to update the session user's email to make it unique
          try {
            // Generate a random access token
            const accessToken = crypto.randomBytes(32).toString('hex');
            // Make email unique by adding a timestamp
            const uniqueEmail = `${user.email}.${Date.now()}@session`;

            await db.user.create({
              data: {
                id: user.id,
                name: user.name || 'User',
                email: uniqueEmail,
                role: user.role || 'ORGANIZER',
                accessToken: accessToken,
              },
            });

            return {
              success: true,
              message: 'Created user with modified email to avoid conflict',
              fixed: 'created_user_modified_email'
            };
          } catch (createError) {
            console.error('Error creating user with modified email:', createError);
            return {
              error: 'Failed to resolve email conflict',
              details: createError instanceof Error ? createError.message : String(createError)
            };
          }
        }
      } else {
        // No user with this email exists, create a new user
        try {
          // Generate a random access token
          const accessToken = crypto.randomBytes(32).toString('hex');

          await db.user.create({
            data: {
              id: user.id,
              name: user.name || 'User',
              email: user.email || `user-${Date.now()}@example.com`, // Fallback email if none provided
              role: user.role || 'ORGANIZER',
              accessToken: accessToken,
            },
          });

          return {
            success: true,
            message: 'User account created successfully',
            fixed: 'created_user'
          };
        } catch (error) {
          console.error('Error creating user:', error);
          return {
            error: 'Failed to create user account',
            details: error instanceof Error ? error.message : String(error)
          };
        }
      }
    } else {
      // User exists, check if access token is missing
      if (!dbUser.accessToken) {
        try {
          // Generate a random access token
          const accessToken = crypto.randomBytes(32).toString('hex');

          await db.user.update({
            where: { id: user.id },
            data: { accessToken: accessToken },
          });

          return {
            success: true,
            message: 'Access token added to user account',
            fixed: 'added_token'
          };
        } catch (error) {
          console.error('Error updating user access token:', error);
          return {
            error: 'Failed to update user access token',
            details: error instanceof Error ? error.message : String(error)
          };
        }
      }

      // User exists and has an access token
      return {
        success: true,
        message: 'User account is already properly configured',
        fixed: 'none_needed'
      };
    }
  } catch (error) {
    console.error('Error fixing user account:', error);
    return {
      error: 'An unexpected error occurred',
      details: error instanceof Error ? error.message : String(error)
    };
  }
}
