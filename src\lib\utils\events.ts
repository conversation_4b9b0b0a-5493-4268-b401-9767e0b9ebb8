import { EventFormData } from '@/types/events';
import { format } from 'date-fns';

export const validateEventData = (data: EventFormData) => {
  const errors: Record<string, string> = {};

  // Basic validation
  if (!data.title?.trim()) errors.title = 'Title is required';
  if (!data.description?.trim()) errors.description = 'Description is required';
  if (!data.category) errors.category = 'Category is required';
  if (!data.eventType) errors.eventType = 'Event type is required';

  // Date and time validation
  const startDateTime = new Date(`${data.startDate}T${data.startTime}`);
  const endDateTime = new Date(`${data.endDate}T${data.endTime}`);
  
  if (isNaN(startDateTime.getTime())) errors.startDate = 'Invalid start date';
  if (isNaN(endDateTime.getTime())) errors.endDate = 'Invalid end date';
  if (endDateTime <= startDateTime) errors.endDate = 'End date must be after start date';

  // Calculate number of days
  const days = Math.ceil((endDateTime.getTime() - startDateTime.getTime()) / (1000 * 60 * 60 * 24));
  if (days < 1) errors.endDate = 'Event must be at least one day long';

  // Ticketing validation
  if (!data.isFree) {
    if (!data.regularPrice || data.regularPrice < 0) {
      errors.regularPrice = 'Regular price must be greater than or equal to 0';
    }
    if (data.vipPrice !== undefined && data.vipPrice < 0) {
      errors.vipPrice = 'VIP price must be greater than or equal to 0';
    }
  }

  if (!data.regularSeats || data.regularSeats < 1) {
    errors.regularSeats = 'Regular seats must be at least 1';
  }
  if (data.vipSeats !== undefined && data.vipSeats < 0) {
    errors.vipSeats = 'VIP seats must be greater than or equal to 0';
  }

  // Age restriction validation
  if (data.minAge !== undefined && data.maxAge !== undefined) {
    if (data.minAge < 0) errors.minAge = 'Minimum age cannot be negative';
    if (data.maxAge < data.minAge) errors.maxAge = 'Maximum age must be greater than minimum age';
  }

  // Parking validation
  if (data.hasParking && data.parkingDetails) {
    if (data.parkingDetails.totalSpaces < 1) {
      errors.parkingSpaces = 'Total parking spaces must be at least 1';
    }
    if (data.parkingDetails.reservedSpaces > data.parkingDetails.totalSpaces) {
      errors.reservedSpaces = 'Reserved spaces cannot exceed total spaces';
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    numberOfDays: days
  };
};

export const formatEventDates = (dateStart: Date, dateEnd: Date) => {
  return {
    dateStart: format(dateStart, 'yyyy-MM-dd'),
    timeStart: format(dateStart, 'HH:mm'),
    dateEnd: format(dateEnd, 'yyyy-MM-dd'),
    timeEnd: format(dateEnd, 'HH:mm'),
  };
};

export const generateEventSlug = (title: string) => {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)+/g, '');
};

// Category slug mapping for SEO-friendly URLs
export const categorySlugMap: Record<string, string> = {
  'WEDDING': 'weddings',
  'FUNERAL': 'funerals',
  'BUSINESS': 'business',
  'MUSIC': 'music',
  'LIFESTYLE_EVENTS': 'lifestyle',
  'EDUCATIONAL_EVENTS': 'education',
  'HOLIDAY_CELEBRATIONS': 'holidays',
  'FASHION_SHOWS': 'fashion',
  'HEALTH_AND_WELLNESS': 'health-wellness',
  'CULTURAL_FESTIVALS': 'cultural',
  'GAMING_EVENTS': 'gaming',
  'ENVIRONMENTAL_EVENTS': 'environmental',
  'TRADE_FAIR': 'trade-fairs',
  'AGRICULTURAL_AND_COMMECIAL_SHOW': 'agriculture',
  'WEB_DEVELOPMENT': 'web-development',
  'MARKETING': 'marketing',
  'TECHNOLOGY': 'technology',
  'CONCERTS_AND_CHURCH': 'concerts',
  'CONFERENCES_AND_WORKSHOPS': 'conferences',
  'SPORTS_AND_FITNESS': 'sports',
  'ARTS_AND_THEATER': 'arts',
  'FAMILY_AND_KIDS': 'family',
  'FOOD_AND_DRINK': 'food-drink',
  'CHARITY_AND_FUNDRAISERS': 'charity',
  'COMEDY_SHOWS': 'comedy',
  'NETWORKING_AND_SOCIAL_GATHERINGS': 'networking',
  'FILM_SCREENINGS': 'film'
};

// Reverse mapping for parsing URLs
export const slugCategoryMap: Record<string, string> = Object.fromEntries(
  Object.entries(categorySlugMap).map(([key, value]) => [value, key])
);

/**
 * Generate SEO-friendly URL for an event
 * @param event Event object with id, title, and category
 * @returns SEO-friendly URL path
 */
export const generateEventUrl = (event: {
  id: string;
  title: string;
  category: string;
}) => {
  const categorySlug = categorySlugMap[event.category] || 'events';
  const titleSlug = generateEventSlug(event.title);
  return `/events/${categorySlug}/${titleSlug}/${event.id}`;
};

/**
 * Parse SEO-friendly URL to extract event information
 * @param url URL path to parse
 * @returns Parsed URL components or null if invalid
 */
export const parseEventUrl = (url: string) => {
  // Handle both old format (/events/[id]) and new format (/events/[category]/[title]/[id])
  const parts = url.split('/').filter(Boolean);

  if (parts.length === 2 && parts[0] === 'events') {
    // Old format: /events/[id]
    return {
      id: parts[1],
      category: null,
      titleSlug: null,
      isLegacyUrl: true
    };
  }

  if (parts.length === 4 && parts[0] === 'events') {
    // New format: /events/[category]/[title]/[id]
    const [, categorySlug, titleSlug, id] = parts;
    const category = slugCategoryMap[categorySlug];

    return {
      id,
      category,
      titleSlug,
      categorySlug,
      isLegacyUrl: false
    };
  }

  return null;
};

/**
 * Generate canonical URL for an event (always returns new format)
 * @param event Event object
 * @returns Canonical URL path
 */
export const getCanonicalEventUrl = (event: {
  id: string;
  title: string;
  category: string;
}) => {
  return generateEventUrl(event);
};

export const calculateEventMetrics = (event: any) => {
  const totalSeats = event.regularSeats + (event.vipSeats || 0);
  const totalSold = event.tickets.reduce((acc: number, ticket: any) => acc + ticket.sold, 0);
  const occupancyRate = (totalSold / totalSeats) * 100;

  return {
    totalSeats,
    totalSold,
    occupancyRate: Math.round(occupancyRate * 100) / 100,
    revenue: event.tickets.reduce((acc: number, ticket: any) => 
      acc + (ticket.price * ticket.sold), 0
    ),
  };
};