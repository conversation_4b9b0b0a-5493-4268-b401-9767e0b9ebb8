#!/usr/bin/env node

/**
 * This script manually seeds the database with an admin user
 * Run it with: node scripts/seed-admin.js
 */

// Load environment variables
require('dotenv').config();

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('Starting database seed for admin users...');

  // Get superadmin credentials from environment variables
  const superadminEmail = process.env.DEFAULT_SUPERADMIN_EMAIL || '<EMAIL>';
  const superadminPassword = process.env.DEFAULT_SUPERADMIN_PASSWORD || 'SuperAdmin@123456';
  const superadminName = process.env.DEFAULT_SUPERADMIN_NAME || 'System Super Administrator';

  // Get regular admin credentials from environment variables
  const adminEmail = process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>';
  const adminPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'Admin@123456';
  const adminName = process.env.DEFAULT_ADMIN_NAME || 'System Administrator';

  console.log(`Checking for existing superadmin user with email: ${superadminEmail}`);

  // Check if superadmin user already exists
  const existingSuperadmin = await prisma.user.findFirst({
    where: {
      email: superadminEmail,
      role: 'SUPERADMIN'
    }
  });

  if (existingSuperadmin) {
    console.log('Superadmin user already exists:', existingSuperadmin.email);
  } else {
    console.log('No superadmin user found. Creating default superadmin user...');

    // Hash the password
    const hashedSuperadminPassword = await bcrypt.hash(superadminPassword, 12);

    // Create the superadmin user
    const superadmin = await prisma.user.create({
      data: {
        email: superadminEmail,
        name: superadminName,
        password: hashedSuperadminPassword,
        role: 'SUPERADMIN',
        emailVerified: new Date(),
      }
    });

    console.log('Default superadmin user created:', superadmin.email);
  }

  console.log(`Checking for existing admin user with email: ${adminEmail}`);

  // Check if user with admin email exists (regardless of role)
  const existingUser = await prisma.user.findUnique({
    where: {
      email: adminEmail
    }
  });

  if (existingUser) {
    console.log('User with admin email already exists:', existingUser.email);

    // Check if the user is already an ADMIN
    if (existingUser.role === 'ADMIN') {
      console.log('User already has ADMIN role');
    } else {
      console.log(`Updating user role from ${existingUser.role} to ADMIN...`);

      // Update the user's role to ADMIN
      const updatedUser = await prisma.user.update({
        where: { id: existingUser.id },
        data: { role: 'ADMIN' }
      });

      console.log('User role updated to ADMIN:', updatedUser.email);
    }
  } else {
    console.log('No user with admin email found. Creating default admin user...');

    // Hash the password
    const hashedAdminPassword = await bcrypt.hash(adminPassword, 12);

    // Create the admin user
    const admin = await prisma.user.create({
      data: {
        email: adminEmail,
        name: adminName,
        password: hashedAdminPassword,
        role: 'ADMIN',
        emailVerified: new Date(),
      }
    });

    console.log('Default admin user created:', admin.email);
  }

  console.log('Database seed completed.');
}

main()
  .catch((e) => {
    console.error('Error during database seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
