# Elite Communication System Database Seeding

This document describes the database seeding scripts for the Elite Communication System, which populate the database with comprehensive sample data for testing and development.

## Overview

The Elite Communication System seeding creates realistic test data including:

- **15 Sample Users** with diverse professional profiles
- **3 Sample Events** (Tech Summit, Finance Conference, Healthcare Symposium)
- **Elite Communication Subscriptions** (Basic, Elite, Elite Pro tiers)
- **Attendee Profiles** with networking information and privacy settings
- **Chat Rooms** (Elite and Elite Pro exclusive)
- **Sample Messages** and conversation threads
- **Meeting Requests** between users
- **Chat Room Messages** for demonstration

## Seeding Scripts

### 1. Main Seed Script (`prisma/seed.ts`)

The main seed script includes Elite Communication seeding as part of the complete database initialization.

**Run with:**
```bash
npx prisma db seed
```

This script:
- Creates admin users and fee configurations
- Seeds partners and promotions
- **Seeds the Elite Communication System**
- Runs all seeding in the correct order

### 2. Dedicated Elite Communication Seed (`prisma/seed-elite-communication.ts`)

A standalone script specifically for the Elite Communication System.

**Run with:**
```bash
npx tsx prisma/seed-elite-communication.ts
```

This script focuses exclusively on Elite Communication data and can be run independently for testing.

## Sample Data Details

### Users Created

The seeding creates 15 diverse users across different industries:

1. **Sarah Chen** (Elite Pro) - Senior Software Engineer, Technology
2. **Marcus Johnson** (Elite Pro) - Investment Director, Finance
3. **Dr. Emily Rodriguez** (Elite) - Chief Medical Officer, Healthcare
4. **David Kim** (Elite) - Creative Director, Marketing
5. **Lisa Thompson** (Elite Pro) - Business Strategy Consultant
6. **Ahmed Hassan** (Elite) - Product Manager, Retail Technology
7. **Jennifer Wu** (Elite) - Data Scientist, Data Analytics
8. **Robert Brown** (Basic) - Operations Director, Manufacturing
9. **Maria Garcia** (Elite) - Program Director, Non-Profit
10. **James Wilson** (Elite) - Senior Broker, Real Estate
11. **Anna Petrov** (Elite Pro) - Security Architect, Cybersecurity
12. **Michael Davis** (Basic) - Supply Chain Manager, Logistics
13. **Sophie Martin** (Elite) - UX Designer, Design
14. **Carlos Rodriguez** (Elite) - Project Engineer, Energy
15. **Rachel Green** (Basic) - Learning Experience Designer, Education

### Events Created

1. **Tech Innovation Summit 2024**
   - Location: San Francisco Convention Center
   - Focus: Technology discussions and networking
   - Capacity: 500 attendees

2. **Global Finance & Fintech Conference**
   - Location: New York Financial District
   - Focus: Finance, blockchain, and digital transformation
   - Capacity: 300 attendees

3. **Healthcare Innovation Symposium**
   - Location: Chicago Medical Center
   - Focus: Digital health and medical AI
   - Capacity: 200 attendees

### Subscription Tiers

- **Basic (Free)**: View attendee list only, no messaging
- **Elite ($29.99)**: Direct messaging, enhanced networking
- **Elite Pro ($49.99)**: All Elite features plus meeting requests and VIP chat rooms

### Chat Rooms

For each event:
- **Elite Networking Room**: Accessible to Elite and Elite Pro members
- **Elite Pro VIP Lounge**: Exclusive to Elite Pro members only

### Sample Communications

- **Direct Messages**: Realistic conversation starters between professionals
- **Meeting Requests**: Virtual and in-person meeting proposals
- **Chat Room Messages**: Welcome messages and networking discussions

## Privacy and Networking Settings

Users are configured with realistic privacy levels:

- **Basic users**: Hidden profiles, no messaging allowed
- **Elite users**: Elite-only visibility and messaging
- **Elite Pro users**: Public profiles, open to everyone

Each user has:
- Industry-specific interests and networking goals
- Realistic availability hours (Monday-Friday 9-5)
- LinkedIn profiles and professional bios
- Geographic distribution across time zones

## Usage for Development

### Testing Elite Communication Features

After seeding, you can test:

1. **User Discovery**: Browse attendee directories with different privacy levels
2. **Messaging System**: Send messages between users with different tiers
3. **Meeting Requests**: Create and respond to meeting proposals
4. **Chat Rooms**: Join tier-specific chat rooms and send messages
5. **Subscription Management**: View and manage Elite Communication subscriptions

### Verification Script

To verify that all data was created correctly, run:
```bash
npx tsx prisma/verify-elite-communication.ts
```

This script will check:
- All users, events, and subscriptions were created
- Attendee profiles have correct privacy settings
- Chat rooms are properly configured with members
- Sample messages and meeting requests exist
- Data relationships are intact

### Cleanup Script

To remove all Elite Communication data for testing:
```bash
npx tsx prisma/cleanup-elite-communication.ts
```

This script safely removes all Elite Communication data in the correct order to respect foreign key constraints.

### Sample Login Credentials

All seeded users have the password: `EliteUser123!`

Example logins:
- `<EMAIL>` (Elite Pro)
- `<EMAIL>` (Elite Pro)
- `<EMAIL>` (Elite)
- `<EMAIL>` (Basic)

### Resetting Data

To reset and re-seed the Elite Communication data:

1. Clear existing data (if needed):
   ```bash
   npx prisma db push --force-reset
   ```

2. Run migrations:
   ```bash
   npx prisma migrate deploy
   ```

3. Seed the database:
   ```bash
   npx prisma db seed
   ```

## Development Notes

- The seeding script checks for existing data to prevent duplicates
- All relationships between users, events, and communications are properly established
- Sample data includes realistic timestamps and status variations
- Geographic and industry diversity ensures comprehensive testing scenarios

## Troubleshooting

If seeding fails:

1. Ensure database is running and accessible
2. Check that all migrations are applied: `npx prisma migrate status`
3. Verify Prisma schema is up to date: `npx prisma generate`
4. Check for any constraint violations in the console output

For questions or issues with the Elite Communication seeding, refer to the main seed script logs for detailed error information.
