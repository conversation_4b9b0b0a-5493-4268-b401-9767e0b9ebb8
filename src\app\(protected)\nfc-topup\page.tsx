'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import { CreditCard, Tag, Watch, Wallet, DollarSign, CreditCard as CreditCardIcon, Phone, AlertTriangle, Info, Check } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

// Types
interface NFCDevice {
  id: string;
  type: 'CARD' | 'WRISTBAND' | 'TAG';
  status: 'ACTIVE' | 'DEACTIVATED' | 'LOST';
  balance: number;
  lastUsed: string | null;
  issuedAt: string;
}

export default function NFCTopupPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const deviceId = searchParams.get('deviceId');

  const [devices, setDevices] = useState<NFCDevice[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDeviceId, setSelectedDeviceId] = useState<string | null>(deviceId);
  const [amount, setAmount] = useState(20);
  const [paymentMethod, setPaymentMethod] = useState('credit-card');
  const [isProcessing, setIsProcessing] = useState(false);

  // Credit card state
  const [cardNumber, setCardNumber] = useState('');
  const [cardholderName, setCardholderName] = useState('');
  const [expiryDate, setExpiryDate] = useState('');
  const [cvv, setCvv] = useState('');

  // Mobile money state
  const [mobileNumber, setMobileNumber] = useState('');
  const [mobileProvider, setMobileProvider] = useState('airtel');

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/login?callbackUrl=/nfc-topup');
    }
  }, [status, router]);

  // Fetch user's NFC devices
  useEffect(() => {
    const fetchDevices = async () => {
      if (status !== 'authenticated') return;

      setIsLoading(true);

      try {
        const response = await fetch('/api/user/nfc-devices');

        if (response.ok) {
          const data = await response.json();
          setDevices(data);

          // If no device is selected and we have devices, select the first active one
          if (!selectedDeviceId && data.length > 0) {
            const activeDevice = data.find(d => d.status === 'ACTIVE');
            if (activeDevice) {
              setSelectedDeviceId(activeDevice.id);
            } else if (data[0]) {
              setSelectedDeviceId(data[0].id);
            }
          }
        } else {
          throw new Error('Failed to fetch devices');
        }
      } catch (error) {
        console.error('Error fetching NFC devices:', error);
        toast({
          title: 'Error',
          description: 'Failed to load your NFC devices',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchDevices();
  }, [status, selectedDeviceId]);

  const handleTopUp = async () => {
    if (!selectedDeviceId) {
      toast({
        title: 'No device selected',
        description: 'Please select an NFC device to top up',
        variant: 'destructive',
      });
      return;
    }

    if (amount < 5) {
      toast({
        title: 'Invalid amount',
        description: 'Minimum top-up amount is $5',
        variant: 'destructive',
      });
      return;
    }

    setIsProcessing(true);

    try {
      // Prepare payment details based on the selected payment method
      let paymentDetails = {};

      if (paymentMethod === 'credit-card') {
        if (!cardNumber || !cardholderName || !expiryDate || !cvv) {
          throw new Error('Please fill in all credit card details');
        }

        paymentDetails = {
          cardNumber: cardNumber.replace(/\s/g, '').slice(-4), // Only store last 4 digits for security
          cardholderName,
          expiryDate,
        };
      } else if (paymentMethod === 'mobile-money') {
        if (!mobileNumber) {
          throw new Error('Please enter your mobile number');
        }

        paymentDetails = {
          provider: mobileProvider,
          mobileNumber,
        };
      }

      const response = await fetch('/api/user/nfc-devices/topup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deviceId: selectedDeviceId,
          amount,
          paymentMethod,
          paymentDetails,
        }),
      });

      if (response.ok) {
        const result = await response.json();

        toast({
          title: 'Top-up successful!',
          description: `$${amount.toFixed(2)} has been added to your device balance.`,
        });

        // Update the device in the local state
        setDevices(prev =>
          prev.map(device =>
            device.id === selectedDeviceId
              ? { ...device, balance: device.balance + amount }
              : device
          )
        );

        // Clear payment form
        setCardNumber('');
        setCardholderName('');
        setExpiryDate('');
        setCvv('');
        setMobileNumber('');
      } else {
        const error = await response.json();
        throw new Error(error.message || 'Failed to top up device');
      }
    } catch (error) {
      console.error('Error topping up device:', error);
      toast({
        title: 'Top-up failed',
        description: error instanceof Error ? error.message : 'An error occurred during top-up',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Get the selected device
  const selectedDevice = devices.find(d => d.id === selectedDeviceId);

  // Helper function to get icon for device type
  const getDeviceIcon = (type: string) => {
    switch (type) {
      case 'CARD':
        return <CreditCard className="h-5 w-5" />;
      case 'WRISTBAND':
        return <Watch className="h-5 w-5" />;
      case 'TAG':
        return <Tag className="h-5 w-5" />;
      default:
        return <CreditCard className="h-5 w-5" />;
    }
  };

  // Predefined top-up amounts
  const topupAmounts = [10, 20, 50, 100];

  if (status === 'loading' || isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <h1 className="text-2xl font-bold mb-6">Top Up NFC Device</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-8 w-3/4 mb-2" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Skeleton className="h-24 w-full" />
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <Skeleton className="h-8 w-3/4 mb-2" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6 text-blue-700">Top Up NFC Device</h1>

      {devices.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <AlertTriangle className="h-12 w-12 text-orange-500 mb-4" />
            <h3 className="text-xl font-medium text-blue-700">No NFC devices found</h3>
            <p className="text-gray-600 mt-2 text-center">
              You don't have any NFC devices linked to your account yet.
            </p>
            <Button asChild className="mt-6 bg-orange-600 hover:bg-orange-700">
              <Link href="/nfc-store">Get an NFC Device</Link>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="border-t-4 border-t-blue-500">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-white">
              <CardTitle className="text-blue-700">Select Device</CardTitle>
              <CardDescription>
                Choose which NFC device you want to top up
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <RadioGroup
                  value={selectedDeviceId || ''}
                  onValueChange={setSelectedDeviceId}
                  className="space-y-3"
                >
                  {devices.map((device) => (
                    <div
                      key={device.id}
                      className={`flex items-center space-x-2 border rounded-md p-3 ${
                        selectedDeviceId === device.id ? 'border-primary bg-primary/5' : ''
                      } ${device.status !== 'ACTIVE' ? 'opacity-60' : ''}`}
                    >
                      <RadioGroupItem
                        value={device.id}
                        id={device.id}
                        disabled={device.status !== 'ACTIVE'}
                      />
                      <Label
                        htmlFor={device.id}
                        className="flex items-center justify-between w-full cursor-pointer"
                      >
                        <div className="flex items-center">
                          {getDeviceIcon(device.type)}
                          <div className="ml-3">
                            <div className="font-medium">{device.type.charAt(0) + device.type.slice(1).toLowerCase()}</div>
                            <div className="text-sm text-gray-500">
                              Balance: ${device.balance.toFixed(2)}
                            </div>
                          </div>
                        </div>
                        <div>
                          {device.status === 'ACTIVE' ? (
                            <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                              Active
                            </Badge>
                          ) : (
                            <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
                              {device.status}
                            </Badge>
                          )}
                        </div>
                      </Label>
                    </div>
                  ))}
                </RadioGroup>

                {selectedDevice && selectedDevice.status !== 'ACTIVE' && (
                  <div className="bg-gradient-to-r from-orange-50 to-blue-50 border-l-2 border-l-orange-400 border-r-2 border-r-blue-400 rounded-md p-3 text-sm">
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="h-4 w-4 mt-0.5 flex-shrink-0 text-orange-500" />
                      <p className="text-blue-700">
                        This device is currently {selectedDevice.status.toLowerCase()}.
                        You need to activate it before you can top it up.
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2 bg-white border-blue-500 text-blue-600 hover:bg-blue-50"
                      asChild
                    >
                      <Link href="/my-tickets?tab=nfc">Manage Devices</Link>
                    </Button>
                  </div>
                )}

                <div className="pt-4">
                  <Label className="mb-2 block">Top-up Amount</Label>
                  <div className="grid grid-cols-4 gap-2 mb-3">
                    {topupAmounts.map((amt) => (
                      <Button
                        key={amt}
                        type="button"
                        variant={amount === amt ? "default" : "outline"}
                        onClick={() => setAmount(amt)}
                        className={`w-full ${amount === amt ? 'bg-blue-600 hover:bg-blue-700' : 'border-orange-500 text-orange-600 hover:bg-orange-50'}`}
                      >
                        ${amt}
                      </Button>
                    ))}
                  </div>
                  <div className="flex items-center gap-2">
                    <Label htmlFor="custom-amount">Custom Amount:</Label>
                    <div className="relative flex-1">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4" />
                      <Input
                        id="custom-amount"
                        type="number"
                        min="5"
                        step="1"
                        value={amount}
                        onChange={(e) => setAmount(Math.max(5, parseInt(e.target.value) || 0))}
                        className="pl-8"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-t-4 border-t-orange-500">
            <CardHeader className="bg-gradient-to-r from-orange-50 to-white">
              <CardTitle className="text-orange-600">Payment Method</CardTitle>
              <CardDescription>
                Choose how you want to pay for the top-up
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <RadioGroup
                  value={paymentMethod}
                  onValueChange={setPaymentMethod}
                  className="space-y-3"
                >
                  <div className={`flex items-center space-x-2 border rounded-md p-3 ${paymentMethod === 'credit-card' ? 'border-blue-500 bg-blue-50' : ''}`}>
                    <RadioGroupItem value="credit-card" id="credit-card" className="text-blue-600" />
                    <Label htmlFor="credit-card" className="flex items-center cursor-pointer">
                      <CreditCardIcon className="h-5 w-5 mr-2 text-blue-600" />
                      Credit / Debit Card
                    </Label>
                  </div>

                  <div className={`flex items-center space-x-2 border rounded-md p-3 ${paymentMethod === 'mobile-money' ? 'border-orange-500 bg-orange-50' : ''}`}>
                    <RadioGroupItem value="mobile-money" id="mobile-money" className="text-orange-600" />
                    <Label htmlFor="mobile-money" className="flex items-center cursor-pointer">
                      <Phone className="h-5 w-5 mr-2 text-orange-600" />
                      Mobile Money
                    </Label>
                  </div>

                  <div className={`flex items-center space-x-2 border rounded-md p-3 ${paymentMethod === 'wallet' ? 'border-blue-500 bg-blue-50' : ''}`}>
                    <RadioGroupItem value="wallet" id="wallet" className="text-blue-600" />
                    <Label htmlFor="wallet" className="flex items-center cursor-pointer">
                      <Wallet className="h-5 w-5 mr-2 text-blue-600" />
                      Account Wallet
                    </Label>
                  </div>
                </RadioGroup>

                {/* Credit Card Form */}
                {paymentMethod === 'credit-card' && (
                  <div className="space-y-4 pt-2">
                    <div className="space-y-2">
                      <Label htmlFor="cardNumber">Card Number</Label>
                      <Input
                        id="cardNumber"
                        value={cardNumber}
                        onChange={(e) => setCardNumber(e.target.value)}
                        placeholder="1234 5678 9012 3456"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="cardholderName">Cardholder Name</Label>
                      <Input
                        id="cardholderName"
                        value={cardholderName}
                        onChange={(e) => setCardholderName(e.target.value)}
                        placeholder="John Doe"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="expiryDate">Expiry Date</Label>
                        <Input
                          id="expiryDate"
                          value={expiryDate}
                          onChange={(e) => setExpiryDate(e.target.value)}
                          placeholder="MM/YY"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="cvv">CVV</Label>
                        <Input
                          id="cvv"
                          type="password"
                          value={cvv}
                          onChange={(e) => setCvv(e.target.value)}
                          placeholder="123"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* Mobile Money Form */}
                {paymentMethod === 'mobile-money' && (
                  <div className="space-y-4 pt-2">
                    <div className="space-y-2">
                      <Label htmlFor="mobileProvider">Select Provider</Label>
                      <RadioGroup
                        value={mobileProvider}
                        onValueChange={setMobileProvider}
                        className="flex space-x-4 pt-2"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="airtel" id="airtel" />
                          <Label htmlFor="airtel" className="flex items-center cursor-pointer">
                            <span className="text-red-600 font-semibold">Airtel Money</span>
                          </Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="mtn" id="mtn" />
                          <Label htmlFor="mtn" className="flex items-center cursor-pointer">
                            <span className="text-yellow-600 font-semibold">MTN Mobile Money</span>
                          </Label>
                        </div>
                      </RadioGroup>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="mobileNumber">Mobile Number</Label>
                      <Input
                        id="mobileNumber"
                        value={mobileNumber}
                        onChange={(e) => setMobileNumber(e.target.value)}
                        placeholder="+**********"
                      />
                      <p className="text-sm text-gray-500">
                        Enter the mobile number registered with your {mobileProvider === 'airtel' ? 'Airtel Money' : 'MTN Mobile Money'} account
                      </p>
                    </div>

                    <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 text-sm text-yellow-800">
                      <p>You will receive a prompt on your mobile phone to confirm the payment.</p>
                    </div>
                  </div>
                )}

                {/* Wallet Form */}
                {paymentMethod === 'wallet' && (
                  <div className="space-y-4 pt-2">
                    <div className="bg-blue-50 border border-blue-200 rounded-md p-3 text-sm">
                      <div className="flex items-start gap-2">
                        <Info className="h-4 w-4 mt-0.5 text-blue-500 flex-shrink-0" />
                        <div>
                          <p className="text-blue-700">
                            Your current wallet balance: <span className="font-semibold">$120.00</span>
                          </p>
                          <p className="text-blue-600 mt-1">
                            The amount will be deducted from your account wallet balance.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Top-up Amount</span>
                    <span>${amount.toFixed(2)}</span>
                  </div>

                  <div className="flex justify-between">
                    <span>Processing Fee</span>
                    <span>${(amount * 0.02).toFixed(2)}</span>
                  </div>

                  <Separator />

                  <div className="flex justify-between font-bold">
                    <span>Total</span>
                    <span>${(amount + (amount * 0.02)).toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-2">
              <Button
                className="w-full"
                onClick={handleTopUp}
                disabled={isProcessing || !selectedDeviceId || (selectedDevice && selectedDevice.status !== 'ACTIVE')}
              >
                {isProcessing ? 'Processing...' : 'Complete Top-up'}
              </Button>

              <Button
                variant="outline"
                className="w-full"
                asChild
              >
                <Link href="/my-tickets?tab=nfc">Back to My Devices</Link>
              </Button>
            </CardFooter>
          </Card>
        </div>
      )}

      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-4">Need a new NFC device?</h2>
        <Card>
          <CardHeader>
            <CardTitle>Purchase NFC Devices</CardTitle>
            <CardDescription>
              Get cards, wristbands, or tags for contactless payments
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-3">
                <CreditCard className="h-8 w-8 text-blue-500" />
                <div>
                  <h3 className="font-medium">NFC Cards</h3>
                  <p className="text-sm text-gray-500">Wallet-sized payment cards</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Watch className="h-8 w-8 text-green-500" />
                <div>
                  <h3 className="font-medium">NFC Wristbands</h3>
                  <p className="text-sm text-gray-500">Comfortable wearable payment</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Tag className="h-8 w-8 text-purple-500" />
                <div>
                  <h3 className="font-medium">NFC Tags</h3>
                  <p className="text-sm text-gray-500">Versatile keychain attachments</p>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button asChild className="w-full">
              <Link href="/nfc-store">Browse NFC Devices</Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
