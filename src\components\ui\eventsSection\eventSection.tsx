
"use client"

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { generateEventUrl } from '@/lib/utils/events';
import { Search, Calendar, MapPin, Users, Filter, Clock, Star } from 'lucide-react';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';

enum EventCategory {
  WEDDING = 'Wedding',
  FUNERAL = 'Funeral',
  BUSINESS = 'Business',
  MUSIC = 'Music',
  LIFESTYLE_EVENTS = 'Lifestyle',
  EDUCATIONAL_EVENTS = 'Educational',
  HOLIDAY_CELEBRATIONS = 'Holiday',
  FASHION_SHOWS = 'Fashion',
  HEALTH_AND_WELLNESS = 'Health & Wellness',
  CULTURAL_FESTIVALS = 'Cultural',
  GAMING_EVENTS = 'Gaming',
  ENVIRONMENTAL_EVENTS = 'Environmental',
  TRADE_FAIR = 'Trade Fair',
  AGRICULTURAL_AND_COMMECIAL_SHOW = 'Agricultural & Commercial', // Fixed typo to match schema
  WEB_DEVELOPMENT = 'Web Development',
  MARKETING = 'Marketing',
  TECHNOLOGY = 'Technology',
  CONCERTS_AND_CHURCH = 'Concerts & Church',
  CONFERENCES_AND_WORKSHOPS = 'Conferences & Workshops',
  SPORTS_AND_FITNESS = 'Sports & Fitness',
  ARTS_AND_THEATER = 'Arts & Theater',
  FAMILY_AND_KIDS = 'Family & Kids',
  FOOD_AND_DRINK = 'Food & Drink',
  CHARITY_AND_FUNDRAISERS = 'Charity & Fundraisers',
  COMEDY_SHOWS = 'Comedy',
  NETWORKING_AND_SOCIAL_GATHERINGS = 'Networking & Social',
  FILM_SCREENINGS = 'Film Screenings',
}

interface Event {
  id: string;
  imagePath: string | null;
  title: string;
  description: string;
  startDate: Date | string; // Schema uses DateTime
  endDate: Date | string;   // Schema uses DateTime
  location: string;
  venue: string;
  category: EventCategory;
  startTime: string;
  endTime: string;
  eventType: string; // Should match EventType enum from schema
  status: string;    // Should match EventStatus enum from schema
  createdAt?: Date;
  updatedAt?: Date;
  userId?: string;
  isFree?: boolean;  // Added this property
  regularPrice?: number;
  vipPrice?: number;

  // Optional relations
  ageRestriction?: {
    id?: string;
    minAge?: number;
    maxAge?: number;
    description?: string;
  } | null;

  ParkingManagement?: {
    id?: string;
    totalSpaces: number;
    reservedSpaces: number;
    isFree: boolean;
    pricePerHour?: number;
    reservationRequired?: boolean;
    description?: string;
  }[];
}

interface FilterOptions {
  categories: EventCategory[];
  dateRange: string;
  priceRange: string;
  eventType: string;
}

const EventSection: React.FC = () => {
  const [events, setEvents] = useState<Event[]>([]);
  const [filteredEvents, setFilteredEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    categories: [],
    dateRange: 'all',
    priceRange: 'all',
    eventType: 'all',
  });
  const [pagination, setPagination] = useState({
    total: 0,
    pages: 1,
    page: 1,
    limit: 12
  });

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        if (typeof window !== 'undefined') {
          // Use the featured events API endpoint instead of published events
          const response = await fetch(`/api/events/featured?limit=${pagination.limit}`);

          console.log('Response status:', response.status);
          console.log('Response headers:', response.headers.get('content-type'));

          if (!response.ok) {
            const errorText = await response.text();
            console.error('Error response:', errorText);
            throw new Error(`Failed to fetch featured events: ${response.status} ${response.statusText}`);
          }

          const contentType = response.headers.get('content-type');
          if (!contentType || !contentType.includes('application/json')) {
            const text = await response.text();
            console.error('Received non-JSON response:', text.substring(0, 200));
            throw new Error('Response is not JSON');
          }

          const data = await response.json();
          console.log('Fetched featured events data:', data);

          // Update to handle the featured events response structure
          setEvents(data.events || []);
          setFilteredEvents(data.events || []);

          // Set pagination data if available (though featured API might not have pagination)
          if (data.pagination) {
            setPagination(data.pagination);
          } else {
            // If no pagination data, update the total count based on events length
            setPagination(prev => ({
              ...prev,
              total: data.events?.length || 0,
              pages: 1
            }));
          }
        }
      } catch (err) {
        console.error('Fetch error:', err);
        setError((err as Error).message);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, [pagination.limit]);

  useEffect(() => {
    const applyFilters = () => {
      let result = events;

      // Apply search filter
      if (searchTerm) {
        result = result.filter(event =>
          event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          event.description.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }

      // Apply category filter
      if (filterOptions.categories.length > 0) {
        result = result.filter(event => filterOptions.categories.includes(event.category));
      }

      // Apply date range filter
      if (filterOptions.dateRange !== 'all') {
        const today = new Date();
        const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
        const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());

        result = result.filter(event => {
          const eventDate = new Date(event.startDate);
          switch (filterOptions.dateRange) {
            case 'today':
              return eventDate.toDateString() === today.toDateString();
            case 'thisWeek':
              return eventDate >= today && eventDate <= nextWeek;
            case 'thisMonth':
              return eventDate >= today && eventDate <= nextMonth;
            default:
              return true;
          }
        });
      }

      // Apply price range filter
      if (filterOptions.priceRange !== 'all') {
        result = result.filter(event => {
          if (filterOptions.priceRange === 'free') return event.isFree;
          if (filterOptions.priceRange === 'paid') return !event.isFree;
          return true;
        });
      }

      // Apply event type filter
      if (filterOptions.eventType !== 'all') {
        result = result.filter(event => event.eventType === filterOptions.eventType);
      }

      setFilteredEvents(result);
    };

    applyFilters();
  }, [events, searchTerm, filterOptions]);

  const loadMoreEvents = () => {
    if (pagination.page < pagination.pages) {
      setPagination(prev => ({
        ...prev,
        page: prev.page + 1
      }));
    }
  };

  if (loading && events.length === 0) return <div className="flex justify-center items-center h-screen">Loading...</div>;
  if (error) return <div className="flex justify-center items-center h-screen text-red-500">Error: {error}</div>;

  // Removing dark mode classes from the return statement
  return (
    <div className="bg-gray-100 min-h-screen py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-center text-gray-900 mb-2">Featured Events</h1>
        <p className="text-xl text-center text-gray-600 mb-8">Discover and join our most popular featured events</p>

        <div className="mb-8 flex flex-col sm:flex-row gap-4 items-center justify-between">
          <Input
            type="text"
            placeholder="Search events..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full sm:w-1/2"
          />
          <FilterDialog filterOptions={filterOptions} setFilterOptions={setFilterOptions} />
        </div>

        <EventGrid events={filteredEvents} />

        {pagination.page < pagination.pages && (
          <div className="flex justify-center mt-8">
            <Button onClick={loadMoreEvents} disabled={loading}>
              {loading ? "Loading..." : "Load More Featured Events"}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

const FilterDialog: React.FC<{
  filterOptions: FilterOptions;
  setFilterOptions: React.Dispatch<React.SetStateAction<FilterOptions>>;
}> = ({ filterOptions, setFilterOptions }) => {
  const [tempFilters, setTempFilters] = useState(filterOptions);

  const handleCategoryChange = (category: EventCategory) => {
    setTempFilters(prev => ({
      ...prev,
      categories: prev.categories.includes(category)
        ? prev.categories.filter(c => c !== category)
        : [...prev.categories, category]
    }));
  };

  const applyFilters = () => {
    setFilterOptions(tempFilters);
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline"><Filter className="mr-2 h-4 w-4" /> Filters</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[80vw] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Filter Events</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-2">
          <div className="grid grid-cols-6 items-start gap-4">
            <Label htmlFor="categories" className="text-right col-span-1">
              Categories
            </Label>
            <div className="col-span-5 grid grid-cols-3 gap-2">
              {Object.values(EventCategory).map(category => (
                <div key={category} className="flex items-center space-x-2">
                  <Checkbox
                    id={category}
                    checked={tempFilters.categories.includes(category)}
                    onCheckedChange={() => handleCategoryChange(category)}
                  />
                  <label htmlFor={category} className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">{category}</label>
                </div>
              ))}
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="dateRange" className="text-right">
              Date Range
            </Label>
            <Select
              onValueChange={(value) => setTempFilters(prev => ({ ...prev, dateRange: value }))}
              defaultValue={tempFilters.dateRange}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select date range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Dates</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="thisWeek">This Week</SelectItem>
                <SelectItem value="thisMonth">This Month</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="priceRange" className="text-right">
              Price Range
            </Label>
            <Select
              onValueChange={(value) => setTempFilters(prev => ({ ...prev, priceRange: value }))}
              defaultValue={tempFilters.priceRange}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select price range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Prices</SelectItem>
                <SelectItem value="free">Free</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="eventType" className="text-right">
              Event Type
            </Label>
            <Select
              onValueChange={(value) => setTempFilters(prev => ({ ...prev, eventType: value }))}
              defaultValue={tempFilters.eventType}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select event type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="ONLINE">Online</SelectItem>
                <SelectItem value="PHYSICAL">In Person</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <DialogFooter>
          <Button onClick={applyFilters}>Apply Filters</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const EventGrid: React.FC<{ events: Event[] }> = ({ events }) => (
  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
    {events.length > 0 ? (
      events.map(event => (
        <Card key={event.id} className="overflow-hidden transition-all duration-300 hover:shadow-lg rounded-lg border-0 bg-white flex flex-col h-full">
          <div className="relative">
            <div className="absolute top-2 right-2 z-10">
              <div className="bg-yellow-500 text-white text-xs px-2 py-1 rounded-md font-medium flex items-center">
                <Star className="h-3 w-3 mr-1 fill-white" />
                Featured
              </div>
            </div>
            <div className="h-48 overflow-hidden">
              <Image
                src={event.imagePath || '/placeholder-event.jpg'}
                alt={event.title}
                width={400}
                height={200}
                className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
              />
            </div>
          </div>

          <CardContent className="p-4 flex-grow">
            <CardTitle className="text-lg font-bold mb-2 line-clamp-1 text-gray-800 hover:text-orange-500 transition-colors">
              {event.title}
            </CardTitle>

            <p className="text-gray-600 mb-3 line-clamp-2 text-xs">{event.description}</p>

            <div className="space-y-1.5">
              <div className="flex items-center text-gray-600">
                <Calendar className="w-3.5 h-3.5 mr-1.5 text-orange-500" />
                <span className="text-xs">
                  {new Date(event.startDate).toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                  })}
                </span>
              </div>

              <div className="flex items-center text-gray-600">
                <MapPin className="w-3.5 h-3.5 mr-1.5 text-orange-500" />
                <span className="text-xs truncate">{event.eventType === 'ONLINE' ? 'Online Event' : event.venue}</span>
              </div>

              <div className="flex justify-between items-center mt-2">
                {event.isFree ? (
                  <span className="inline-block bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-md font-medium">Free</span>
                ) : (
                  <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-md font-medium">
                    {event.regularPrice ? `${event.regularPrice} ZMW` : 'Paid'}
                  </span>
                )}

                <Link href={generateEventUrl({ id: event.id, title: event.title, category: event.category })} passHref>
                  <Button variant="outline" size="sm" className="bg-white hover:bg-orange-50 text-orange-600 border-orange-200 hover:border-orange-300 font-medium text-xs transition-all duration-200 shadow-sm">
                    Details →
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      ))
    ) : (
      <div className="col-span-full text-center p-8 bg-white rounded-xl shadow-sm">
        <div className="flex flex-col items-center">
          <Calendar className="w-10 h-10 text-gray-300 mb-3" />
          <h3 className="text-lg font-semibold text-gray-800">No featured events found</h3>
          <p className="text-gray-500 mt-1 max-w-md mx-auto text-sm">There are no featured events at the moment. Check back later or contact us to feature your event.</p>
        </div>
      </div>
    )}
  </div>
);

export default EventSection;