/**
 * Permission Combinations Testing Script
 * 
 * This script tests different combinations of permissions against various endpoints.
 * It helps verify that the permission system is working correctly.
 * 
 * Usage:
 * node test-permission-combinations.js
 * 
 * Note: This script requires multiple API keys with different permission combinations.
 * You'll need to create these API keys manually and add them to the apiKeys array below.
 */

const fetch = require('node-fetch');

// Define API keys with different permission combinations
// Replace these with actual API keys from your system
const apiKeys = [
  { 
    key: 'your-read-only-key', 
    name: 'Read Only', 
    permissions: ['read:events', 'read:tickets', 'read:orders'] 
  },
  { 
    key: 'your-write-only-key', 
    name: 'Write Only', 
    permissions: ['write:events', 'write:tickets', 'write:orders'] 
  },
  { 
    key: 'your-read-write-key', 
    name: 'Read & Write', 
    permissions: ['read:events', 'write:events', 'read:tickets', 'write:tickets'] 
  },
  { 
    key: 'your-full-access-key', 
    name: 'Full Access', 
    permissions: ['read:events', 'write:events', 'delete:events', 'read:tickets', 'write:tickets', 'delete:tickets', 'read:orders', 'write:orders', 'delete:orders', 'read:users', 'write:users', 'read:analytics'] 
  }
];

// Define endpoints to test with their required permissions
const endpoints = [
  { url: 'http://localhost:3000/api/test', method: 'GET', description: 'Test API (Public)', requiredPermissions: [] },
  { url: 'http://localhost:3000/api/events/published', method: 'GET', description: 'Published Events (Public)', requiredPermissions: [] },
  { url: 'http://localhost:3000/api/external/events', method: 'GET', description: 'External Events API', requiredPermissions: ['read:events'] },
  { url: 'http://localhost:3000/api/events', method: 'GET', description: 'Events API', requiredPermissions: ['read:events'] },
  { url: 'http://localhost:3000/api/events/create', method: 'POST', description: 'Create Event', requiredPermissions: ['write:events'], body: {
    title: 'Test Event',
    description: 'Test Description',
    startDate: new Date(Date.now() + 86400000).toISOString(),
    endDate: new Date(Date.now() + 172800000).toISOString(),
    location: 'Test Location',
    venue: 'Test Venue',
    category: 'MUSIC',
    eventType: 'CONCERT'
  }},
  { url: 'http://localhost:3000/api/tickets', method: 'GET', description: 'Tickets API', requiredPermissions: ['read:tickets'] },
  { url: 'http://localhost:3000/api/orders', method: 'GET', description: 'Orders API', requiredPermissions: ['read:orders'] },
];

console.log('Testing permission combinations');
console.log('---------------------------------------------------');

// Function to check if an API key has all required permissions
function hasRequiredPermissions(apiKey, requiredPermissions) {
  return requiredPermissions.every(permission => apiKey.permissions.includes(permission));
}

// Function to make a request with an API key
async function testEndpointWithKey(endpoint, apiKey) {
  try {
    console.log(`Testing: ${endpoint.description}`);
    console.log(`URL: ${endpoint.url}`);
    console.log(`Method: ${endpoint.method}`);
    console.log(`API Key: ${apiKey.name}`);
    console.log(`API Key Permissions: ${apiKey.permissions.join(', ')}`);
    console.log(`Required Permissions: ${endpoint.requiredPermissions.join(', ') || 'None (Public)'}`);
    
    const hasPermissions = hasRequiredPermissions(apiKey, endpoint.requiredPermissions);
    console.log(`Should have access: ${hasPermissions ? 'Yes' : 'No'}`);
    
    const options = {
      method: endpoint.method,
      headers: {
        'X-API-Key': apiKey.key,
        'Content-Type': 'application/json'
      }
    };
    
    if (endpoint.body) {
      options.body = JSON.stringify(endpoint.body);
    }
    
    const response = await fetch(endpoint.url, options);
    const status = response.status;
    
    let data;
    try {
      data = await response.json();
    } catch (e) {
      data = await response.text();
    }
    
    console.log(`Status: ${status}`);
    
    if (hasPermissions) {
      if (status === 200) {
        console.log('PASS: Has required permissions and received 200 status');
      } else if (status === 403) {
        console.log('FAIL: Has required permissions but received 403 Forbidden');
      } else {
        console.log(`UNEXPECTED: Has required permissions but received status ${status}`);
      }
    } else {
      if (endpoint.requiredPermissions.length === 0) {
        // Public endpoint
        if (status === 200) {
          console.log('PASS: Public endpoint accessible without permissions');
        } else {
          console.log(`FAIL: Public endpoint returned status ${status}`);
        }
      } else {
        // Protected endpoint without required permissions
        if (status === 403) {
          console.log('PASS: Missing required permissions and received 403 Forbidden');
        } else if (status === 200) {
          console.log('FAIL: Missing required permissions but received 200 status');
        } else {
          console.log(`UNEXPECTED: Missing required permissions but received status ${status}`);
        }
      }
    }
    
    console.log('---------------------------------------------------');
    
    return { 
      endpoint: endpoint.url, 
      apiKey: apiKey.name,
      hasRequiredPermissions: hasPermissions,
      status,
      success: (hasPermissions && status === 200) || 
               (!hasPermissions && endpoint.requiredPermissions.length > 0 && status === 403) ||
               (endpoint.requiredPermissions.length === 0 && status === 200)
    };
  } catch (error) {
    console.error(`Error testing endpoint ${endpoint.url} with key ${apiKey.name}: ${error.message}`);
    console.log('---------------------------------------------------');
    return { 
      endpoint: endpoint.url, 
      apiKey: apiKey.name,
      hasRequiredPermissions: hasRequiredPermissions(apiKey, endpoint.requiredPermissions),
      error: error.message, 
      success: false
    };
  }
}

// Function to delay execution
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Main function to run the tests
async function runTests() {
  console.log('Starting permission combination tests...');
  
  const results = [];
  
  for (const endpoint of endpoints) {
    for (const apiKey of apiKeys) {
      const result = await testEndpointWithKey(endpoint, apiKey);
      results.push(result);
      
      // Add a small delay between requests
      await delay(500);
    }
  }
  
  // Print summary
  console.log('Permission Combination Test Results:');
  console.log('---------------------------------------------------');
  
  const successCount = results.filter(r => r.success).length;
  const failureCount = results.length - successCount;
  
  console.log(`Total Tests: ${results.length}`);
  console.log(`Successful: ${successCount}`);
  console.log(`Failed: ${failureCount}`);
  
  if (failureCount > 0) {
    console.log('\nFailed Tests:');
    results.filter(r => !r.success).forEach(result => {
      console.log(`  - ${result.endpoint} with key ${result.apiKey}: ${result.hasRequiredPermissions ? 'Should have access' : 'Should not have access'}, got status ${result.status || 'error'}`);
    });
  }
  
  console.log('---------------------------------------------------');
  
  // Check for unexpected access
  const unexpectedAccess = results.filter(r => !r.hasRequiredPermissions && r.status === 200 && endpoint.requiredPermissions.length > 0);
  if (unexpectedAccess.length > 0) {
    console.log('\nSECURITY ISSUE: Unexpected Access:');
    unexpectedAccess.forEach(result => {
      console.log(`  - ${result.endpoint} accessible with key ${result.apiKey} without required permissions`);
    });
    console.log('---------------------------------------------------');
  }
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
});
