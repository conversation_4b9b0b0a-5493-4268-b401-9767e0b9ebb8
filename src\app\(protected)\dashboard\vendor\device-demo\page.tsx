'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { BluetoothDeviceScanner } from '@/components/bluetooth/BluetoothDeviceScanner';
import { NFCScanner } from '@/components/vendor/nfc/NFCScanner';
import { AlertCircle, Bluetooth, CreditCard, Info } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

export default function DeviceDemoPage() {
  const [selectedBluetoothDevice, setSelectedBluetoothDevice] = useState<any>(null);
  const [lastNfcCard, setLastNfcCard] = useState<string | null>(null);

  const handleBluetoothDeviceSelected = (device: any) => {
    setSelectedBluetoothDevice(device);
    toast({
      title: 'Device Selected',
      description: `Selected device: ${device.name}`,
    });
  };

  const handleNfcCardDetected = (cardId: string) => {
    setLastNfcCard(cardId);
    toast({
      title: 'NFC Card Detected',
      description: `Card ID: ${cardId}`,
    });
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Device API Demo</CardTitle>
          <CardDescription>
            Test Bluetooth and NFC functionality with proper user gesture handling
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert className="mb-6">
            <Info className="h-4 w-4" />
            <AlertTitle>Important Information</AlertTitle>
            <AlertDescription>
              Both Bluetooth and NFC APIs require user gestures (like button clicks) to request permissions.
              This demo shows the correct way to implement these features.
            </AlertDescription>
          </Alert>

          <Tabs defaultValue="bluetooth" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="bluetooth">
                <Bluetooth className="mr-2 h-4 w-4" />
                Bluetooth
              </TabsTrigger>
              <TabsTrigger value="nfc">
                <CreditCard className="mr-2 h-4 w-4" />
                NFC
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="bluetooth" className="space-y-4">
              <BluetoothDeviceScanner 
                onDeviceSelected={handleBluetoothDeviceSelected}
                title="Bluetooth Scanner"
                description="Click the button below to scan for Bluetooth devices"
              />
              
              {selectedBluetoothDevice && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Selected Device</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <p><span className="font-medium">Name:</span> {selectedBluetoothDevice.name}</p>
                      <p><span className="font-medium">ID:</span> {selectedBluetoothDevice.id}</p>
                      <p><span className="font-medium">Type:</span> {selectedBluetoothDevice.isPrinter ? 'Printer' : 'Other Device'}</p>
                      {selectedBluetoothDevice.info && (
                        <p><span className="font-medium">Info:</span> {selectedBluetoothDevice.info}</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
            
            <TabsContent value="nfc" className="space-y-4">
              <NFCScanner 
                onCardDetected={handleNfcCardDetected}
                title="NFC Card Scanner"
                description="Click the button below to start scanning for NFC cards"
              />
              
              {lastNfcCard && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Detected NFC Card</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <p><span className="font-medium">Card ID:</span> {lastNfcCard}</p>
                      <p><span className="font-medium">Detected At:</span> {new Date().toLocaleString()}</p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
      
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Common Errors</AlertTitle>
        <AlertDescription className="space-y-2">
          <p>If you see the error &quot;Failed to execute &apos;requestDevice&apos; on &apos;Bluetooth&apos;: Must be handling a user gesture to show a permission request&quot;, it means you&apos;re trying to call the Bluetooth API outside of a direct user gesture.</p>
          <p>The same applies to NFC - permissions must be requested in direct response to a user action.</p>
        </AlertDescription>
      </Alert>
    </div>
  );
}
