import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = await currentUser();

    // Check if user is admin or superadmin
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search') || '';
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Build the where clause for events that need review
    // We're looking for events with status 'Pending' or 'UnderReview'
    const where: any = {
      OR: [
        { status: 'Pending' },
        { status: 'UnderReview' }
      ]
    };

    if (search) {
      where.AND = [
        {
          OR: [
            { title: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
            { location: { contains: search, mode: 'insensitive' } },
            { venue: { contains: search, mode: 'insensitive' } }
          ]
        }
      ];
    }

    console.log('Fetching events for review with where clause:', JSON.stringify(where));

    // Find all events that need review with pagination
    const events = await db.event.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            emailVerified: true
          }
        },
        _count: {
          select: {
            orders: true,
            tickets: true
          }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip,
      take: limit
    });

    // Get total count for pagination
    const totalCount = await db.event.count({ where });

    console.log(`Found ${events.length} events for review out of ${totalCount} total`);

    // Format the events data
    const formattedEvents = await Promise.all(
      events.map(async (event) => {
        // Get engagement data
        const engagement = await db.engagement.findFirst({
          where: {
            eventId: event.id
          }
        }) || { views: 0, clicks: 0, shares: 0, likes: 0 };

        return {
          id: event.id,
          title: event.title,
          description: event.description || '',
          startDate: event.startDate.toISOString(),
          endDate: event.endDate.toISOString(),
          startTime: event.startTime,
          endTime: event.endTime,
          location: event.location,
          venue: event.venue,
          category: event.category,
          status: event.status,
          eventType: event.eventType,
          createdAt: event.createdAt.toISOString(),
          updatedAt: event.updatedAt.toISOString(),
          imagePath: event.imagePath || null,
          organizer: {
            id: event.user.id,
            name: event.user.name || 'Unknown',
            email: event.user.email || '',
            isVerified: event.user.emailVerified !== null
          },
          stats: {
            totalOrders: event._count.orders,
            totalTickets: event._count.tickets,
            engagement: {
              views: engagement.views,
              clicks: engagement.clicks,
              shares: engagement.shares,
              likes: engagement.likes
            }
          }
        };
      })
    );

    return NextResponse.json({
      events: formattedEvents,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching events for review:', error);
    return NextResponse.json(
      { error: 'Failed to fetch events for review' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const user = await currentUser();

    // Check if user is admin or superadmin
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the request body
    const body = await request.json();
    const { eventId, status, reviewNotes } = body;

    // Validate required fields
    if (!eventId || !status) {
      return NextResponse.json(
        { error: 'Event ID and status are required' },
        { status: 400 }
      );
    }

    // Verify status is valid
    const validStatuses = ['Draft', 'Pending', 'UnderReview', 'Approved', 'Published', 'Rejected', 'Cancelled', 'Completed'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: `Status must be one of: ${validStatuses.join(', ')}` },
        { status: 400 }
      );
    }

    // First, get the current event to access its metadata
    const currentEvent = await db.event.findUnique({
      where: { id: eventId },
      select: { metadata: true }
    });

    // Prepare the update data
    let updateData: any = { status };

    // Handle metadata updates properly
    if (status === 'Rejected') {
      // Merge with existing metadata instead of overwriting
      updateData.metadata = {
        ...currentEvent?.metadata,
        rejectionReason: reviewNotes
      };
    }

    // Update the event status
    const updatedEvent = await db.event.update({
      where: { id: eventId },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    // Skip creating event review record for now until the model is properly set up
    // We'll add this functionality back once the Prisma schema is updated and generated

    // For now, just log that we would create a review
    console.log('Would create/update event review:', {
      eventId,
      reviewerId: user.id,
      status: status === 'Published' ? 'APPROVED' : 'REJECTED',
      comments: reviewNotes || 'Event reviewed',
      reviewedAt: new Date(),
    });

    // Create notification for the organizer
    await db.notification.create({
      data: {
        userId: updatedEvent.user.id,
        message: status === 'Approved'
          ? `Your event "${updatedEvent.title}" has been approved. You can now publish it from your dashboard.`
          : `Your event "${updatedEvent.title}" has been rejected. ${reviewNotes ? `Reason: ${reviewNotes}` : ''}`,
        type: status === 'Approved' ? 'EVENT_APPROVED' : 'EVENT_REJECTED',
        isRead: false
      }
    });

    return NextResponse.json({
      success: true,
      event: {
        id: updatedEvent.id,
        title: updatedEvent.title,
        status: updatedEvent.status,
        organizer: {
          id: updatedEvent.user.id,
          name: updatedEvent.user.name,
          email: updatedEvent.user.email
        }
      }
    });
  } catch (error) {
    console.error('Error updating event status:', error);
    return NextResponse.json(
      { error: 'Failed to update event status' },
      { status: 500 }
    );
  }
}
