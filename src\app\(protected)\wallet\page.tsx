import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import { UnifiedWalletDashboard } from '@/components/wallet/unified-wallet-dashboard';

export const metadata = {
  title: 'Wallet | QuickTimeEvents',
  description: 'Manage your funds, transactions, and financial settings',
};

export default async function WalletPage() {
  const session = await getSession();

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  return (
    <div className="container mx-auto py-8">
      <UnifiedWalletDashboard 
        userId={session.user.id} 
        userRole={session.user.role || 'USER'} 
      />
    </div>
  );
}
