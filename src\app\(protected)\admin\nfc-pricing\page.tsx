'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Switch } from '@/components/ui/switch';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Save, Plus, CreditCard, Tag, Watch, X, DollarSign, AlertTriangle } from 'lucide-react';
import Image from 'next/image';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';

// Types
interface NFCProductPricing {
  id: string;
  deviceType: string;
  name: string;
  description: string | null;
  price: number;
  currency: string;
  isActive: boolean;
  imageUrl: string | null;
}

// New product form state
interface NewProductForm {
  name: string;
  description: string;
  deviceType: string;
  price: number;
  currency: string;
  imageUrl: string;
}

// Device type options
const deviceTypeOptions = [
  { value: 'CARD', label: 'Smart Card' },
  { value: 'FABRIC_WRISTBAND', label: 'Fabric Wristband' },
  { value: 'PAPER_WRISTBAND', label: 'Paper Wristband' },
  { value: 'SILICONE_WRISTBAND', label: 'Silicone Wristband' },
  { value: 'TAG', label: 'NFC Tag' }
];

export default function NFCPricingPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [products, setProducts] = useState<NFCProductPricing[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [editMode, setEditMode] = useState<Record<string, boolean>>({});

  // New product state
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [newProduct, setNewProduct] = useState<NewProductForm>({
    name: '',
    description: '',
    deviceType: '',
    price: 0,
    currency: 'USD',
    imageUrl: ''
  });

  // Redirect if not admin
  useEffect(() => {
    if (status === 'authenticated' && session?.user?.role !== 'ADMIN') {
      router.push('/dashboard');
      toast({
        title: 'Access Denied',
        description: 'You do not have permission to access this page.',
        variant: 'destructive',
      });
    }
  }, [status, session, router]);

  // Fetch NFC product pricing
  useEffect(() => {
    const fetchPricing = async () => {
      if (status !== 'authenticated' || session?.user?.role !== 'ADMIN') return;

      setIsLoading(true);

      try {
        const response = await fetch('/api/admin/nfc-pricing');

        if (response.ok) {
          const data = await response.json();
          setProducts(data);
        } else {
          throw new Error('Failed to fetch NFC pricing');
        }
      } catch (error) {
        console.error('Error fetching NFC pricing:', error);
        toast({
          title: 'Error',
          description: 'Failed to load NFC product pricing',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchPricing();
  }, [status, session]);

  // Toggle edit mode for a product
  const toggleEditMode = (productId: string) => {
    setEditMode(prev => ({
      ...prev,
      [productId]: !prev[productId]
    }));
  };

  // Update product field
  const updateProduct = (productId: string, field: string, value: any) => {
    setProducts(prev =>
      prev.map(product =>
        product.id === productId
          ? { ...product, [field]: value }
          : product
      )
    );
  };

  // Save product changes
  const saveProduct = async (productId: string) => {
    const product = products.find(p => p.id === productId);

    if (!product) return;

    setIsSaving(true);

    try {
      const response = await fetch(`/api/admin/nfc-pricing/${productId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: product.name,
          description: product.description,
          price: parseFloat(product.price.toString()),
          isActive: product.isActive,
          imageUrl: product.imageUrl
        }),
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'NFC product pricing updated successfully',
        });

        toggleEditMode(productId);
      } else {
        throw new Error('Failed to update NFC pricing');
      }
    } catch (error) {
      console.error('Error updating NFC pricing:', error);
      toast({
        title: 'Error',
        description: 'Failed to update NFC product pricing',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Create a new product
  const createProduct = async () => {
    if (!newProduct.name || !newProduct.deviceType || newProduct.price <= 0) {
      toast({
        title: 'Validation Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    setIsCreating(true);

    try {
      const response = await fetch('/api/admin/nfc-pricing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newProduct.name,
          description: newProduct.description,
          deviceType: newProduct.deviceType,
          price: parseFloat(newProduct.price.toString()),
          currency: newProduct.currency,
          imageUrl: newProduct.imageUrl || null,
          isActive: true
        }),
      });

      if (response.ok) {
        const createdProduct = await response.json();

        // Add the new product to the list
        setProducts(prev => [...prev, createdProduct]);

        toast({
          title: 'Success',
          description: 'NFC product created successfully',
        });

        // Reset form and close dialog
        setNewProduct({
          name: '',
          description: '',
          deviceType: '',
          price: 0,
          currency: 'USD',
          imageUrl: ''
        });
        setIsAddDialogOpen(false);
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create NFC product');
      }
    } catch (error) {
      console.error('Error creating NFC product:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create NFC product',
        variant: 'destructive',
      });
    } finally {
      setIsCreating(false);
    }
  };

  // Get icon for device type
  const getDeviceIcon = (type: string) => {
    switch (type) {
      case 'CARD':
        return <CreditCard className="h-5 w-5" />;
      case 'FABRIC_WRISTBAND':
      case 'PAPER_WRISTBAND':
      case 'SILICONE_WRISTBAND':
        return <Watch className="h-5 w-5" />;
      case 'TAG':
        return <Tag className="h-5 w-5" />;
      default:
        return <CreditCard className="h-5 w-5" />;
    }
  };

  // Format device type for display
  const formatDeviceType = (type: string) => {
    return type
      .replace('_', ' ')
      .split('_')
      .map(word => word.charAt(0) + word.slice(1).toLowerCase())
      .join(' ');
  };

  if (status === 'loading' || isLoading) {
    return (
      <div className="container mx-auto py-10 max-w-5xl">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading...</span>
        </div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    router.push('/auth/login?callbackUrl=/admin/nfc-pricing');
    return null;
  }

  if (session?.user?.role !== 'ADMIN') {
    return null; // Already redirecting in useEffect
  }

  return (
    <div className="container mx-auto py-10 max-w-5xl">
      <Card className="border-t-4 border-t-orange-500">
        <CardHeader className="bg-gradient-to-r from-orange-50 to-blue-50">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-blue-700">NFC Product Pricing</CardTitle>
              <CardDescription>
                Manage pricing for different NFC device types
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsAddDialogOpen(true)}
              className="border-orange-500 text-orange-600 hover:bg-orange-50"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add New Product
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]"></TableHead>
                <TableHead>Product</TableHead>
                <TableHead>Type</TableHead>
                <TableHead className="text-right">Price (USD)</TableHead>
                <TableHead className="text-center">Active</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {products.map((product) => (
                <TableRow key={product.id}>
                  <TableCell>
                    {getDeviceIcon(product.deviceType)}
                  </TableCell>
                  <TableCell>
                    {editMode[product.id] ? (
                      <div className="space-y-2">
                        <Input
                          value={product.name}
                          onChange={(e) => updateProduct(product.id, 'name', e.target.value)}
                          className="w-full"
                        />
                        <Input
                          value={product.description || ''}
                          onChange={(e) => updateProduct(product.id, 'description', e.target.value)}
                          className="w-full text-sm"
                          placeholder="Description"
                        />
                      </div>
                    ) : (
                      <div>
                        <div className="font-medium">{product.name}</div>
                        <div className="text-sm text-gray-500">{product.description}</div>
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    {formatDeviceType(product.deviceType)}
                  </TableCell>
                  <TableCell className="text-right">
                    {editMode[product.id] ? (
                      <Input
                        type="number"
                        value={product.price}
                        onChange={(e) => updateProduct(product.id, 'price', parseFloat(e.target.value) || 0)}
                        className="w-24 text-right ml-auto"
                        min="0"
                        step="0.01"
                      />
                    ) : (
                      <div className="font-medium">${product.price.toFixed(2)}</div>
                    )}
                  </TableCell>
                  <TableCell className="text-center">
                    <Switch
                      checked={product.isActive}
                      onCheckedChange={(checked) => updateProduct(product.id, 'isActive', checked)}
                      disabled={!editMode[product.id]}
                      className="data-[state=checked]:bg-orange-500"
                    />
                  </TableCell>
                  <TableCell className="text-right">
                    {editMode[product.id] ? (
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() => saveProduct(product.id)}
                        disabled={isSaving}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        {isSaving ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <Save className="h-4 w-4 mr-2" />
                        )}
                        Save
                      </Button>
                    ) : (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toggleEditMode(product.id)}
                        className="border-orange-500 text-orange-600 hover:bg-orange-50"
                      >
                        Edit
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter className="flex justify-between bg-gradient-to-r from-blue-50 to-orange-50">
          <Button
            variant="outline"
            onClick={() => router.push('/admin')}
            className="border-blue-500 text-blue-600 hover:bg-blue-50"
          >
            Back to Admin Dashboard
          </Button>
          <Button
            onClick={() => router.push('/nfc-store')}
            className="bg-orange-600 hover:bg-orange-700"
          >
            View NFC Store
          </Button>
        </CardFooter>
      </Card>

      {/* Add New Product Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px] border-t-4 border-t-orange-500 border-b-4 border-b-blue-500">
          <DialogHeader className="bg-gradient-to-r from-orange-50 to-blue-50 rounded-t-lg pb-4">
            <DialogTitle className="text-blue-700 text-xl">Add New NFC Product</DialogTitle>
            <DialogDescription>
              Create a new NFC product with pricing information.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="deviceType" className="text-right">
                Device Type <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                <Select
                  value={newProduct.deviceType}
                  onValueChange={(value) => setNewProduct({...newProduct, deviceType: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select device type" />
                  </SelectTrigger>
                  <SelectContent>
                    {deviceTypeOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                value={newProduct.name}
                onChange={(e) => setNewProduct({...newProduct, name: e.target.value})}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <Textarea
                id="description"
                value={newProduct.description}
                onChange={(e) => setNewProduct({...newProduct, description: e.target.value})}
                className="col-span-3"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="price" className="text-right">
                Price <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3 flex items-center">
                <DollarSign className="h-4 w-4 mr-1 text-gray-500" />
                <Input
                  id="price"
                  type="number"
                  value={newProduct.price}
                  onChange={(e) => setNewProduct({...newProduct, price: parseFloat(e.target.value) || 0})}
                  min="0"
                  step="0.01"
                />
                <Select
                  value={newProduct.currency}
                  onValueChange={(value) => setNewProduct({...newProduct, currency: value})}
                  defaultValue="USD"
                >
                  <SelectTrigger className="w-24 ml-2">
                    <SelectValue placeholder="USD" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD</SelectItem>
                    <SelectItem value="EUR">EUR</SelectItem>
                    <SelectItem value="GBP">GBP</SelectItem>
                    <SelectItem value="ZMW">ZMW</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="imageUrl" className="text-right">
                Image URL
              </Label>
              <Input
                id="imageUrl"
                value={newProduct.imageUrl}
                onChange={(e) => setNewProduct({...newProduct, imageUrl: e.target.value})}
                className="col-span-3"
                placeholder="https://example.com/image.jpg"
              />
            </div>

            {!newProduct.deviceType || !newProduct.name || newProduct.price <= 0 ? (
              <div className="flex items-center gap-2 text-orange-600 bg-orange-50 p-2 rounded-md">
                <AlertTriangle className="h-4 w-4" />
                <span className="text-sm">Please fill in all required fields marked with *</span>
              </div>
            ) : null}
          </div>

          <DialogFooter className="bg-gradient-to-r from-blue-50 to-orange-50 rounded-b-lg pt-4">
            <Button
              variant="outline"
              onClick={() => setIsAddDialogOpen(false)}
              className="border-orange-500 text-orange-600 hover:bg-orange-50"
            >
              Cancel
            </Button>
            <Button
              onClick={createProduct}
              disabled={isCreating || !newProduct.deviceType || !newProduct.name || newProduct.price <= 0}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isCreating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Creating...
                </>
              ) : (
                'Create Product'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
