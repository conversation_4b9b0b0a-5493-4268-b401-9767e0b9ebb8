{"name": "quicktimeevents", "version": "0.1.0", "private": true, "scripts": {"dev": "npm run db:ensure-seeded && next dev", "dev:turbo": "npm run db:ensure-seeded && next dev --turbo", "dev:clean": "npm run db:ensure-seeded && next dev", "clean": "node scripts/clean-turbo.js", "clean:full": "node scripts/clean-turbo.js --full", "clean:simple": "rimraf .next-turbo .turbo", "build": "next build", "build:clean": "npm run clean:simple && next build", "optimize-images": "node scripts/optimize-images.js", "build:optimized-images": "npm run optimize-images && next build", "build:optimized": "NODE_ENV=production next build && node scripts/optimize-build.js", "build:analyze": "ANALYZE=true NODE_OPTIONS=--max_old_space_size=4096 next build -c next.config.analyzer.js", "start": "next start", "build:local": "env-cmd -f .env.local next build", "build:prod": "env-cmd -f .env.production next build", "start:prod": "NODE_ENV=production next start", "lint": "next lint", "postinstall": "prisma generate", "deploy:prod": "node scripts/deploy-production.js", "analyze": "ANALYZE=true NODE_OPTIONS=--max_old_space_size=4096 next build -c next.config.analyzer.js", "db:seed": "ts-node --compiler-options '{\"module\":\"CommonJS\"}' prisma/seed.ts", "prisma:migrate": "prisma migrate deploy", "prisma:seed": "prisma db seed", "seed:admin": "node scripts/seed-admin.js", "seed:api-tiers": "node scripts/seed-api-tiers.js", "prisma:generate": "prisma generate", "seed:notifications": "node scripts/generate-test-notifications.js", "delete:notifications": "node scripts/delete-test-notifications.js", "setup:nfc": "node scripts/setup-nfc-system.js", "seed:withdrawals": "ts-node --compiler-options '{\"module\":\"CommonJS\"}' prisma/seed-withdrawals.ts", "seed:nfc": "ts-node --compiler-options '{\"module\":\"CommonJS\"}' prisma/seed-nfc.ts", "db:setup": "prisma migrate deploy && prisma db seed", "db:ensure-seeded": "node scripts/ensure-database-seeded.js"}, "dependencies": {"@auth/prisma-adapter": "^2.8.0", "@faker-js/faker": "^9.7.0", "@googlemaps/js-api-loader": "^1.16.8", "@hookform/resolvers": "^3.3.4", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@react-google-maps/api": "^2.20.6", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.1.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tanstack/react-query": "^5.71.5", "@tanstack/react-query-devtools": "^5.71.5", "@tanstack/react-router": "^1.114.34", "@types/nodemailer": "^6.4.17", "@types/react-helmet": "^6.1.11", "ai": "^3.2.22", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.6.3", "jsonwebtoken": "^9.0.2", "libphonenumber-js": "^1.12.6", "lodash": "^4.17.21", "lucide-react": "^0.400.0", "moment": "^2.30.1", "multer": "^1.4.5-lts.1", "next": "15.2.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "oidc-token-hash": "^5.0.3", "pdf-lib": "^1.17.1", "popover": "^2.4.1", "qrcode": "^1.5.4", "react": "19.1.0", "react-day-picker": "^9.6.6", "react-dom": "19.1.0", "react-error-boundary": "^6.0.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.51.3", "react-icons": "^5.1.0", "react-intersection-observer": "^9.10.3", "react-qr-code": "^2.0.15", "react-spinners": "^0.15.0", "react-syntax-highlighter": "^15.6.1", "react-table": "^7.8.0", "recharts": "^2.15.2", "resend": "^3.5.0", "sharp": "^0.33.4", "socket.io": "^4.8.1", "sonner": "^2.0.3", "stripe": "^18.0.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "toast": "^0.5.4", "ts-pattern": "^5.2.0", "uuid": "^11.1.0", "ws": "^8.18.1", "yup": "^1.4.0", "zod": "^3.24.2"}, "devDependencies": {"@next/bundle-analyzer": "^15.2.4", "@tanstack/eslint-plugin-query": "^5.52.0", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/google.maps": "^3.55.12", "@types/jsonwebtoken": "^9.0.6", "@types/lodash": "^4.17.6", "@types/multer": "^1.4.11", "@types/node": "^20.14.9", "@types/qrcode": "^1.5.5", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "@types/react-table": "^7.7.20", "@types/slate": "^0.47.15", "@types/uuid": "^10.0.0", "eslint": "^8", "eslint-config-next": "15.2.4", "postcss": "^8", "prisma": "^6.8.2", "rimraf": "^6.0.1", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.5.2"}, "prisma": {"seed": "node -r ts-node/register prisma/seed.ts"}, "overrides": {"@types/react": "19.1.0", "@types/react-dom": "19.1.1", "@react-google-maps/api": {"react": "^16.8 || ^17 || ^18 || ^19", "react-dom": "^16.8 || ^17 || ^18 || ^19"}, "next-themes": {"react": "^16.8 || ^17 || ^18 || ^19", "react-dom": "^16.8 || ^17 || ^18 || ^19"}, "react-day-picker": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "react-spinners": {"react": "^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "react-table": {"react": "^16.8.3 || ^17.0.0-0 || ^18.0.0 || ^19.0.0"}, "recharts": {"react": "^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "sonner": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}}}