import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { partnerRedirectMiddleware } from './middleware/partner-redirect';
import { eventUrlRedirectMiddleware } from './middleware/event-url-redirect';

// Note: We can't use Prisma in middleware (Edge runtime)
// Database initialization will be handled in a server component or API route instead

export function middleware(request: NextRequest) {
  // First, check if this is a partner trying to access the general dashboard
  const partnerRedirect = partnerRedirectMiddleware(request);
  if (partnerRedirect) {
    return partnerRedirect;
  }

  // Check for event URL redirects (old format to new format)
  const eventRedirect = eventUrlRedirectMiddleware(request);
  if (eventRedirect && eventRedirect.status !== 200) {
    return eventRedirect;
  }

  // Simple middleware that just adds CORS headers
  const response = NextResponse.next();

  // Add CORS headers
  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key, Cache-Control');

  return response;
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico).*)', // Apply middleware to all routes except static assets
  ],
};