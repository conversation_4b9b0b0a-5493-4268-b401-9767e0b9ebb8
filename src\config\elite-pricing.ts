import { EliteTier } from '@prisma/client';
import { ElitePricingTier } from '@/types/elite-communication';

export const ELITE_PRICING_TIERS: ElitePricingTier[] = [
  {
    tier: EliteTier.BASIC,
    name: 'Basic',
    description: 'View attendee list and basic networking',
    features: [
      'View attendee list',
      'See public profiles',
      'Basic event information',
      'Standard support'
    ],
    pricePerEvent: 0,
    popular: false
  },
  {
    tier: EliteTier.ELITE,
    name: 'Elite',
    description: 'Direct messaging and enhanced networking',
    features: [
      'Everything in Basic',
      'Direct messaging with attendees',
      'Access contact information',
      'Advanced profile visibility',
      'Message history',
      'Priority support'
    ],
    pricePerEvent: 29.99,
    popular: true
  },
  {
    tier: EliteTier.ELITE_PRO,
    name: 'Elite Pro',
    description: 'Complete networking suite with exclusive features',
    features: [
      'Everything in Elite',
      'Exclusive Elite Pro chat rooms',
      'One-on-one meeting scheduling',
      'Video call integration',
      'Advanced attendee matching',
      'Calendar integration',
      'File sharing capabilities',
      'Premium support'
    ],
    pricePerEvent: 59.99,
    monthlyPrice: 149.99,
    annualPrice: 1499.99,
    popular: false
  }
];

export const ELITE_FEATURE_MATRIX = {
  [EliteTier.BASIC]: {
    viewAttendeeList: true,
    viewPublicProfiles: true,
    directMessaging: false,
    accessContactInfo: false,
    exclusiveChatRooms: false,
    meetingScheduling: false,
    videoCallIntegration: false,
    fileSharing: false,
    advancedMatching: false,
    calendarIntegration: false
  },
  [EliteTier.ELITE]: {
    viewAttendeeList: true,
    viewPublicProfiles: true,
    directMessaging: true,
    accessContactInfo: true,
    exclusiveChatRooms: false,
    meetingScheduling: false,
    videoCallIntegration: false,
    fileSharing: true,
    advancedMatching: false,
    calendarIntegration: false
  },
  [EliteTier.ELITE_PRO]: {
    viewAttendeeList: true,
    viewPublicProfiles: true,
    directMessaging: true,
    accessContactInfo: true,
    exclusiveChatRooms: true,
    meetingScheduling: true,
    videoCallIntegration: true,
    fileSharing: true,
    advancedMatching: true,
    calendarIntegration: true
  }
};

export const getElitePricingTier = (tier: EliteTier): ElitePricingTier => {
  return ELITE_PRICING_TIERS.find(t => t.tier === tier) || ELITE_PRICING_TIERS[0];
};

export const hasFeature = (tier: EliteTier, feature: keyof typeof ELITE_FEATURE_MATRIX[EliteTier.BASIC]): boolean => {
  return ELITE_FEATURE_MATRIX[tier]?.[feature] || false;
};

export const getUpgradePrice = (currentTier: EliteTier, targetTier: EliteTier): number => {
  const current = getElitePricingTier(currentTier);
  const target = getElitePricingTier(targetTier);
  
  return (target.pricePerEvent || 0) - (current.pricePerEvent || 0);
};

// Stripe Price IDs (to be configured in Stripe Dashboard)
export const STRIPE_PRICE_IDS = {
  [EliteTier.ELITE]: {
    perEvent: process.env.STRIPE_ELITE_PER_EVENT_PRICE_ID,
  },
  [EliteTier.ELITE_PRO]: {
    perEvent: process.env.STRIPE_ELITE_PRO_PER_EVENT_PRICE_ID,
    monthly: process.env.STRIPE_ELITE_PRO_MONTHLY_PRICE_ID,
    annual: process.env.STRIPE_ELITE_PRO_ANNUAL_PRICE_ID,
  }
};

export const ELITE_COMMUNICATION_LIMITS = {
  [EliteTier.BASIC]: {
    messagesPerDay: 0,
    meetingRequestsPerDay: 0,
    fileUploadSizeMB: 0,
    chatRoomAccess: []
  },
  [EliteTier.ELITE]: {
    messagesPerDay: 100,
    meetingRequestsPerDay: 0,
    fileUploadSizeMB: 10,
    chatRoomAccess: ['GENERAL']
  },
  [EliteTier.ELITE_PRO]: {
    messagesPerDay: 500,
    meetingRequestsPerDay: 20,
    fileUploadSizeMB: 50,
    chatRoomAccess: ['GENERAL', 'ELITE_EXCLUSIVE', 'ELITE_PRO_EXCLUSIVE']
  }
};
