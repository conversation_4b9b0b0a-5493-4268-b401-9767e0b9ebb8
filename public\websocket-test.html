<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WebSocket Test</title>
  <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .status {
      padding: 10px;
      margin-bottom: 20px;
      border-radius: 4px;
    }
    .connected {
      background-color: #d4edda;
      color: #155724;
    }
    .disconnected {
      background-color: #f8d7da;
      color: #721c24;
    }
    .transaction {
      border: 1px solid #ddd;
      padding: 10px;
      margin-bottom: 10px;
      border-radius: 4px;
    }
    .transaction-completed {
      border-left: 5px solid #28a745;
    }
    .transaction-pending {
      border-left: 5px solid #ffc107;
    }
    .transaction-failed {
      border-left: 5px solid #dc3545;
    }
    .metrics {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
    }
    .metric {
      flex: 1;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 4px;
      text-align: center;
    }
    .metric h3 {
      margin-top: 0;
    }
    .metric p {
      font-size: 24px;
      font-weight: bold;
      margin: 10px 0 0;
    }
    button {
      padding: 8px 16px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #0069d9;
    }
    #log {
      height: 200px;
      overflow-y: auto;
      border: 1px solid #ddd;
      padding: 10px;
      margin-top: 20px;
      font-family: monospace;
      background-color: #f8f9fa;
    }
  </style>
</head>
<body>
  <h1>WebSocket Test</h1>
  
  <div id="status" class="status disconnected">Disconnected</div>
  
  <div class="metrics">
    <div class="metric">
      <h3>Active Users</h3>
      <p id="activeUsers">0</p>
    </div>
    <div class="metric">
      <h3>Transactions/min</h3>
      <p id="transactionsPerMinute">0</p>
    </div>
  </div>
  
  <h2>Transactions</h2>
  <div id="transactions"></div>
  
  <h2>Connection</h2>
  <button id="connect">Connect</button>
  <button id="disconnect">Disconnect</button>
  
  <h2>Test Transaction</h2>
  <button id="sendTransaction">Send Test Transaction</button>
  
  <h2>Log</h2>
  <div id="log"></div>
  
  <script>
    // DOM elements
    const statusEl = document.getElementById('status');
    const activeUsersEl = document.getElementById('activeUsers');
    const transactionsPerMinuteEl = document.getElementById('transactionsPerMinute');
    const transactionsEl = document.getElementById('transactions');
    const connectBtn = document.getElementById('connect');
    const disconnectBtn = document.getElementById('disconnect');
    const sendTransactionBtn = document.getElementById('sendTransaction');
    const logEl = document.getElementById('log');
    
    // Socket.IO instance
    let socket = null;
    
    // Log function
    function log(message) {
      const now = new Date();
      const timestamp = now.toLocaleTimeString();
      logEl.innerHTML += `<div>[${timestamp}] ${message}</div>`;
      logEl.scrollTop = logEl.scrollHeight;
    }
    
    // Format currency
    function formatCurrency(value) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2
      }).format(value);
    }
    
    // Connect to WebSocket
    function connect() {
      if (socket) {
        log('Already connected');
        return;
      }
      
      log('Connecting to WebSocket...');
      
      // Create Socket.IO instance
      socket = io({
        path: '/api/socket',
        auth: {
          token: null // No authentication for this test
        }
      });
      
      // Connection event
      socket.on('connect', () => {
        log('Connected to WebSocket');
        statusEl.textContent = 'Connected';
        statusEl.className = 'status connected';
      });
      
      // Disconnection event
      socket.on('disconnect', (reason) => {
        log(`Disconnected: ${reason}`);
        statusEl.textContent = 'Disconnected';
        statusEl.className = 'status disconnected';
      });
      
      // Connection error event
      socket.on('connect_error', (error) => {
        log(`Connection error: ${error.message}`);
        statusEl.textContent = `Connection error: ${error.message}`;
        statusEl.className = 'status disconnected';
      });
      
      // Transaction event
      socket.on('nfc_transaction', (data) => {
        log(`Received transaction: ${JSON.stringify(data)}`);
        
        // Create transaction element
        const transactionEl = document.createElement('div');
        transactionEl.className = `transaction transaction-${data.status.toLowerCase()}`;
        transactionEl.innerHTML = `
          <div><strong>${data.eventName}</strong></div>
          <div>${data.customerName || 'Customer'}</div>
          <div>${formatCurrency(data.amount)}</div>
          <div><small>${new Date(data.timestamp).toLocaleTimeString()}</small></div>
          <div><span class="badge">${data.status}</span></div>
        `;
        
        // Add to transactions container
        transactionsEl.prepend(transactionEl);
        
        // Limit to 10 transactions
        if (transactionsEl.children.length > 10) {
          transactionsEl.removeChild(transactionsEl.lastChild);
        }
      });
      
      // Metrics update event
      socket.on('metrics_update', (data) => {
        log(`Received metrics update: ${JSON.stringify(data)}`);
        
        // Update metrics
        activeUsersEl.textContent = data.concurrentUsers;
        transactionsPerMinuteEl.textContent = data.transactionsPerMinute;
      });
    }
    
    // Disconnect from WebSocket
    function disconnect() {
      if (!socket) {
        log('Not connected');
        return;
      }
      
      log('Disconnecting from WebSocket...');
      socket.disconnect();
      socket = null;
    }
    
    // Send test transaction
    function sendTestTransaction() {
      if (!socket) {
        log('Not connected');
        return;
      }
      
      // Create test transaction
      const transaction = {
        id: Math.random().toString(36).substring(2, 15),
        eventName: 'Test Event',
        amount: Math.floor(Math.random() * 10000) / 100,
        status: ['COMPLETED', 'PENDING', 'FAILED'][Math.floor(Math.random() * 3)],
        timestamp: new Date().toISOString(),
        customerName: 'Test Customer',
        vendorId: 'test-vendor'
      };
      
      log(`Sending test transaction: ${JSON.stringify(transaction)}`);
      
      // Send test transaction via fetch API
      fetch('/api/vendor/nfc/transaction', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(transaction)
      })
      .then(response => response.json())
      .then(data => {
        log(`Transaction response: ${JSON.stringify(data)}`);
      })
      .catch(error => {
        log(`Transaction error: ${error.message}`);
      });
    }
    
    // Event listeners
    connectBtn.addEventListener('click', connect);
    disconnectBtn.addEventListener('click', disconnect);
    sendTransactionBtn.addEventListener('click', sendTestTransaction);
    
    // Auto-connect on page load
    window.addEventListener('load', connect);
  </script>
</body>
</html>
