import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { parseEventUrl } from '@/lib/utils/events';

export const dynamic = 'force-dynamic';

/**
 * GET /api/events/by-url?url=[url]
 * Get event details by parsing a URL (supports both old and new formats)
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const url = searchParams.get('url');

    if (!url) {
      return NextResponse.json(
        { error: 'URL parameter is required' },
        { status: 400 }
      );
    }

    // Parse the URL to extract event information
    const parsedUrl = parseEventUrl(url);
    
    if (!parsedUrl) {
      return NextResponse.json(
        { error: 'Invalid URL format' },
        { status: 400 }
      );
    }

    const { id, category, titleSlug, isLegacyUrl } = parsedUrl;

    // Find the event
    const event = await db.event.findUnique({
      where: { 
        id,
        status: 'Published'
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        tickets: true,
        ageRestriction: true,
        ParkingManagement: true,
        seoSettings: true,
        socialSettings: true,
        eventPartners: {
          where: {
            isActive: true,
          },
          include: {
            partner: {
              select: {
                id: true,
                businessName: true,
                partnerType: true,
                logo: true,
                city: true,
                rating: true,
                totalReviews: true,
                acceptsNfcPayments: true,
              },
            },
          },
        },
      },
    });

    if (!event) {
      return NextResponse.json(
        { error: 'Event not found or not published' },
        { status: 404 }
      );
    }

    // If this is a new format URL, validate that the category and title match
    if (!isLegacyUrl && category && titleSlug) {
      // Check if the category matches
      if (event.category !== category) {
        return NextResponse.json(
          { error: 'URL category does not match event category' },
          { status: 400 }
        );
      }

      // Generate the expected title slug and compare
      const expectedTitleSlug = event.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)+/g, '');

      if (titleSlug !== expectedTitleSlug) {
        return NextResponse.json(
          { error: 'URL title does not match event title' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json({
      event,
      urlInfo: {
        isLegacyUrl,
        parsedUrl
      }
    });

  } catch (error) {
    console.error('Error fetching event by URL:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
