#!/usr/bin/env node

/**
 * Database Seeding Automation Script
 * 
 * This script ensures the database is properly seeded before starting the development server.
 * It runs automatically when you start the development server with `npm run dev`.
 * 
 * Features:
 * - Checks if database migrations are applied
 * - Runs database seeding if needed
 * - Provides clear console output with emojis
 * - Handles errors gracefully without breaking the startup process
 * - Includes Elite Communication System seeding
 */

const { execSync, spawn } = require('child_process');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logHeader(message) {
  log(`\n🚀 ${message}`, colors.cyan + colors.bright);
}

async function checkDatabaseConnection() {
  try {
    logInfo('Checking database connection...');
    execSync('npx prisma db execute --stdin < /dev/null', {
      stdio: 'pipe',
      timeout: 10000
    });
    logSuccess('Database connection established');
    return true;
  } catch (error) {
    logWarning('Database connection failed');
    logInfo('This is normal if you haven\'t started your database yet');
    logInfo('💡 To start your database:');
    logInfo('   • If using Docker: docker-compose up -d');
    logInfo('   • If using local PostgreSQL: ensure PostgreSQL service is running');
    logInfo('   • Check your DATABASE_URL in .env file');
    logInfo('');
    logInfo('🚀 Starting development server anyway...');
    logInfo('   The database will be seeded automatically when you first access the app');
    return false;
  }
}

async function checkMigrations() {
  try {
    logInfo('Checking database migrations...');
    const output = execSync('npx prisma migrate status', { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    if (output.includes('Database schema is up to date')) {
      logSuccess('All migrations are applied');
      return true;
    } else if (output.includes('Following migrations have not yet been applied')) {
      logWarning('Pending migrations found. Applying migrations...');
      execSync('npx prisma migrate deploy', { stdio: 'inherit' });
      logSuccess('Migrations applied successfully');
      return true;
    }
    
    return true;
  } catch (error) {
    logError('Migration check failed');
    logWarning('Attempting to apply migrations...');
    
    try {
      execSync('npx prisma migrate deploy', { stdio: 'inherit' });
      logSuccess('Migrations applied successfully');
      return true;
    } catch (migrateError) {
      logError('Failed to apply migrations');
      return false;
    }
  }
}

async function runSeeding() {
  try {
    logInfo('Running database seeding...');
    logInfo('This includes Elite Communication System with sample users, events, and networking data');

    // Use the existing Prisma seed command which includes Elite Communication seeding
    execSync('npx prisma db seed', {
      stdio: 'inherit',
      timeout: 120000 // 2 minutes timeout
    });

    logSuccess('Database seeding completed successfully');
    logSuccess('Elite Communication System is ready with sample data');
    return true;
  } catch (error) {
    logError(`Seeding failed: ${error.message}`);

    // Try alternative seeding method
    try {
      logInfo('Trying alternative seeding method...');
      execSync('npx tsx prisma/seed.ts', {
        stdio: 'inherit',
        timeout: 120000
      });
      logSuccess('Alternative seeding completed successfully');
      return true;
    } catch (altError) {
      logError(`Alternative seeding also failed: ${altError.message}`);
      return false;
    }
  }
}

async function main() {
  logHeader('Elite Communication System - Database Initialization');
  
  console.log(`
${colors.cyan}╔══════════════════════════════════════════════════════════════╗
║                    🎯 DEVELOPMENT SETUP                      ║
║                                                              ║
║  Ensuring your database is ready with:                      ║
║  • Admin users and system configurations                    ║
║  • Elite Communication System sample data                   ║
║  • 10 professional users across diverse industries          ║
║  • 3 networking events (Tech, Finance, Healthcare)          ║
║  • Elite/Elite Pro subscriptions and chat rooms             ║
║  • Sample messages and meeting requests                     ║
║                                                              ║
║  This runs automatically on 'npm run dev' 🚀                ║
╚══════════════════════════════════════════════════════════════╝${colors.reset}
  `);

  try {
    // Check database connection
    const dbConnected = await checkDatabaseConnection();
    if (!dbConnected) {
      logWarning('Skipping database seeding due to connection issues');
      logInfo('The Elite Communication System will be seeded when you first access the app');
      logSuccess('Development server will start normally');
      return;
    }

    // Check and apply migrations
    const migrationsOk = await checkMigrations();
    if (!migrationsOk) {
      logWarning('Migration issues detected, but continuing...');
      logInfo('Migrations will be applied automatically when the app starts');
    }

    // Run seeding
    const seedingOk = await runSeeding();
    if (!seedingOk) {
      logWarning('Seeding failed, but continuing with development server startup');
      logWarning('You can manually run seeding later with: npm run prisma:seed');
    }

    logSuccess('Database initialization completed!');
    logInfo('Starting development server...');

  } catch (error) {
    logWarning(`Initialization encountered issues: ${error.message}`);
    logInfo('Continuing with development server startup...');
    logInfo('Database seeding will happen automatically when you access the app');
  }
}

// Handle process termination gracefully
process.on('SIGINT', () => {
  logInfo('Database initialization interrupted');
  process.exit(0);
});

process.on('SIGTERM', () => {
  logInfo('Database initialization terminated');
  process.exit(0);
});

// Run the main function
main().catch((error) => {
  logError(`Unexpected error: ${error.message}`);
  process.exit(1);
});
