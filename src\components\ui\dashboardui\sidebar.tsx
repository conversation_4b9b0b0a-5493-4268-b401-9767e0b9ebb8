"use client";
import React, { useState, useEffect } from "react";
import { UserButton } from "@/components/auth/user-button";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import Image from "next/image";
import logo from "../../../../public/images/quick.png";
import { OrderTicketCounts } from "./order-ticket-counts";
import {
  CalendarDays,
  Users,
  Ticket,
  Mail,
  Tags,
  MapPin,
  Briefcase,
  TrendingUp,
  Settings,
  Menu,
  X,
  PlusCircle,
  LayoutDashboard,
  LayoutGrid,
  Send,
  BarChart3,
  Box,
  CreditCard,
  ShoppingCart,
  ChevronRight,
  ChevronDown,
  LogOut,
  User,
  Bell,
  HelpCircle,
  Home,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  AlertTriangle,
  DollarSign,
  Wallet,
  Key,
  Star,
  Share,
  Share2,
  Calendar,
  Link as LinkIcon,
  Shuffle,
  UserPlus,
  History,
  Tag,
  Store,
  WifiOff,
  Bluetooth,
  Percent,
  Hotel,
  Utensils,
  Award,
} from "lucide-react";
import { useCurrentRole } from "@/hooks/use-current-role";
import { cn } from "@/lib/utils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

type Role = "ADMIN" | "USER" | "ORGANIZER" | "VENDOR" | "SUPERADMIN" | "DEVELOPER" | "PARTNER";

interface NavItem {
  href: string;
  label: string;
  icon: React.ReactNode;
  badge?: string | React.ReactNode;
  badgeColor?: string;
  children?: NavItem[];
}

type NavItems = {
  [K in Role]: NavItem[];
};

export function Sidebar() {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const role = useCurrentRole() as Role;

  const toggleSidebar = () => setIsOpen(!isOpen);

  const navItems: NavItems = {
    USER: [
      { href: "/dashboard/user", label: "Dashboard", icon: <LayoutDashboard size={20} /> },
      { href: "/dashboard/user/browse-events", label: "Browse Events", icon: <CalendarDays size={20} />,  },
      { href: "/partners", label: "Partners", icon: <Hotel size={20} /> },
      { href: "/dashboard/user/orders", label: "My Orders", icon: <ShoppingCart size={20} /> },
      { href: "/my-tickets", label: "My Tickets", icon: <Ticket size={20} /> },
      {
        href: "/nfc-store",
        label: "NFC Devices",
        icon: <CreditCard size={20} />,
        children: [
          { href: "/nfc-store", label: "Buy NFC Devices", icon: <ShoppingCart size={20} /> },
          { href: "/nfc-topup", label: "Top Up Balance", icon: <Wallet size={20} /> },
          { href: "/my-tickets?tab=nfc", label: "Manage Devices", icon: <Settings size={20} /> },
        ]
      },
      { href: "/dashboard/wallet", label: "Wallet", icon: <Wallet size={20} />,  },
      { href: "/dashboard/user/profile", label: "Profile", icon: <User size={20} /> },
      { href: "/dashboard/user/settings", label: "Settings", icon: <Settings size={20} /> },
    ],
    ADMIN: [
      { href: "/dashboard/admin", label: "Dashboard", icon: <LayoutDashboard size={20} /> },
      { href: "/dashboard/admin/users", label: "Users", icon: <Users size={20} /> },
      { href: "/dashboard/admin/organizers", label: "Organizers", icon: <Briefcase size={20} />,  },
      { href: "/dashboard/admin/verifications", label: "Verifications", icon: <CheckCircle size={20} />, },
      {
        href: "/dashboard/admin/teams",
        label: "Teams",
        icon: <UserPlus size={20} />,
        children: [
          { href: "/dashboard/admin/teams", label: "All Teams", icon: <Users size={20} /> },
          { href: "/dashboard/admin/teams/create", label: "Create Team", icon: <PlusCircle size={20} /> },
        ]
      },
       {
        href: "/admin/finance",
        label: "Finance",
        icon: <DollarSign size={20} />,
        children: [
          { href: "/admin/finance/dashboard", label: "Dashboard", icon: <LayoutDashboard size={20} /> },
          { href: "/admin/finance/fees", label: "Fee Configuration", icon: <Percent size={20} /> },
          { href: "/admin/finance/subscription-tiers", label: "Subscription Tiers", icon: <Star size={20} /> },
          { href: "/admin/finance/pos-devices", label: "POS Devices", icon: <CreditCard size={20} /> },
          { href: "/admin/finance/transactions", label: "Transactions", icon: <History size={20} /> },
          { href: "/admin/finance/withdrawals", label: "Withdrawals", icon: <Wallet size={20} />,  },
        ]
      },
       {
        href: "/admin/nfc",
        label: "NFC System",
        icon: <CreditCard size={20} />,
        children: [
          { href: "/admin/nfc/dashboard", label: "System Overview", icon: <LayoutDashboard size={20} /> },
          { href: "/admin/nfc/transactions", label: "All Transactions", icon: <History size={20} /> },
          { href: "/admin/nfc/vendors", label: "Vendor Terminals", icon: <Store size={20} /> },
          { href: "/admin/nfc/cards", label: "Card Inventory", icon: <CreditCard size={20} /> },
          { href: "/dashboard/admin/nfc-pricing", label: "NFC Device Pricing", icon: <DollarSign size={20} /> },
          { href: "/admin/nfc/analytics", label: "System Analytics", icon: <BarChart3 size={20} /> },
          { href: "/admin/nfc/settings", label: "Global Settings", icon: <Settings size={20} /> },
        ]
      },
      {
        href: "/admin/events", // Corrected parent path
        label: "Events",
        icon: <CalendarDays size={20} />,
        children: [
          { href: "/admin/events", label: "All Events", icon: <CalendarDays size={20} /> },
          { href: "/admin/events/review", label: "Review Events", icon: <CheckCircle size={20} /> },
        ]
      },
      {
        href: "/dashboard/admin/partners", // Keep within dashboard
        label: "Partners",
        icon: <Hotel size={20} />,
        children: [
          { href: "/dashboard/admin/partners", label: "All Partners", icon: <Users size={20} /> },
          { href: "/dashboard/admin/partners/verifications", label: "Verifications", icon: <CheckCircle size={20} /> },
          { href: "/dashboard/admin/partners/promotions", label: "Promotions", icon: <Tag size={20} /> },
        ]
      },
      {
        href: "/admin/featuring", // Corrected parent path
        label: "Featuring",
        icon: <Star size={20} />,

        children: [
          { href: "/admin/featuring", label: "Featured Events", icon: <Star size={20} /> },
          { href: "/admin/featuring/ab-testing", label: "A/B Testing", icon: <Shuffle size={20} /> },
        ]
      },
      { href: "/admin/finance/reports", label: "Revenue Reports", icon: <DollarSign size={20} /> }, // Corrected path
      { href: "/wallet", label: "Wallet", icon: <Wallet size={20} /> }, // This seems to be a shared top-level wallet page
      { href: "/admin/settings", label: "Settings", icon: <Settings size={20} /> }, // Corrected path
    ],
    ORGANIZER: [
      { href: "/dashboard/organizer", label: "Dashboard", icon: <LayoutDashboard size={20} /> },
      {
        href: "/dashboard/organizer/events",
        label: "Events",
        icon: <CalendarDays size={20} />,
        children: [
          { href: "/dashboard/organizer/events/create", label: "Create Event", icon: <PlusCircle size={20} /> },
          { href: "/dashboard/organizer/events/myEvents", label: "My Events", icon: <CalendarDays size={20} /> },
          { href: "/dashboard/organizer/events/vendors", label: "Vendor Applications", icon: <Store size={20} />,  },
          { href: "/dashboard/organizer/events/featured", label: "Featured Events", icon: <Star size={20} />, },
          { href: "/dashboard/organizer/events/seating", label: "Seating Management", icon: <LayoutGrid size={20} /> },
        ]
      },
      {
        href: "/dashboard/organizer/team",
        label: "Team Management",
        icon: <Users size={20} />,

        children: [
          { href: "/dashboard/organizer/team", label: "My Teams", icon: <Users size={20} /> },
          { href: "/dashboard/organizer/team/invitations", label: "Invitations", icon: <Mail size={20} /> },
        ]
      },
      {
        href: "/dashboard/organizer/attendees",
        label: "Attendees",
        icon: <Users size={20} />,
      },
      {
        href: "/organizer/partners",
        label: "Partners",
        icon: <Hotel size={20} />,
        children: [
          { href: "/organizer/partners", label: "All Partners", icon: <Users size={20} /> },
          { href: "/organizer/partners/add", label: "Add Partner", icon: <PlusCircle size={20} /> },
          { href: "/organizer/partners/promotions", label: "Promotions", icon: <Tag size={20} /> },
        ]
      },
      {
        href: "/dashboard/organizer/finance",
        label: "Finance",
        icon: <DollarSign size={20} />,
        children: [
          { href: "/dashboard/organizer/finance", label: "Overview", icon: <LayoutDashboard size={20} /> },
          { href: "/dashboard/organizer/finance/transactions", label: "Transactions", icon: <CreditCard size={20} /> },
          { href: "/dashboard/organizer/finance/withdrawals", label: "Withdrawals", icon: <Wallet size={20} /> },
        ]
      },
      {
        href: "/dashboard/organizer/nfc",
        label: "NFC Management",
        icon: <CreditCard size={20} />,

        children: [
          { href: "/organizer/nfc-management", label: "NFC Management", icon: <LayoutDashboard size={20} /> },
          { href: "/dashboard/organizer/nfc/dashboard", label: "NFC Dashboard", icon: <LayoutDashboard size={20} /> },
          { href: "/dashboard/organizer/nfc/cards", label: "Card Management", icon: <CreditCard size={20} /> },
          { href: "/dashboard/organizer/nfc/transactions", label: "Transaction History", icon: <History size={20} /> },
          { href: "/dashboard/organizer/nfc/analytics", label: "Payment Analytics", icon: <BarChart3 size={20} /> },
          { href: "/dashboard/organizer/nfc/settings", label: "NFC Settings", icon: <Settings size={20} /> },
        ]
      },
      {
        href: "/dashboard/organizer/marketing",
        label: "Marketing",
        icon: <Mail size={20} />,
        children: [
          { href: "/dashboard/organizer/marketing", label: "Marketing Dashboard", icon: <LayoutDashboard size={20} /> },
          { href: "/dashboard/organizer/marketing/campaigns", label: "Email Campaigns", icon: <Send size={20} /> },
          { href: "/dashboard/organizer/marketing/email-templates", label: "Email Templates", icon: <FileText size={20} /> },
          { href: "/dashboard/organizer/marketing/newsletter", label: "Newsletter", icon: <Users size={20} /> },
          { href: "/dashboard/featured", label: "Promote Events", icon: <Star size={20} />,  },
        ]
      },
      {
        href: "/dashboard/organizer/orders",
        label: "Orders & Tickets",
        icon: <Ticket size={20} />,
        badge: <OrderTicketCounts type="orders" />,
        children: [
          {
            href: "/dashboard/organizer/orders/active",
            label: "Active Orders",
            icon: <Clock size={20} />,
            badge: <OrderTicketCounts type="activeOrders"  />
          },
          {
            href: "/dashboard/organizer/orders/completed",
            label: "Completed",
            icon: <CheckCircle size={20} />,
            badge: <OrderTicketCounts type="completedOrders"  />
          },
          {
            href: "/dashboard/organizer/orders/cancelled",
            label: "Cancelled",
            icon: <AlertCircle size={20} />,
            badge: <OrderTicketCounts type="cancelledOrders"  />
          },
        ]
      },
      { href: "/dashboard/organizer/email", label: "Email Inbox", icon: <Mail size={20} /> },
      { href: "/dashboard/organizer/venues", label: "Venues", icon: <MapPin size={20} /> },
      { href: "/dashboard/organizer/analytics", label: "Analytics", icon: <BarChart3 size={20} /> },
      { href: "/organizer/finance/reports", label: "Revenue Reports", icon: <DollarSign size={20} /> },
      { href: "/dashboard/wallet", label: "Wallet", icon: <Wallet size={20} /> },
      {
        href: "/dashboard/organizer/integrations",
        label: "Integrations",
        icon: <Share size={20} />,
        children: [
          { href: "/dashboard/organizer/integrations/webhooks", label: "Webhooks", icon: <LinkIcon size={20} /> },
          { href: "/dashboard/organizer/integrations/payment", label: "Payment Gateways", icon: <CreditCard size={20} /> },
          { href: "/dashboard/organizer/integrations/calendar", label: "Calendar Sync", icon: <Calendar size={20} /> },
          { href: "/dashboard/organizer/integrations/social", label: "Social Media", icon: <Share2 size={20} /> },
          { href: "/dashboard/developer", label: "Developer Portal", icon: <Key size={20} />, badge: "API", badgeColor: "bg-purple-500" },
        ]
      },
      { href: "/dashboard/organizer/profile", label: "Profile", icon: <User size={20} /> },
      { href: "/dashboard/organizer/settings", label: "Settings", icon: <Settings size={20} /> },
    ],
    VENDOR: [
      { href: "/dashboard/vendor", label: "Dashboard", icon: <LayoutDashboard size={20} /> },
      {
        href: "/dashboard/vendor/events",
        label: "Events",
        icon: <CalendarDays size={20} />,
        children: [
          { href: "/dashboard/vendor/events", label: "My Events", icon: <CalendarDays size={20} /> },
          { href: "/dashboard/vendor/events/apply", label: "Apply for Events", icon: <FileText size={20} /> },
          { href: "/dashboard/vendor/events/applications", label: "My Applications", icon: <Store size={20} />, },
        ]
      },
      {
        href: "/dashboard/vendor/devices",
        label: "Devices & Scanning",
        icon: <Bluetooth size={20} />,
        children: [
          { href: "/dashboard/vendor/devices", label: "Device Hub", icon: <Bluetooth size={20} /> },
          { href: "/dashboard/vendor/scanner", label: "Device Scanner", icon: <Bluetooth size={20} /> },
          { href: "/dashboard/vendor/nfc/terminal", label: "NFC Terminal", icon: <CreditCard size={20} /> },
        ]
      },
      {
        href: "/dashboard/vendor/nfc",
        label: "NFC Payments",
        icon: <CreditCard size={20} />,
        children: [
          { href: "/dashboard/vendor/enhanced-pos", label: "Enhanced POS", icon: <ShoppingCart size={20} /> },
          { href: "/dashboard/vendor/nfc/transactions", label: "Transaction History", icon: <History size={20} /> },
          { href: "/dashboard/vendor/nfc/analytics", label: "Sales Analytics", icon: <BarChart3 size={20} /> },
          { href: "/dashboard/vendor/nfc/receipts", label: "Digital Receipts", icon: <FileText size={20} /> },
          { href: "/dashboard/vendor/nfc/cards", label: "Card Management", icon: <CreditCard size={20} /> },
          { href: "/dashboard/vendor/nfc/offline", label: "Offline Mode", icon: <WifiOff size={20} /> },
          { href: "/dashboard/vendor/nfc/settings", label: "Terminal Settings", icon: <Settings size={20} /> }
        ]
      },
      {
        href: "/vendor/products",
        label: "Products",
        icon: <Box size={20} />,
        children: [
          { href: "/vendor/products", label: "All Products", icon: <Box size={20} /> },
          { href: "/vendor/createproduct", label: "Create Product", icon: <PlusCircle size={20} /> },
          { href: "/vendor/products/categories", label: "Categories", icon: <Tag size={20} /> },
        ]
      },
      { href: "/dashboard/vendor/orders", label: "Orders", icon: <ShoppingCart size={20} /> },
      { href: "/dashboard/vendor/analytics", label: "Analytics", icon: <BarChart3 size={20} /> },
      {
        href: "/dashboard/vendor/finance",
        label: "Finance",
        icon: <DollarSign size={20} />,
        children: [
          { href: "/dashboard/vendor/finance", label: "Overview", icon: <LayoutDashboard size={20} /> },
          { href: "/vendor/finance/reports", label: "Sales Reports", icon: <BarChart3 size={20} /> },
          { href: "/dashboard/vendor/finance/payments", label: "Payments", icon: <CreditCard size={20} /> },
          { href: "/dashboard/vendor/finance/withdrawals", label: "Withdrawals", icon: <Wallet size={20} /> },
          { href: "/dashboard/vendor/pos-rentals", label: "POS Rentals", icon: <CreditCard size={20} /> },
        ]
      },
      { href: "/dashboard/vendor/verification", label: "Verification", icon: <CheckCircle size={20} /> },
      { href: "/dashboard/vendor/staff", label: "Staff", icon: <Users size={20} /> },
      { href: "/dashboard/vendor/settings", label: "Settings", icon: <Settings size={20} /> },
    ],
    SUPERADMIN: [
      { href: "/dashboard/superadmin", label: "Dashboard", icon: <LayoutDashboard size={20} /> },
      { href: "/dashboard/superadmin/users", label: "Users", icon: <Users size={20} /> },
      { href: "/dashboard/superadmin/vendors", label: "Vendors", icon: <Briefcase size={20} /> },
      { href: "/dashboard/superadmin/events", label: "Events", icon: <CalendarDays size={20} /> },
      { href: "/dashboard/superadmin/analytics", label: "Analytics", icon: <BarChart3 size={20} /> },
    ],

    DEVELOPER: [
      { href: "/dashboard/developer", label: "Dashboard", icon: <LayoutDashboard size={20} /> },
      { href: "/dashboard/developer/api-keys", label: "API Keys", icon: <Key size={20} /> },
      { href: "/dashboard/developer/docs", label: "API Documentation", icon: <FileText size={20} />,  },
      { href: "/dashboard/developer/analytics", label: "API Analytics", icon: <BarChart3 size={20} /> },
      { href: "/dashboard/developer/alerts", label: "API Alerts", icon: <AlertTriangle size={20} /> },
    ],
    PARTNER: [
      { href: "/dashboard/partner", label: "Partner Dashboard", icon: <Hotel size={20} /> },
      { href: "/dashboard/partner/profile", label: "My Profile", icon: <User size={20} /> },
      { href: "/dashboard/partner/settings", label: "Settings", icon: <Settings size={20} /> },
      // Add other partner-specific links here, e.g., for menu, promotions, loyalty
      { href: "/dashboard/partner/menu", label: "Manage Menu", icon: <Utensils size={20} /> },
      { href: "/dashboard/partner/promotions", label: "Promotions", icon: <Tag size={20} /> },
      { href: "/dashboard/partner/loyalty", label: "Loyalty Program", icon: <Award size={20} /> },
      { href: "/dashboard/partner/events", label: "Event Partnerships", icon: <Calendar size={20} /> },
      { href: "/dashboard/partner/transactions", label: "Transactions", icon: <CreditCard size={20} /> },
      { href: "/dashboard/partner/reviews", label: "Reviews", icon: <Star size={20} /> },
      { href: "/dashboard/partner/analytics", label: "Analytics", icon: <BarChart3 size={20} /> },
    ],
  };

  const currentNavItems = navItems[role] || [];

  // Track expanded menu items
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({
    'Events': true,
    'Marketing': true,
    'Team Management': true,
    'Finance': true,
    'NFC Payments': true,
    'Devices & Scanning': true,
    'Products': true,
    'Integrations': true,
    'Partners': true
  });

  // Toggle expanded state for an item
  const toggleExpand = (label: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [label]: !prev[label]
    }));
  };

  // Check if a nav item should be shown as active
  const isNavItemActive = (item: NavItem): boolean => {
    if (pathname === item.href) return true;
    if (item.children) {
      return item.children.some(child => pathname === child.href);
    }
    return false;
  };

  // Render a nav item (supports nested items)
  const renderNavItem = (item: NavItem) => {
    const isActive = isNavItemActive(item);
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems[item.label];

    if (hasChildren) {
      return (
        <div key={item.href} className={`mb-1 ${isActive ? 'bg-blue-50 rounded-lg' : ''}`}>
          <button
            onClick={() => toggleExpand(item.label)}
            className={`w-full flex items-center justify-between py-2 px-4 rounded-lg transition-colors text-sm font-medium hover:bg-gray-500 ${isActive ? 'text-blue-600' : 'text-gray-700'}`}
          >
            <div className="flex items-center gap-3 min-w-0 overflow-hidden">
              <div className="flex-shrink-0">{item.icon}</div>
              <span className="whitespace-nowrap overflow-hidden text-ellipsis">{item.label}</span>
              {item.badge && (
                typeof item.badge === 'string' ? (
                  <Badge className={cn('ml-2 text-xs flex-shrink-0', item.badgeColor || 'bg-blue-500')}>
                    {item.badge}
                  </Badge>
                ) : (
                  <div className="flex-shrink-0">{item.badge}</div>
                )
              )}
            </div>
            <ChevronDown
              size={16}
              className={`transition-transform flex-shrink-0 ml-2 ${isExpanded ? 'transform rotate-180' : ''}`}
            />
          </button>

          {isExpanded && (
            <div className="ml-8 mt-1 space-y-1">
              {item.children?.map(child => (
                <Link
                  key={child.href}
                  href={child.href}
                  className={`flex items-center justify-between py-2 px-4 rounded-lg transition-colors text-sm font-medium hover:bg-gray-500 ${pathname === child.href ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
                >
                  <div className="flex items-center min-w-0 overflow-hidden">
                    <div className="flex-shrink-0">{child.icon}</div>
                    <span className="ml-3 whitespace-nowrap overflow-hidden text-ellipsis">{child.label}</span>
                  </div>
                  {child.badge && (
                    typeof child.badge === 'string' ? (
                      <Badge className={cn('text-xs flex-shrink-0 ml-2', child.badgeColor || 'bg-blue-500')}>
                        {child.badge}
                      </Badge>
                    ) : (
                      <div className="flex-shrink-0">{child.badge}</div>
                    )
                  )}
                </Link>
              ))}
            </div>
          )}
        </div>
      );
    }

    return (
      <Link
        key={item.href}
        href={item.href}
        className={`flex items-center justify-between py-2 px-4 rounded-lg transition-colors text-sm font-medium hover:bg-gray-500 ${isActive ? 'bg-blue-50 text-blue-600' : 'text-gray-700'}`}
      >
        <div className="flex items-center gap-3 min-w-0 overflow-hidden">
          <div className="flex-shrink-0">{item.icon}</div>
          <span className="whitespace-nowrap overflow-hidden text-ellipsis">{item.label}</span>
        </div>
        {item.badge && (
          typeof item.badge === 'string' ? (
            <Badge className={cn('text-xs flex-shrink-0 ml-2', item.badgeColor || 'bg-blue-500')}>
              {item.badge}
            </Badge>
          ) : (
            <div className="flex-shrink-0">{item.badge}</div>
          )
        )}
      </Link>
    );
  };

  return (
    <div className="h-full border-r bg-white py-4 flex flex-col shadow-sm">
      <div className="px-4 mb-6 flex items-center">
        <Image src={logo} alt="Logo" width={32} height={32} className="rounded-md" />
        <span className="ml-2 font-semibold text-gray-800 whitespace-nowrap">QuickTime</span>
      </div>

      <div className="flex-1 overflow-y-auto px-3 custom-scrollbar">
        <nav className="space-y-1 pb-20">
          {currentNavItems.map(item => renderNavItem(item))}
        </nav>

        <div className="mt-6">
          <Separator className="my-4" />
          <div className="px-3 mb-2">
            <p className="text-xs font-medium text-gray-400 uppercase tracking-wider">Support</p>
          </div>
          <div className="space-y-1">
            <Link
              href="/help"
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-500 transition-colors"
            >
              <HelpCircle size={18} className="min-w-[18px] mr-3 text-gray-500" />
              <span className="whitespace-nowrap overflow-hidden text-ellipsis">Help Center</span>
            </Link>
            <Link
              href="/documentation"
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-500 transition-colors"
            >
              <FileText size={18} className="min-w-[18px] mr-3 text-gray-500" />
              <span className="whitespace-nowrap overflow-hidden text-ellipsis">Documentation</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
export default Sidebar;