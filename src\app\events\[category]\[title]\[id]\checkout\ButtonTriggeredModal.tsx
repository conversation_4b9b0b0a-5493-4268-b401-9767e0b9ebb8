'use client';

import React, { useState, useEffect } from 'react';
import { Calendar, Clock, MapPin, Ticket, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import CheckoutClient from './CheckoutClient';

interface TicketType {
  id: string;
  type: string;
  displayName: string;
  price: number;
  description: string;
  isAvailable: boolean;
  maxPerOrder: number;
}

interface ButtonTriggeredModalProps {
  eventId: string;
  eventDetails: {
    title: string;
    startDate: Date | string;
    startTime: string;
    endTime: string;
    venue: string;
    imagePath?: string | null;
  };
}

export default function ButtonTriggeredModal({
  eventId,
  eventDetails
}: ButtonTriggeredModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [ticketTypes, setTicketTypes] = useState<TicketType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const openModal = () => setIsOpen(true);
  const closeModal = () => setIsOpen(false);

  // Fetch ticket data from the API
  useEffect(() => {
    const fetchTicketData = async () => {
      if (!eventId) return;

      try {
        setIsLoading(true);
        setError(null);

        console.log('Fetching tickets for event:', eventId);
        const response = await fetch(`/api/events/${eventId}/tickets`);

        if (!response.ok) {
          console.error('Ticket API response not OK:', response.status);
          const errorText = await response.text();
          console.error('Error response:', errorText);
          throw new Error(`Failed to fetch ticket data: ${response.status}`);
        }

        const data = await response.json();
        console.log('Ticket data received:', data);

        if (!data.tickets || data.tickets.length === 0) {
          console.warn('No tickets found for this event');
          setError('No tickets available for this event. Please try again later.');
        } else {
          setTicketTypes(data.tickets);
        }
      } catch (err) {
        console.error('Error fetching ticket data:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch ticket data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTicketData();
  }, [eventId]);

  // Hide navigation bar and main header when modal is open
  useEffect(() => {
    // Function to hide elements
    const hideElements = () => {
      // Hide the event page navigation bar
      const navBar = document.querySelector('.sticky.top-0.z-50');
      // Hide the main site header
      const mainHeader = document.querySelector('header');

      // Filter out null elements and explicitly type as HTMLElement[]
      const elementsToHide = [navBar, mainHeader].filter((el): el is HTMLElement => el !== null);

      elementsToHide.forEach(element => {
        element.classList.add('hidden');
      });

      // Prevent body scrolling
      document.body.style.overflow = 'hidden';
    };

    // Function to show elements
    const showElements = () => {
      // Get the event page navigation bar
      const navBar = document.querySelector('.sticky.top-0.z-50');
      // Get the main site header
      const mainHeader = document.querySelector('header');

      // Filter out null elements and explicitly type as HTMLElement[]
      const elementsToHide = [navBar, mainHeader].filter((el): el is HTMLElement => el !== null);

      elementsToHide.forEach(element => {
        element.classList.remove('hidden');
      });

      // Restore body scrolling
      document.body.style.overflow = '';
    };

    // Apply hiding/showing based on modal state
    if (isOpen) {
      hideElements();

      // Set up a recurring check to ensure elements stay hidden
      const intervalId = setInterval(hideElements, 500);

      // Cleanup function
      return () => {
        clearInterval(intervalId);
        showElements();
      };
    }
  }, [isOpen]);

  return (
    <>
      {/* Button to open modal */}
      <Button
        className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white py-6 shadow-md transition-all duration-300 rounded-lg"
        size="lg"
        onClick={openModal}
      >
        <Ticket className="mr-2 h-5 w-5" />
        Get Tickets
      </Button>

      {/* Modal */}
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black/80 backdrop-blur-sm transition-opacity"
            onClick={closeModal}
          />

          {/* Modal content */}
          <div className="fixed z-50 w-[95vw] max-w-[1000px] h-[90vh] rounded-xl bg-white shadow-xl flex flex-col animate-in fade-in-50 zoom-in-95 duration-300">
            {/* Close button */}
            <button
              onClick={closeModal}
              className="absolute right-4 top-4 z-10 rounded-full bg-white/90 p-2 text-gray-600 shadow-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 transition-all duration-200"
              aria-label="Close"
            >
              <X className="h-5 w-5" />
              <span className="sr-only">Close</span>
            </button>

            <div className="flex h-full flex-col md:flex-row overflow-hidden">
              {/* Left side - Event details */}
              <div className="bg-gradient-to-br from-blue-600 to-indigo-700 p-6 text-white md:w-1/3 relative overflow-hidden">
                {/* Background pattern */}
                <div className="absolute inset-0 opacity-10">
                  <svg className="h-full w-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                    <path d="M0,0 L100,0 L100,100 L0,100 Z" fill="url(#grid-pattern)" />
                  </svg>
                  <defs>
                    <pattern id="grid-pattern" width="10" height="10" patternUnits="userSpaceOnUse">
                      <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" strokeWidth="0.5" />
                    </pattern>
                  </defs>
                </div>

                <div className="h-full flex flex-col relative z-10">
                  <div>
                    <h3 className="text-xl font-bold mb-2">{eventDetails.title}</h3>
                    <div className="mt-4 space-y-4">
                      <div className="flex items-center">
                        <Calendar className="h-5 w-5 mr-3 text-blue-200" />
                        <span>{new Date(eventDetails.startDate).toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}</span>
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-5 w-5 mr-3 text-blue-200" />
                        <span>{eventDetails.startTime} - {eventDetails.endTime}</span>
                      </div>
                      <div className="flex items-center">
                        <MapPin className="h-5 w-5 mr-3 text-blue-200" />
                        <span>{eventDetails.venue}</span>
                      </div>
                    </div>
                  </div>

                  {eventDetails.imagePath && (
                    <div className="relative mt-6 flex-1 min-h-[150px] rounded-lg overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent z-10"></div>
                      <div
                        className="w-full h-full bg-cover bg-center opacity-80"
                        style={{ backgroundImage: `url(${eventDetails.imagePath})` }}
                      ></div>
                    </div>
                  )}

                  <div className="mt-auto pt-6">
                    <div className="text-sm text-blue-100">
                      <p>Powered by</p>
                      <p className="font-semibold text-white">QUICK TIME</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right side - Checkout Client */}
              <div className="bg-white md:w-2/3 overflow-y-auto" style={{ maxHeight: '90vh' }}>
                {isLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                    <span className="ml-2 text-blue-600">Loading ticket information...</span>
                  </div>
                ) : error ? (
                  <div className="p-6 text-center">
                    <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                      <h3 className="text-red-800 font-medium mb-2">Error Loading Tickets</h3>
                      <p className="text-red-600 mb-4">{error}</p>
                      <div className="text-sm text-gray-600 mb-4">
                        <p className="mb-2">Possible reasons:</p>
                        <ul className="list-disc pl-5 space-y-1 text-left">
                          <li>No tickets have been created for this event yet</li>
                          <li>The tickets are not available for purchase</li>
                          <li>There might be a temporary server issue</li>
                        </ul>
                      </div>
                    </div>
                    <div className="flex space-x-4 justify-center">
                      <Button onClick={() => window.location.reload()} variant="outline">
                        Try Again
                      </Button>
                      <Button onClick={closeModal} variant="ghost">
                        Close
                      </Button>
                    </div>
                  </div>
                ) : (
                  <CheckoutClient
                    id={eventId}
                    availableTickets={ticketTypes}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
