'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Star, TrendingUp, Users, MousePointerClick, DollarSign, Calendar, Check, ArrowRight, BarChart3, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useToast } from '@/components/ui/use-toast';
import { format } from 'date-fns';

// Define interfaces for our data
interface Featuring {
  id: string;
  eventId: string;
  eventTitle: string;
  tier: string;
  startDate: string;
  endDate: string;
  status: string;
  paymentAmount: string;
  organizer: string;
  organizerId: string;
  impressions: number;
  imageUrl: string | null;
}

interface Stats {
  activeCount: number;
  pendingCount: number;
  expiredCount: number;
  totalCount: number;
  monthlyRevenue: string;
  averageConversionRate: number;
}

interface Pagination {
  page: number;
  pageSize: number;
  totalPages: number;
}

export default function AdminFeaturingPage() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('active');
  const [isLoading, setIsLoading] = useState(true);
  const [featurings, setFeaturings] = useState<Featuring[]>([]);
  const [stats, setStats] = useState<Stats>({
    activeCount: 0,
    pendingCount: 0,
    expiredCount: 0,
    totalCount: 0,
    monthlyRevenue: '0',
    averageConversionRate: 0
  });
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    pageSize: 10,
    totalPages: 1
  });

  // Fetch featuring data
  const fetchFeaturingData = async (status = activeTab, page = 1) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/admin/featuring?status=${status}&page=${page}&pageSize=${pagination.pageSize}`);

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();
      setFeaturings(data.featurings);
      setStats(data.stats);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error fetching featuring data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load featuring data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    fetchFeaturingData(value, 1);
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    fetchFeaturingData(activeTab, newPage);
  };

  // Handle approve/reject actions
  const handleAction = async (featuringId: string, action: 'approve' | 'reject') => {
    try {
      const response = await fetch('/api/admin/featuring', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ featuringId, action }),
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();

      toast({
        title: 'Success',
        description: data.message,
      });

      // Refresh data
      fetchFeaturingData(activeTab, pagination.page);
    } catch (error) {
      console.error(`Error ${action}ing featuring:`, error);
      toast({
        title: 'Error',
        description: `Failed to ${action} featuring. Please try again.`,
        variant: 'destructive',
      });
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchFeaturingData();
  }, []);

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Featured Events Management</h1>
          <p className="text-gray-500 mt-1">
            Manage featured events and optimize their performance
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex gap-3">
          <Button asChild variant="outline">
            <Link href="/admin/featuring/settings">
              <Star className="mr-2 h-4 w-4" />
              Featuring Settings
            </Link>
          </Button>
          <Button asChild>
            <Link href="/admin/featuring/ab-testing">
              <BarChart3 className="mr-2 h-4 w-4" />
              A/B Testing
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Active Featured Events</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center">
                <Loader2 className="h-5 w-5 animate-spin mr-2" />
                <span className="text-2xl font-bold">Loading...</span>
              </div>
            ) : (
              <div className="flex items-center">
                <Star className="h-5 w-5 text-yellow-500 mr-2" />
                <span className="text-2xl font-bold">{stats.activeCount}</span>
              </div>
            )}
            <p className="text-xs text-gray-500 mt-1">
              Currently featured on the landing page
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Total Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center">
                <Loader2 className="h-5 w-5 animate-spin mr-2" />
                <span className="text-2xl font-bold">Loading...</span>
              </div>
            ) : (
              <div className="flex items-center">
                <DollarSign className="h-5 w-5 text-green-500 mr-2" />
                <span className="text-2xl font-bold">${parseFloat(stats.monthlyRevenue).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
              </div>
            )}
            <p className="text-xs text-gray-500 mt-1">
              From featuring fees this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Conversion Rate</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center">
                <Loader2 className="h-5 w-5 animate-spin mr-2" />
                <span className="text-2xl font-bold">Loading...</span>
              </div>
            ) : (
              <div className="flex items-center">
                <TrendingUp className="h-5 w-5 text-blue-500 mr-2" />
                <span className="text-2xl font-bold">{stats.averageConversionRate}%</span>
              </div>
            )}
            <p className="text-xs text-gray-500 mt-1">
              Average for all featured events
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="active" className="space-y-4" value={activeTab} onValueChange={handleTabChange}>
        <TabsList>
          <TabsTrigger value="active">Active ({stats.activeCount})</TabsTrigger>
          <TabsTrigger value="pending">Pending ({stats.pendingCount})</TabsTrigger>
          <TabsTrigger value="expired">Expired ({stats.expiredCount})</TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Active Featured Events</CardTitle>
              <CardDescription>
                Events currently featured on the landing page
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin mr-2" />
                  <span>Loading featured events...</span>
                </div>
              ) : featurings.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No active featured events found.
                </div>
              ) : (
                <div className="rounded-md border">
                  <div className="grid grid-cols-8 gap-4 p-4 font-medium border-b">
                    <div className="col-span-3">Event</div>
                    <div>Tier</div>
                    <div>Start Date</div>
                    <div>End Date</div>
                    <div>Impressions</div>
                    <div>Actions</div>
                  </div>

                  {featurings.map((featuring) => (
                    <div key={featuring.id} className="grid grid-cols-8 gap-4 p-4 border-b">
                      <div className="col-span-3 font-medium">{featuring.eventTitle}</div>
                      <div>
                        <Badge className={
                          featuring.tier === 'ELITE' ? "bg-purple-500" :
                          featuring.tier === 'PREMIUM' ? "bg-yellow-500" :
                          "bg-blue-500"
                        }>
                          {featuring.tier === 'ELITE' ? "Elite" :
                           featuring.tier === 'PREMIUM' ? "Premium" :
                           "Basic"}
                        </Badge>
                      </div>
                      <div className="text-gray-500">{format(new Date(featuring.startDate), 'MMM d, yyyy')}</div>
                      <div className="text-gray-500">{format(new Date(featuring.endDate), 'MMM d, yyyy')}</div>
                      <div>{featuring.impressions.toLocaleString()}</div>
                      <div>
                        <Button
                          variant="outline"
                          size="sm"
                          asChild
                        >
                          <Link href={`/admin/events/${featuring.eventId}`}>
                            View
                          </Link>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            {!isLoading && featurings.length > 0 && (
              <CardFooter>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page === 1}
                >
                  Previous
                </Button>
                <span className="mx-4">
                  Page {pagination.page} of {pagination.totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page === pagination.totalPages}
                >
                  Next
                </Button>
              </CardFooter>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="pending" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pending Featured Events</CardTitle>
              <CardDescription>
                Events waiting for approval
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin mr-2" />
                  <span>Loading pending events...</span>
                </div>
              ) : featurings.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No pending featured events found.
                </div>
              ) : (
                <div className="rounded-md border">
                  <div className="grid grid-cols-8 gap-4 p-4 font-medium border-b">
                    <div className="col-span-3">Event</div>
                    <div>Tier</div>
                    <div>Organizer</div>
                    <div>Requested</div>
                    <div>Payment</div>
                    <div>Actions</div>
                  </div>

                  {featurings.map((featuring) => (
                    <div key={featuring.id} className="grid grid-cols-8 gap-4 p-4 border-b">
                      <div className="col-span-3 font-medium">{featuring.eventTitle}</div>
                      <div>
                        <Badge className={
                          featuring.tier === 'ELITE' ? "bg-purple-500" :
                          featuring.tier === 'PREMIUM' ? "bg-yellow-500" :
                          "bg-blue-500"
                        }>
                          {featuring.tier === 'ELITE' ? "Elite" :
                           featuring.tier === 'PREMIUM' ? "Premium" :
                           "Basic"}
                        </Badge>
                      </div>
                      <div className="text-gray-500">{featuring.organizer}</div>
                      <div className="text-gray-500">{format(new Date(featuring.startDate), 'MMM d, yyyy')}</div>
                      <div>
                        <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                          ${parseFloat(featuring.paymentAmount).toFixed(2)}
                        </Badge>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleAction(featuring.id, 'approve')}
                        >
                          Approve
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-500 border-red-200 hover:bg-red-50"
                          onClick={() => handleAction(featuring.id, 'reject')}
                        >
                          Reject
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            {!isLoading && featurings.length > 0 && (
              <CardFooter>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page === 1}
                >
                  Previous
                </Button>
                <span className="mx-4">
                  Page {pagination.page} of {pagination.totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page === pagination.totalPages}
                >
                  Next
                </Button>
              </CardFooter>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="expired" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Expired Featured Events</CardTitle>
              <CardDescription>
                Events that were previously featured
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin mr-2" />
                  <span>Loading expired events...</span>
                </div>
              ) : featurings.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No expired featured events found.
                </div>
              ) : (
                <div className="rounded-md border">
                  <div className="grid grid-cols-8 gap-4 p-4 font-medium border-b">
                    <div className="col-span-3">Event</div>
                    <div>Tier</div>
                    <div>End Date</div>
                    <div>Total Impressions</div>
                    <div>Revenue</div>
                    <div>Actions</div>
                  </div>

                  {featurings.map((featuring) => (
                    <div key={featuring.id} className="grid grid-cols-8 gap-4 p-4 border-b">
                      <div className="col-span-3 font-medium">{featuring.eventTitle}</div>
                      <div>
                        <Badge className={
                          featuring.tier === 'ELITE' ? "bg-purple-500" :
                          featuring.tier === 'PREMIUM' ? "bg-yellow-500" :
                          "bg-blue-500"
                        }>
                          {featuring.tier === 'ELITE' ? "Elite" :
                           featuring.tier === 'PREMIUM' ? "Premium" :
                           "Basic"}
                        </Badge>
                      </div>
                      <div className="text-gray-500">{format(new Date(featuring.endDate), 'MMM d, yyyy')}</div>
                      <div>{featuring.impressions.toLocaleString()}</div>
                      <div>${parseFloat(featuring.paymentAmount).toFixed(2)}</div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          asChild
                        >
                          <Link href={`/admin/events/${featuring.eventId}`}>
                            View
                          </Link>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            {!isLoading && featurings.length > 0 && (
              <CardFooter>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page === 1}
                >
                  Previous
                </Button>
                <span className="mx-4">
                  Page {pagination.page} of {pagination.totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page === pagination.totalPages}
                >
                  Next
                </Button>
              </CardFooter>
            )}
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
