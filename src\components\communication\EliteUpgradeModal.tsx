'use client';

import React, { useState } from 'react';
import { 
  X, 
  Crown, 
  Sparkles, 
  Check, 
  MessageCircle, 
  Calendar, 
  Users, 
  Shield,
  Zap,
  Star
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { EliteTier, EliteSubscriptionType } from '@prisma/client';
import { ELITE_PRICING_TIERS } from '@/config/elite-pricing';
import { CheckoutContainer } from '@/components/checkout/CheckoutContainer';

interface EliteUpgradeModalProps {
  eventId: string;
  currentTier: EliteTier;
  onClose: () => void;
  onSuccess: () => void;
}

export default function EliteUpgradeModal({
  eventId,
  currentTier,
  onClose,
  onSuccess
}: EliteUpgradeModalProps) {
  const [selectedTier, setSelectedTier] = useState<EliteTier>(
    currentTier === EliteTier.BASIC ? EliteTier.ELITE : EliteTier.ELITE_PRO
  );
  const [subscriptionType, setSubscriptionType] = useState<EliteSubscriptionType>(
    EliteSubscriptionType.PER_EVENT
  );
  const [showCheckout, setShowCheckout] = useState(false);

  const availableTiers = ELITE_PRICING_TIERS.filter(tier => 
    tier.tier !== currentTier && tier.tier !== EliteTier.BASIC
  );

  const selectedPricingTier = ELITE_PRICING_TIERS.find(t => t.tier === selectedTier);

  const getPrice = () => {
    if (!selectedPricingTier) return 0;
    
    switch (subscriptionType) {
      case EliteSubscriptionType.PER_EVENT:
        return selectedPricingTier.pricePerEvent || 0;
      case EliteSubscriptionType.MONTHLY:
        return selectedPricingTier.monthlyPrice || 0;
      case EliteSubscriptionType.ANNUAL:
        return selectedPricingTier.annualPrice || 0;
      default:
        return 0;
    }
  };

  const handleUpgrade = () => {
    setShowCheckout(true);
  };

  const handleCheckoutComplete = async (orderData: any) => {
    try {
      // Create Elite Communication subscription
      const response = await fetch('/api/elite-communication/subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          eventId,
          tier: selectedTier,
          subscriptionType,
          paymentIntentId: orderData.paymentIntentId
        }),
      });

      if (response.ok) {
        onSuccess();
      } else {
        console.error('Failed to create subscription');
      }
    } catch (error) {
      console.error('Error creating subscription:', error);
    }
  };

  if (showCheckout) {
    const checkoutTickets = [{
      id: 'elite-communication',
      type: `Elite Communication - ${selectedTier}`,
      price: getPrice(),
      isAvailable: true,
      totalSeats: 1,
      description: `${selectedTier} tier access for Elite Communication features`
    }];

    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm" onClick={onClose} />
        <div className="relative z-50 w-[95vw] max-w-[1200px] h-[90vh] rounded-xl bg-white shadow-xl overflow-hidden">
          <button
            onClick={onClose}
            className="absolute right-4 top-4 z-10 rounded-full bg-white/90 p-2 text-gray-600 shadow-md hover:bg-gray-100"
          >
            <X className="h-5 w-5" />
          </button>
          
          <CheckoutContainer
            eventDetails={{
              id: eventId,
              title: 'Elite Communication Upgrade',
              startDate: new Date().toISOString(),
              startTime: '00:00',
              endTime: '23:59',
              venue: 'Digital Access',
              location: 'Online',
              imagePath: null
            }}
            availableTickets={checkoutTickets}
            onCheckoutComplete={handleCheckoutComplete}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black/80 backdrop-blur-sm" onClick={onClose} />

      {/* Modal */}
      <div className="relative z-50 w-[95vw] max-w-4xl max-h-[90vh] overflow-y-auto rounded-xl bg-white shadow-xl">
        {/* Header */}
        <div className="sticky top-0 bg-gradient-to-r from-purple-600 to-blue-600 text-white p-6 rounded-t-xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-white/20 rounded-lg">
                <Crown className="h-6 w-6" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">Upgrade to Elite Communication</h2>
                <p className="text-purple-100">
                  Unlock premium networking features for this event
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/20 rounded-lg transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Pricing Tiers */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {availableTiers.map((tier) => (
              <Card 
                key={tier.tier}
                className={`cursor-pointer transition-all ${
                  selectedTier === tier.tier 
                    ? 'ring-2 ring-purple-500 border-purple-200' 
                    : 'hover:border-purple-200'
                } ${tier.popular ? 'relative' : ''}`}
                onClick={() => setSelectedTier(tier.tier)}
              >
                {tier.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-3 py-1">
                      <Star className="h-3 w-3 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}
                
                <CardHeader className="text-center pb-4">
                  <div className="flex items-center justify-center mb-2">
                    {tier.tier === EliteTier.ELITE ? (
                      <Shield className="h-8 w-8 text-purple-600" />
                    ) : (
                      <Crown className="h-8 w-8 text-yellow-600" />
                    )}
                  </div>
                  <CardTitle className="text-xl">{tier.name}</CardTitle>
                  <p className="text-sm text-gray-600">{tier.description}</p>
                </CardHeader>

                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900">
                      ${tier.pricePerEvent}
                    </div>
                    <div className="text-sm text-gray-600">per event</div>
                    {tier.monthlyPrice && (
                      <div className="text-sm text-gray-500 mt-1">
                        or ${tier.monthlyPrice}/month
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    {tier.features.map((feature, index) => (
                      <div key={index} className="flex items-center text-sm">
                        <Check className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </div>
                    ))}
                  </div>

                  <div className="pt-2">
                    <RadioGroup 
                      value={selectedTier} 
                      onValueChange={(value) => setSelectedTier(value as EliteTier)}
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value={tier.tier} id={tier.tier} />
                        <Label htmlFor={tier.tier} className="text-sm font-medium">
                          Select {tier.name}
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Subscription Type Selection (for Elite Pro) */}
          {selectedTier === EliteTier.ELITE_PRO && selectedPricingTier?.monthlyPrice && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Subscription Options</CardTitle>
              </CardHeader>
              <CardContent>
                <RadioGroup 
                  value={subscriptionType} 
                  onValueChange={(value) => setSubscriptionType(value as EliteSubscriptionType)}
                  className="space-y-3"
                >
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value={EliteSubscriptionType.PER_EVENT} id="per-event" />
                      <Label htmlFor="per-event" className="font-medium">
                        Per Event Access
                      </Label>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">${selectedPricingTier.pricePerEvent}</div>
                      <div className="text-sm text-gray-600">One-time payment</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value={EliteSubscriptionType.MONTHLY} id="monthly" />
                      <Label htmlFor="monthly" className="font-medium">
                        Monthly Subscription
                      </Label>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">${selectedPricingTier.monthlyPrice}</div>
                      <div className="text-sm text-gray-600">per month</div>
                    </div>
                  </div>

                  {selectedPricingTier.annualPrice && (
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value={EliteSubscriptionType.ANNUAL} id="annual" />
                        <Label htmlFor="annual" className="font-medium">
                          Annual Subscription
                          <Badge className="ml-2 bg-green-100 text-green-800">Save 17%</Badge>
                        </Label>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">${selectedPricingTier.annualPrice}</div>
                        <div className="text-sm text-gray-600">per year</div>
                      </div>
                    </div>
                  )}
                </RadioGroup>
              </CardContent>
            </Card>
          )}

          {/* Feature Comparison */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">What You'll Get</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
                  <MessageCircle className="h-6 w-6 text-purple-600" />
                  <div>
                    <div className="font-medium">Direct Messaging</div>
                    <div className="text-sm text-gray-600">Connect privately with attendees</div>
                  </div>
                </div>

                {selectedTier === EliteTier.ELITE_PRO && (
                  <>
                    <div className="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg">
                      <Calendar className="h-6 w-6 text-yellow-600" />
                      <div>
                        <div className="font-medium">Meeting Scheduling</div>
                        <div className="text-sm text-gray-600">Book one-on-one meetings</div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                      <Zap className="h-6 w-6 text-blue-600" />
                      <div>
                        <div className="font-medium">Exclusive Chat Rooms</div>
                        <div className="text-sm text-gray-600">Access premium networking spaces</div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="text-sm text-gray-600">
              Total: <span className="font-bold text-lg">${getPrice()}</span>
              {subscriptionType === EliteSubscriptionType.MONTHLY && ' /month'}
              {subscriptionType === EliteSubscriptionType.ANNUAL && ' /year'}
            </div>
            
            <div className="flex space-x-3">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button 
                onClick={handleUpgrade}
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
              >
                <Sparkles className="h-4 w-4 mr-2" />
                Upgrade Now
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
