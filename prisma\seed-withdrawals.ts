import { PrismaClient, WithdrawalStatus } from '@prisma/client';
import { faker } from '@faker-js/faker';

const prisma = new PrismaClient();

async function main() {
  console.log('Starting to seed withdrawal data...');

  // Get all organizers
  const organizers = await prisma.user.findMany({
    where: {
      role: 'ORGANIZER',
    },
    select: {
      id: true,
      name: true,
      email: true,
    },
  });

  if (organizers.length === 0) {
    console.log('No organizers found. Please create some organizer accounts first.');
    return;
  }

  console.log(`Found ${organizers.length} organizers.`);

  // Create bank accounts for each organizer if they don't have one
  for (const organizer of organizers) {
    const existingBankAccounts = await prisma.bankAccount.count({
      where: {
        userId: organizer.id,
      },
    });

    if (existingBankAccounts === 0) {
      console.log(`Creating bank account for organizer: ${organizer.name || organizer.email}`);

      await prisma.bankAccount.create({
        data: {
          userId: organizer.id,
          bankName: faker.company.name() + ' Bank',
          accountNumber: faker.finance.accountNumber(10),
          accountName: organizer.name || 'Account Holder',
          branchCode: faker.finance.routingNumber(),
          swiftCode: faker.finance.bic(),
          routingNumber: faker.finance.routingNumber(),
          isDefault: true,
          isVerified: true,
          verificationDate: faker.date.past(),
        },
      });
    }
  }

  // Get all bank accounts
  const bankAccounts = await prisma.bankAccount.findMany({
    where: {
      user: {
        role: 'ORGANIZER',
      },
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  console.log(`Found ${bankAccounts.length} bank accounts.`);

  // Create withdrawals
  const statuses: WithdrawalStatus[] = ['Pending', 'Approved', 'Processed', 'Rejected', 'Failed'];
  const withdrawalsToCreate = 50;

  console.log(`Creating ${withdrawalsToCreate} withdrawals...`);

  for (let i = 0; i < withdrawalsToCreate; i++) {
    const bankAccount = bankAccounts[Math.floor(Math.random() * bankAccounts.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const amount = parseFloat(faker.finance.amount({ min: 100, max: 5000, dec: 2 }));
    const requestDate = faker.date.past({ years: 1 });

    // If status is not Pending, add a processed date
    const processedDate = status !== 'Pending'
      ? faker.date.between({ from: requestDate, to: new Date() })
      : null;

    await prisma.withdrawal.create({
      data: {
        userId: bankAccount.user.id,
        bankAccountId: bankAccount.id,
        amount,
        status,
        requestDate,
        processedDate,
        notes: faker.lorem.sentence(),
        adminNotes: status !== 'Pending' ? faker.lorem.sentence() : null,
        reference: status === 'Processed' ? faker.finance.routingNumber() : null,
        transactionId: status === 'Processed' ? faker.string.uuid() : null,
      },
    });
  }

  console.log('Withdrawal data seeding completed!');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
