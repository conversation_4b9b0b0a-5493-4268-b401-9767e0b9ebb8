'use client';

import React, { useState, useEffect, useRef } from 'react';
import { MessageCircle, Send, Paperclip, Smile, Phone, Video, MoreVertical } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ChatInterfaceProps, Message } from '@/types/elite-communication';
import { getRealWebSocketService } from '@/lib/realWebSocket';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';

export default function ChatInterface({
  eventId,
  currentUser,
  recipientId,
  chatRoomId
}: ChatInterfaceProps) {
  const { data: session } = useSession();
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const wsService = useRef(getRealWebSocketService('/api/messaging/ws'));
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (!session?.user?.id || !eventId) return;

    initializeChat();

    return () => {
      wsService.current.disconnect();
    };
  }, [session?.user?.id, eventId, recipientId, chatRoomId]);

  const initializeChat = async () => {
    try {
      // Connect to WebSocket
      await wsService.current.connectWithAuth(session!.user.id, eventId);
      setIsConnected(true);

      // Subscribe to message events
      wsService.current.subscribe('new_message', handleNewMessage);
      wsService.current.subscribe('typing_indicator', handleTypingIndicator);
      wsService.current.subscribe('read_receipt', handleReadReceipt);
      wsService.current.subscribe('user_status_change', handleUserStatusChange);

      // Join chat room if applicable
      if (chatRoomId) {
        wsService.current.joinChatRoom(chatRoomId);
      }

      // Load message history
      await loadMessages();

    } catch (error) {
      console.error('Error initializing chat:', error);
      toast.error('Failed to connect to chat');
    }
  };

  const loadMessages = async () => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams({
        eventId,
        ...(recipientId && { recipientId }),
        ...(chatRoomId && { chatRoomId }),
        limit: '50'
      });

      const response = await fetch(`/api/messaging/messages?${params}`);
      const data = await response.json();

      if (response.ok) {
        setMessages(data.messages);
        scrollToBottom();
      } else {
        toast.error(data.error || 'Failed to load messages');
      }
    } catch (error) {
      console.error('Error loading messages:', error);
      toast.error('Failed to load messages');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!message.trim() || !isConnected) return;

    try {
      const messageData = {
        eventId,
        content: message.trim(),
        ...(recipientId && { recipientId }),
        ...(chatRoomId && { chatRoomId })
      };

      const response = await fetch('/api/messaging/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(messageData)
      });

      if (response.ok) {
        setMessage('');
        stopTyping();
      } else {
        const data = await response.json();
        toast.error(data.error || 'Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    }
  };

  const handleNewMessage = (messageData: Message) => {
    setMessages(prev => [...prev, messageData]);
    scrollToBottom();

    // Mark as read if it's for current user
    if (messageData.recipientId === session?.user?.id || chatRoomId) {
      markMessageAsRead(messageData.id, messageData.senderId);
    }
  };

  const handleTypingIndicator = (data: any) => {
    if (data.userId === session?.user?.id) return;

    setTypingUsers(prev => {
      if (data.isTyping) {
        return prev.includes(data.userId) ? prev : [...prev, data.userId];
      } else {
        return prev.filter(id => id !== data.userId);
      }
    });
  };

  const handleReadReceipt = (data: any) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === data.messageId
          ? { ...msg, readAt: data.readAt }
          : msg
      )
    );
  };

  const handleUserStatusChange = (data: any) => {
    // Handle user status changes if needed
    console.log('User status changed:', data);
  };

  const startTyping = () => {
    if (!isTyping) {
      setIsTyping(true);
      wsService.current.sendTypingIndicator(
        recipientId || chatRoomId || '',
        true,
        chatRoomId
      );
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing
    typingTimeoutRef.current = setTimeout(stopTyping, 3000);
  };

  const stopTyping = () => {
    if (isTyping) {
      setIsTyping(false);
      wsService.current.sendTypingIndicator(
        recipientId || chatRoomId || '',
        false,
        chatRoomId
      );
    }

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
  };

  const markMessageAsRead = async (messageId: string, senderId: string) => {
    try {
      await fetch(`/api/messaging/messages?messageId=${messageId}`, {
        method: 'PATCH'
      });

      wsService.current.markMessageAsRead(messageId, senderId);
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMessage(e.target.value);
    if (e.target.value.trim()) {
      startTyping();
    } else {
      stopTyping();
    }
  };

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  };

  const formatDate = (date: Date) => {
    const today = new Date();
    const messageDate = new Date(date);

    if (messageDate.toDateString() === today.toDateString()) {
      return 'Today';
    }

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (messageDate.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    }

    return messageDate.toLocaleDateString();
  };

  return (
    <div className="flex flex-col h-[600px] bg-white rounded-xl border border-gray-200">
      {/* Chat Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={recipientId ? '/placeholder-avatar.png' : '/group-chat.png'} />
            <AvatarFallback>
              {chatRoomId ? 'GC' : 'DM'}
            </AvatarFallback>
          </Avatar>
          <div>
            <h3 className="font-semibold text-gray-900">
              {chatRoomId ? 'Group Chat' : 'Direct Message'}
            </h3>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="text-sm text-gray-500">
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {recipientId && (
            <>
              <Button variant="ghost" size="sm">
                <Phone className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Video className="h-4 w-4" />
              </Button>
            </>
          )}
          <Button variant="ghost" size="sm">
            <MoreVertical className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Messages Area */}
      <ScrollArea className="flex-1 p-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-gray-500">Loading messages...</div>
          </div>
        ) : messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500">
              <MessageCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No messages yet</p>
              <p className="text-sm">Start the conversation!</p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map((msg, index) => {
              const isOwnMessage = msg.senderId === session?.user?.id;
              const showDate = index === 0 ||
                formatDate(messages[index - 1].createdAt) !== formatDate(msg.createdAt);

              return (
                <div key={msg.id}>
                  {showDate && (
                    <div className="flex justify-center my-4">
                      <Badge variant="secondary" className="text-xs">
                        {formatDate(msg.createdAt)}
                      </Badge>
                    </div>
                  )}

                  <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
                    <div className={`flex items-start space-x-2 max-w-[70%] ${isOwnMessage ? 'flex-row-reverse space-x-reverse' : ''}`}>
                      {!isOwnMessage && (
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={msg.sender?.image} />
                          <AvatarFallback>
                            {msg.sender?.name?.charAt(0) || 'U'}
                          </AvatarFallback>
                        </Avatar>
                      )}

                      <div className={`rounded-lg px-3 py-2 ${
                        isOwnMessage
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}>
                        {!isOwnMessage && chatRoomId && (
                          <p className="text-xs font-medium mb-1 opacity-70">
                            {msg.sender?.name}
                          </p>
                        )}
                        <p className="text-sm">{msg.content}</p>
                        <div className={`flex items-center justify-between mt-1 text-xs ${
                          isOwnMessage ? 'text-blue-100' : 'text-gray-500'
                        }`}>
                          <span>{formatTime(msg.createdAt)}</span>
                          {isOwnMessage && msg.readAt && (
                            <span className="ml-2">✓✓</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}

            {/* Typing Indicators */}
            {typingUsers.length > 0 && (
              <div className="flex items-center space-x-2 text-gray-500 text-sm">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                </div>
                <span>
                  {typingUsers.length === 1 ? 'Someone is' : `${typingUsers.length} people are`} typing...
                </span>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        )}
      </ScrollArea>

      <Separator />

      {/* Message Input */}
      <div className="p-4">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm">
            <Paperclip className="h-4 w-4" />
          </Button>

          <div className="flex-1 relative">
            <Input
              value={message}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              placeholder={chatRoomId ? "Type a message to the group..." : "Type a message..."}
              className="pr-12"
              disabled={!isConnected}
            />
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2"
            >
              <Smile className="h-4 w-4" />
            </Button>
          </div>

          <Button
            onClick={handleSendMessage}
            disabled={!message.trim() || !isConnected}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>

        {!isConnected && (
          <p className="text-xs text-red-500 mt-2">
            Disconnected from chat server. Trying to reconnect...
          </p>
        )}
      </div>
    </div>
  );
}
