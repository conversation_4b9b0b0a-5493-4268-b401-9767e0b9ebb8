import { PrismaClient, FeeType } from '@prisma/client';
import { hash } from 'bcryptjs';
import { DEFAULT_RATE_LIMIT_CONFIG } from './rate-limiter-auth';
import { DEFAULT_PASSWORD_STRENGTH_CONFIG } from './password-validator';

const prisma = new PrismaClient();

/**
 * Seeds the database with initial data if needed
 * This function is called during application startup
 */
export async function seedDatabase() {
  console.log('Checking if database needs seeding...');

  try {
    // Get superadmin credentials from environment variables
    const superadminEmail = process.env.DEFAULT_SUPERADMIN_EMAIL || '<EMAIL>';
    const superadminPassword = process.env.DEFAULT_SUPERADMIN_PASSWORD || 'SuperAdmin@123456';
    const superadminName = process.env.DEFAULT_SUPERADMIN_NAME || 'System Super Administrator';

    // Get regular admin credentials from environment variables
    const adminEmail = process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'Admin@123456';
    const adminName = process.env.DEFAULT_ADMIN_NAME || 'System Administrator';

    // Check if superadmin user exists
    console.log(`Checking for existing superadmin user with email: ${superadminEmail}`);

    const existingSuperadmin = await prisma.user.findFirst({
      where: {
        email: superadminEmail,
        role: 'SUPERADMIN'
      }
    });

    if (existingSuperadmin) {
      console.log('Superadmin user already exists:', existingSuperadmin.email);
    } else {
      console.log('No superadmin user found. Creating default superadmin user...');

      // Hash the password
      const hashedSuperadminPassword = await hash(superadminPassword, 12);

      // Create the superadmin user
      const superadmin = await prisma.user.create({
        data: {
          email: superadminEmail,
          name: superadminName,
          password: hashedSuperadminPassword,
          role: 'SUPERADMIN',
          emailVerified: new Date(),
        }
      });

      console.log('Default superadmin user created:', superadmin.email);
    }

    // Check if user with admin email exists (regardless of role)
    console.log(`Checking for existing admin user with email: ${adminEmail}`);

    const existingUser = await prisma.user.findUnique({
      where: {
        email: adminEmail
      }
    });

    if (existingUser) {
      console.log('User with admin email already exists:', existingUser.email);

      // Check if the user is already an ADMIN
      if (existingUser.role === 'ADMIN') {
        console.log('User already has ADMIN role');
      } else {
        console.log(`Updating user role from ${existingUser.role} to ADMIN...`);

        // Update the user's role to ADMIN
        const updatedUser = await prisma.user.update({
          where: { id: existingUser.id },
          data: { role: 'ADMIN' }
        });

        console.log('User role updated to ADMIN:', updatedUser.email);
      }
    } else {
      console.log('No user with admin email found. Creating default admin user...');

      // Hash the password
      const hashedAdminPassword = await hash(adminPassword, 12);

      // Create the admin user
      const admin = await prisma.user.create({
        data: {
          email: adminEmail,
          name: adminName,
          password: hashedAdminPassword,
          role: 'ADMIN',
          emailVerified: new Date(),
        }
      });

      console.log('Default admin user created:', admin.email);
    }

    // Seed fee configurations
    await seedFeeConfigurations();

    // Seed subscription tier configurations
    await seedSubscriptionTiers();

    // Seed withdrawal test data
    await seedWithdrawals();

    // Seed partner test data
    await seedPartners();

    // Seed security settings
    await seedSecuritySettings();

    // Seed Elite Communication System
    await seedEliteCommunicationSystem();
  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Seeds default fee configurations into the database
 */
async function seedFeeConfigurations() {
  console.log('Checking fee configurations...');

  try {
    // 1. Platform commission fee (6% of ticket sales)
    await prisma.feeConfiguration.upsert({
      where: { id: 'platform-commission-fee' },
      update: {},
      create: {
        id: 'platform-commission-fee',
        name: 'Platform Commission',
        description: 'Platform commission fee for ticket sales',
        feeType: FeeType.PLATFORM_COMMISSION,
        value: 6.0, // 6%
        isPercentage: true,
        isActive: true,
        appliesTo: ['ORGANIZER'],
        transactionType: ['TICKET_SALE'],
        effectiveFrom: new Date(),
      },
    });

    // 2. Processing fee for organizers (3.5%)
    await prisma.feeConfiguration.upsert({
      where: { id: 'organizer-processing-fee' },
      update: {},
      create: {
        id: 'organizer-processing-fee',
        name: 'Organizer Processing Fee',
        description: 'Payment processing fee for organizers',
        feeType: FeeType.PROCESSING_FEE,
        value: 3.5, // 3.5%
        isPercentage: true,
        isActive: true,
        appliesTo: ['ORGANIZER'],
        transactionType: ['TICKET_SALE', 'PROCESSING_FEE'],
        effectiveFrom: new Date(),
      },
    });

    // 3. Processing fee for vendors (3.5%)
    await prisma.feeConfiguration.upsert({
      where: { id: 'vendor-processing-fee' },
      update: {},
      create: {
        id: 'vendor-processing-fee',
        name: 'Vendor Processing Fee',
        description: 'Payment processing fee for vendors',
        feeType: FeeType.PROCESSING_FEE,
        value: 3.5, // 3.5%
        isPercentage: true,
        isActive: true,
        appliesTo: ['VENDOR'],
        transactionType: ['VENDOR_SALE', 'PROCESSING_FEE'],
        effectiveFrom: new Date(),
      },
    });

    // 4. POS rental fee for vendors (K1000 flat fee)
    await prisma.feeConfiguration.upsert({
      where: { id: 'pos-rental-fee' },
      update: {},
      create: {
        id: 'pos-rental-fee',
        name: 'POS Rental Fee',
        description: 'Flat fee for POS device rental',
        feeType: FeeType.POS_RENTAL_FEE,
        value: 1000, // K1000
        isPercentage: false,
        isActive: true,
        appliesTo: ['VENDOR'],
        transactionType: ['POS_RENTAL_FEE'],
        effectiveFrom: new Date(),
      },
    });

    console.log('Default fee configurations seeded successfully');
  } catch (error) {
    console.error('Error seeding fee configurations:', error);
  }
}

/**
 * Seeds default subscription tier pricing configurations into the database
 */
async function seedSubscriptionTiers() {
  console.log('Checking subscription tier configurations...');

  try {
    // Define default tier prices
    const defaultTiers = [
      {
        id: 'none-tier',
        tier: 'NONE',
        monthlyPrice: 0,
        yearlyPrice: 0,
        commissionRate: 0.06, // 6%
        maxEvents: 2,
        maxTeamMembers: 0,
        maxEmailCampaigns: 0,
        maxAnalyticsReports: 0,
        maxVendorManagement: 0,
        isActive: true,
      },
      {
        id: 'basic-tier',
        tier: 'BASIC',
        monthlyPrice: 29.99,
        yearlyPrice: 299.99,
        commissionRate: 0.06, // 6%
        maxEvents: 5,
        maxTeamMembers: 1,
        maxEmailCampaigns: 1,
        maxAnalyticsReports: 3,
        maxVendorManagement: 1,
        isActive: true,
      },
      {
        id: 'premium-tier',
        tier: 'PREMIUM',
        monthlyPrice: 79.99,
        yearlyPrice: 799.99,
        commissionRate: 0.05, // 5%
        maxEvents: 15,
        maxTeamMembers: 5,
        maxEmailCampaigns: 5,
        maxAnalyticsReports: 10,
        maxVendorManagement: 5,
        isActive: true,
      },
      {
        id: 'elite-tier',
        tier: 'ELITE',
        monthlyPrice: 199.99,
        yearlyPrice: 1999.99,
        commissionRate: 0.04, // 4%
        maxEvents: null, // Unlimited
        maxTeamMembers: null, // Unlimited
        maxEmailCampaigns: null, // Unlimited
        maxAnalyticsReports: null, // Unlimited
        maxVendorManagement: null, // Unlimited
        isActive: true,
      },
    ];

    // Insert or update default tiers
    for (const tier of defaultTiers) {
      await prisma.subscriptionTierPrice.upsert({
        where: { tier: tier.tier },
        update: {
          monthlyPrice: tier.monthlyPrice,
          yearlyPrice: tier.yearlyPrice,
          commissionRate: tier.commissionRate,
          maxEvents: tier.maxEvents,
          maxTeamMembers: tier.maxTeamMembers,
          maxEmailCampaigns: tier.maxEmailCampaigns,
          maxAnalyticsReports: tier.maxAnalyticsReports,
          maxVendorManagement: tier.maxVendorManagement,
          isActive: tier.isActive,
          updatedAt: new Date()
        },
        create: {
          id: tier.id,
          tier: tier.tier,
          monthlyPrice: tier.monthlyPrice,
          yearlyPrice: tier.yearlyPrice,
          commissionRate: tier.commissionRate,
          maxEvents: tier.maxEvents,
          maxTeamMembers: tier.maxTeamMembers,
          maxEmailCampaigns: tier.maxEmailCampaigns,
          maxAnalyticsReports: tier.maxAnalyticsReports,
          maxVendorManagement: tier.maxVendorManagement,
          isActive: tier.isActive
        }
      });
    }

    console.log('Default subscription tier configurations seeded successfully');
  } catch (error) {
    console.error('Error seeding subscription tier configurations:', error);
  }
}

/**
 * Seeds withdrawal test data into the database
 */
async function seedWithdrawals() {
  console.log('Checking withdrawal test data...');

  try {
    // Check if withdrawals already exist
    const existingWithdrawals = await prisma.withdrawal.count();

    if (existingWithdrawals > 0) {
      console.log(`Withdrawal test data already exists (${existingWithdrawals} withdrawals found)`);
      return;
    }

    // Get all organizers
    const organizers = await prisma.user.findMany({
      where: {
        role: 'ORGANIZER',
      },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    if (organizers.length === 0) {
      console.log('No organizers found. Skipping withdrawal seeding.');
      return;
    }

    console.log(`Found ${organizers.length} organizers for withdrawal seeding`);

    // Create bank accounts for organizers if they don't have one
    for (const organizer of organizers) {
      const existingBankAccounts = await prisma.bankAccount.count({
        where: {
          userId: organizer.id,
        },
      });

      if (existingBankAccounts === 0) {
        console.log(`Creating bank account for organizer: ${organizer.name || organizer.email}`);

        await prisma.bankAccount.create({
          data: {
            userId: organizer.id,
            bankName: 'Test Bank',
            accountNumber: `ACC${Math.random().toString().slice(2, 12)}`,
            accountName: organizer.name || 'Account Holder',
            branchCode: '001',
            swiftCode: 'TESTBIC',
            routingNumber: '*********',
            isDefault: true,
            isVerified: true,
            verificationDate: new Date(),
          },
        });
      }
    }

    // Get all bank accounts for organizers
    const bankAccounts = await prisma.bankAccount.findMany({
      where: {
        user: {
          role: 'ORGANIZER',
        },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (bankAccounts.length === 0) {
      console.log('No bank accounts found. Skipping withdrawal seeding.');
      return;
    }

    // Create sample withdrawals
    const statuses = ['Pending', 'Approved', 'Processed', 'Rejected'];
    const withdrawalsToCreate = Math.min(20, bankAccounts.length * 3); // Create up to 20 withdrawals

    console.log(`Creating ${withdrawalsToCreate} sample withdrawals...`);

    for (let i = 0; i < withdrawalsToCreate; i++) {
      const bankAccount = bankAccounts[Math.floor(Math.random() * bankAccounts.length)];
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const amount = Math.floor(Math.random() * 4900) + 100; // Random amount between 100-5000
      const requestDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000); // Random date within last 30 days

      // If status is not Pending, add a processed date
      const processedDate = status !== 'Pending'
        ? new Date(requestDate.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000) // Random date within 7 days after request
        : null;

      await prisma.withdrawal.create({
        data: {
          userId: bankAccount.user.id,
          bankAccountId: bankAccount.id,
          amount,
          status,
          requestDate,
          processedDate,
          description: `Withdrawal request #${i + 1}`,
          notes: status !== 'Pending' ? `${status} by system` : null,
          reference: status === 'Processed' ? `REF${Date.now()}${i}` : null,
        },
      });
    }

    console.log('Withdrawal test data seeded successfully');
  } catch (error) {
    console.error('Error seeding withdrawal test data:', error);
  }
}

/**
 * Seeds partner test data into the database
 */
async function seedPartners() {
  console.log('Checking partner test data...');

  try {
    // Check if partners already exist
    const existingPartners = await prisma.partner.count();

    if (existingPartners > 0) {
      console.log(`Partner test data already exists (${existingPartners} partners found)`);
      return;
    }

    // Create partner users first
    const partnerUsers = [
      {
        id: 'partner-user-1',
        name: 'John Smith',
        email: '<EMAIL>',
        role: 'PARTNER',
        isActive: true,
        emailVerified: new Date(),
      },
      {
        id: 'partner-user-2',
        name: 'Maria Garcia',
        email: '<EMAIL>',
        role: 'PARTNER',
        isActive: true,
        emailVerified: new Date(),
      },
      {
        id: 'partner-user-3',
        name: 'David Wilson',
        email: '<EMAIL>',
        role: 'PARTNER',
        isActive: true,
        emailVerified: new Date(),
      },
      {
        id: 'partner-user-4',
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        role: 'PARTNER',
        isActive: true,
        emailVerified: new Date(),
      }
    ];

    for (const userData of partnerUsers) {
      const existingUser = await prisma.user.findUnique({
        where: { email: userData.email }
      });

      if (!existingUser) {
        await prisma.user.create({
          data: userData
        });
        console.log(`Created partner user: ${userData.email}`);
      }
    }

    // Create partner businesses
    const partners = [
      {
        id: 'partner-1',
        userId: 'partner-user-1',
        businessName: 'The Grand Hotel',
        partnerType: 'HOTEL',
        description: 'Luxury hotel in the heart of Lusaka with premium amenities and world-class service',
        tier: 'PREMIUM',
        address: '123 Independence Avenue',
        city: 'Lusaka',
        province: 'Lusaka',
        country: 'Zambia',
        phone: '+260 211 123456',
        email: '<EMAIL>',
        website: 'https://grandhotel.com',
        isVerified: true,
        isActive: true,
        verifiedAt: new Date(),
        acceptsNfcPayments: true,
        rating: 4.8,
        totalReviews: 245,
        totalSales: 125000,
        totalOrders: 1250,
      },
      {
        id: 'partner-2',
        userId: 'partner-user-2',
        businessName: 'Bella Vista Restaurant',
        partnerType: 'RESTAURANT',
        description: 'Fine dining restaurant offering international cuisine with a focus on fresh, local ingredients',
        tier: 'BASIC',
        address: '456 Cairo Road',
        city: 'Lusaka',
        province: 'Lusaka',
        country: 'Zambia',
        phone: '+260 211 789012',
        email: '<EMAIL>',
        website: 'https://bellavista.com',
        isVerified: true,
        isActive: true,
        verifiedAt: new Date(),
        acceptsNfcPayments: true,
        rating: 4.6,
        totalReviews: 189,
        totalSales: 85000,
        totalOrders: 890,
      },
      {
        id: 'partner-3',
        userId: 'partner-user-3',
        businessName: 'The Blue Bar',
        partnerType: 'BAR',
        description: 'Trendy cocktail bar with live music and an extensive selection of premium spirits',
        tier: 'BASIC',
        address: '789 Kafue Road',
        city: 'Kitwe',
        province: 'Copperbelt',
        country: 'Zambia',
        phone: '+260 212 345678',
        email: '<EMAIL>',
        isVerified: true,
        isActive: true,
        verifiedAt: new Date(),
        acceptsNfcPayments: true,
        rating: 4.4,
        totalReviews: 156,
        totalSales: 45000,
        totalOrders: 567,
      },
      {
        id: 'partner-4',
        userId: 'partner-user-4',
        businessName: 'Pulse Nightclub',
        partnerType: 'NIGHTCLUB',
        description: 'Premier nightclub featuring top DJs and an unforgettable nightlife experience',
        tier: 'PREMIUM',
        address: '321 Great East Road',
        city: 'Lusaka',
        province: 'Lusaka',
        country: 'Zambia',
        phone: '+260 211 987654',
        email: '<EMAIL>',
        isVerified: false,
        isActive: true,
        acceptsNfcPayments: false,
        rating: 4.2,
        totalReviews: 98,
        totalSales: 32000,
        totalOrders: 234,
      }
    ];

    for (const partnerData of partners) {
      await prisma.partner.create({
        data: partnerData
      });
      console.log(`Created partner: ${partnerData.businessName}`);
    }

    // Create some partner promotions
    const promotions = [
      {
        id: 'promo-1',
        partnerId: 'partner-2',
        title: '20% Off All Meals',
        description: 'Get 20% discount on all food items for event attendees',
        discountType: 'PERCENTAGE',
        discountValue: 20,
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        isActive: true,
        maxUsage: 100,
        usageCount: 45,
      },
      {
        id: 'promo-2',
        partnerId: 'partner-3',
        title: 'Happy Hour Special',
        description: 'Buy one get one free on selected cocktails during happy hour',
        discountType: 'PERCENTAGE',
        discountValue: 50,
        startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
        isActive: true,
        usageCount: 23,
      }
    ];

    for (const promoData of promotions) {
      await prisma.partnerPromotion.create({
        data: promoData
      });
      console.log(`Created promotion: ${promoData.title}`);
    }

    console.log('Partner test data seeded successfully');
  } catch (error) {
    console.error('Error seeding partner test data:', error);
  }
}

/**
 * Seeds security settings into the database
 */
async function seedSecuritySettings() {
  console.log('Checking security settings...');

  try {
    // Check for rate limiting configuration
    const existingRateLimitConfig = await prisma.systemSetting.findUnique({
      where: { key: 'login_rate_limit_config' }
    });

    if (existingRateLimitConfig) {
      console.log('Rate limiting configuration already exists');
    } else {
      console.log('Creating default rate limiting configuration');
      await prisma.systemSetting.create({
        data: {
          key: 'login_rate_limit_config',
          value: DEFAULT_RATE_LIMIT_CONFIG as any,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
      console.log('Default rate limiting configuration created');
    }

    // Check for password strength configuration
    const existingPasswordConfig = await prisma.systemSetting.findUnique({
      where: { key: 'password_strength_config' }
    });

    if (existingPasswordConfig) {
      console.log('Password strength configuration already exists');
    } else {
      console.log('Creating default password strength configuration');
      await prisma.systemSetting.create({
        data: {
          key: 'password_strength_config',
          value: DEFAULT_PASSWORD_STRENGTH_CONFIG as any,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
      console.log('Default password strength configuration created');
    }
  } catch (error) {
    console.error('Error seeding security settings:', error);
  }
}

/**
 * Seeds the Elite Communication System with comprehensive sample data
 * This includes users, events, subscriptions, attendee profiles, chat rooms, messages, and meeting requests
 */
async function seedEliteCommunicationSystem() {
  console.log('🚀 Starting Elite Communication System seeding...');

  try {
    // Sample user data for Elite Communication System
    const sampleUsers = [
      {
        name: 'Sarah Chen',
        email: '<EMAIL>',
        company: 'TechCorp Solutions',
        role: 'Senior Software Engineer',
        industry: 'Technology',
        bio: 'Passionate about AI and machine learning. Looking to connect with fellow tech enthusiasts and explore collaboration opportunities.',
        interests: ['Artificial Intelligence', 'Machine Learning', 'Cloud Computing', 'Open Source'],
        networkingGoals: 'Connect with AI researchers and potential collaborators for open source projects',
        linkedinUrl: 'https://linkedin.com/in/sarahchen',
        timezone: 'America/Los_Angeles',
        tier: 'ELITE_PRO'
      },
      {
        name: 'Marcus Johnson',
        email: '<EMAIL>',
        company: 'FinancePlus',
        role: 'Investment Director',
        industry: 'Finance',
        bio: 'Experienced investment professional specializing in fintech startups. Always interested in innovative financial solutions.',
        interests: ['Fintech', 'Blockchain', 'Investment Strategy', 'Startups'],
        networkingGoals: 'Discover promising fintech startups and connect with entrepreneurs',
        linkedinUrl: 'https://linkedin.com/in/marcusjohnson',
        timezone: 'America/New_York',
        tier: 'ELITE_PRO'
      },
      {
        name: 'Dr. Emily Rodriguez',
        email: '<EMAIL>',
        company: 'HealthTech Innovations',
        role: 'Chief Medical Officer',
        industry: 'Healthcare',
        bio: 'Medical doctor turned healthcare technology executive. Focused on digital health solutions that improve patient outcomes.',
        interests: ['Digital Health', 'Telemedicine', 'Medical AI', 'Patient Care'],
        networkingGoals: 'Connect with healthcare innovators and discuss digital transformation in medicine',
        linkedinUrl: 'https://linkedin.com/in/emilyrodriguez',
        timezone: 'America/Chicago',
        tier: 'ELITE'
      },
      {
        name: 'David Kim',
        email: '<EMAIL>',
        company: 'MarketingPro Agency',
        role: 'Creative Director',
        industry: 'Marketing',
        bio: 'Award-winning creative director with 10+ years in digital marketing. Passionate about brand storytelling and user experience.',
        interests: ['Brand Strategy', 'Digital Marketing', 'UX Design', 'Content Creation'],
        networkingGoals: 'Meet potential clients and collaborate with other creative professionals',
        linkedinUrl: 'https://linkedin.com/in/davidkim',
        timezone: 'America/Los_Angeles',
        tier: 'ELITE'
      },
      {
        name: 'Lisa Thompson',
        email: '<EMAIL>',
        company: 'Independent Consultant',
        role: 'Business Strategy Consultant',
        industry: 'Consulting',
        bio: 'Independent consultant helping startups and SMEs develop growth strategies. Former McKinsey consultant.',
        interests: ['Business Strategy', 'Startups', 'Growth Hacking', 'Leadership'],
        networkingGoals: 'Find new clients and stay updated on industry trends',
        linkedinUrl: 'https://linkedin.com/in/lisathompson',
        timezone: 'America/New_York',
        tier: 'ELITE_PRO'
      },
      {
        name: 'Ahmed Hassan',
        email: '<EMAIL>',
        company: 'RetailTech Solutions',
        role: 'Product Manager',
        industry: 'Retail Technology',
        bio: 'Product manager focused on e-commerce and retail technology solutions. Experienced in agile development and user research.',
        interests: ['E-commerce', 'Product Management', 'User Research', 'Agile'],
        networkingGoals: 'Connect with other product managers and learn about emerging retail trends',
        linkedinUrl: 'https://linkedin.com/in/ahmedhassan',
        timezone: 'Europe/London',
        tier: 'ELITE'
      },
      {
        name: 'Jennifer Wu',
        email: '<EMAIL>',
        company: 'DataAnalytics Corp',
        role: 'Data Scientist',
        industry: 'Data Analytics',
        bio: 'Data scientist with expertise in predictive modeling and business intelligence. PhD in Statistics from Stanford.',
        interests: ['Data Science', 'Machine Learning', 'Statistics', 'Business Intelligence'],
        networkingGoals: 'Share knowledge and learn about new data science applications',
        linkedinUrl: 'https://linkedin.com/in/jenniferwu',
        timezone: 'America/Los_Angeles',
        tier: 'ELITE'
      },
      {
        name: 'Robert Brown',
        email: '<EMAIL>',
        company: 'Advanced Manufacturing Inc',
        role: 'Operations Director',
        industry: 'Manufacturing',
        bio: 'Operations expert with 15+ years in manufacturing. Specializing in lean processes and automation.',
        interests: ['Lean Manufacturing', 'Automation', 'Supply Chain', 'Quality Control'],
        networkingGoals: 'Learn about new manufacturing technologies and best practices',
        linkedinUrl: 'https://linkedin.com/in/robertbrown',
        timezone: 'America/Chicago',
        tier: 'BASIC'
      },
      {
        name: 'Maria Garcia',
        email: '<EMAIL>',
        company: 'Global Impact Foundation',
        role: 'Program Director',
        industry: 'Non-Profit',
        bio: 'Dedicated to social impact and sustainable development. Leading programs that address global challenges.',
        interests: ['Social Impact', 'Sustainability', 'Community Development', 'Fundraising'],
        networkingGoals: 'Connect with like-minded professionals and potential partners',
        linkedinUrl: 'https://linkedin.com/in/mariagarcia',
        timezone: 'America/New_York',
        tier: 'ELITE'
      },
      {
        name: 'James Wilson',
        email: '<EMAIL>',
        company: 'Premier Real Estate',
        role: 'Senior Broker',
        industry: 'Real Estate',
        bio: 'Commercial real estate broker with expertise in tech company relocations and office space optimization.',
        interests: ['Commercial Real Estate', 'Property Investment', 'Urban Planning', 'Architecture'],
        networkingGoals: 'Meet potential clients and stay informed about market trends',
        linkedinUrl: 'https://linkedin.com/in/jameswilson',
        timezone: 'America/Los_Angeles',
        tier: 'ELITE'
      }
    ];

    // Check if Elite Communication users already exist
    const existingEliteUsers = await prisma.user.count({
      where: {
        email: {
          in: sampleUsers.map(u => u.email)
        }
      }
    });

    if (existingEliteUsers > 0) {
      console.log(`✅ Elite Communication users already exist (${existingEliteUsers} found). Skipping Elite Communication seeding.`);
      return;
    }

    console.log('👥 Creating sample users for Elite Communication System...');

    // Create users
    const createdUsers = [];
    for (const userData of sampleUsers) {
      const hashedPassword = await hash('EliteUser123!', 12);
      const user = await prisma.user.create({
        data: {
          name: userData.name,
          email: userData.email,
          password: hashedPassword,
          role: 'USER',
          emailVerified: new Date(),
          image: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userData.name.replace(' ', '')}`
        }
      });
      createdUsers.push({ ...user, userData });
    }

    console.log(`✅ Created ${createdUsers.length} Elite Communication users`);

    // Create sample events for Elite Communication
    console.log('🎪 Creating sample events...');

    // Get an organizer to create events
    let organizer = await prisma.user.findFirst({
      where: { role: 'ORGANIZER' }
    });

    if (!organizer) {
      console.log('📝 No organizer found. Creating a sample organizer...');
      const hashedPassword = await hash('Organizer123!', 12);
      organizer = await prisma.user.create({
        data: {
          name: 'Elite Event Organizer',
          email: '<EMAIL>',
          password: hashedPassword,
          role: 'ORGANIZER',
          emailVerified: new Date(),
          subscriptionTier: 'PREMIUM'
        }
      });
    }

    const sampleEvents = [
      {
        title: 'Tech Innovation Summit 2024',
        description: 'Join industry leaders and innovators for a day of cutting-edge technology discussions, networking, and collaboration opportunities.',
        location: 'San Francisco Convention Center',
        venue: 'Main Auditorium',
        startDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000 + 8 * 60 * 60 * 1000), // 8 hours later
        startTime: '09:00',
        endTime: '17:00',
        eventType: 'PHYSICAL',
        category: 'TECHNOLOGY',
        status: 'Published',
        hasStadiumSeating: false
      },
      {
        title: 'Global Finance & Fintech Conference',
        description: 'Explore the future of finance with blockchain, AI, and digital transformation. Network with financial professionals and fintech entrepreneurs.',
        location: 'New York Financial District',
        venue: 'Finance Tower Conference Center',
        startDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000), // 45 days from now
        endDate: new Date(Date.now() + 47 * 24 * 60 * 60 * 1000), // 2 days later
        startTime: '08:00',
        endTime: '18:00',
        eventType: 'PHYSICAL',
        category: 'BUSINESS',
        status: 'Published',
        hasStadiumSeating: false
      },
      {
        title: 'Healthcare Innovation Symposium',
        description: 'Discover the latest in digital health, telemedicine, and medical AI. Connect with healthcare professionals and technology innovators.',
        location: 'Chicago Medical Center',
        venue: 'Innovation Hub',
        startDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
        endDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000 + 6 * 60 * 60 * 1000), // 6 hours later
        startTime: '10:00',
        endTime: '16:00',
        eventType: 'HYBRID',
        category: 'HEALTH_AND_WELLNESS',
        status: 'Published',
        hasStadiumSeating: false
      }
    ];

    const createdEvents = [];
    for (const eventData of sampleEvents) {
      const event = await prisma.event.create({
        data: {
          ...eventData,
          userId: organizer.id
        }
      });
      createdEvents.push(event);
    }

    console.log(`✅ Created ${createdEvents.length} sample events`);

    // Create Elite Communication subscriptions and attendee profiles
    console.log('💎 Creating Elite Communication subscriptions and attendee profiles...');

    const createdProfiles = [];
    for (let i = 0; i < createdUsers.length; i++) {
      const user = createdUsers[i];
      const userData = user.userData;

      // Create Elite Communication subscription for each event
      for (const event of createdEvents) {
        const eliteCommunication = await prisma.eliteCommunication.create({
          data: {
            userId: user.id,
            eventId: event.id,
            tier: userData.tier as any,
            subscriptionType: 'PER_EVENT',
            isActive: true,
            expiresAt: new Date(event.endDate.getTime() + 7 * 24 * 60 * 60 * 1000), // 7 days after event
            purchasePrice: userData.tier === 'BASIC' ? 0 : userData.tier === 'ELITE' ? 29.99 : 49.99
          }
        });

        // Create attendee profile
        const attendeeProfile = await prisma.attendeeProfile.create({
          data: {
            userId: user.id,
            eventId: event.id,
            displayName: userData.name,
            bio: userData.bio,
            company: userData.company,
            role: userData.role,
            industry: userData.industry,
            interests: userData.interests,
            networkingGoals: userData.networkingGoals,
            profilePhoto: user.image,
            linkedinUrl: userData.linkedinUrl,
            isDiscoverable: userData.tier !== 'BASIC',
            privacyLevel: userData.tier === 'BASIC' ? 'HIDDEN' : userData.tier === 'ELITE' ? 'ELITE_ONLY' : 'PUBLIC',
            allowMessages: userData.tier === 'BASIC' ? 'NONE' : userData.tier === 'ELITE' ? 'ELITE_ONLY' : 'EVERYONE',
            allowMeetings: userData.tier !== 'BASIC',
            timezone: userData.timezone,
            availableHours: {
              monday: { start: '09:00', end: '17:00', available: true },
              tuesday: { start: '09:00', end: '17:00', available: true },
              wednesday: { start: '09:00', end: '17:00', available: true },
              thursday: { start: '09:00', end: '17:00', available: true },
              friday: { start: '09:00', end: '17:00', available: true },
              saturday: { start: '10:00', end: '14:00', available: false },
              sunday: { start: '10:00', end: '14:00', available: false }
            }
          }
        });

        createdProfiles.push({ profile: attendeeProfile, user, event, tier: userData.tier });
      }
    }

    console.log(`✅ Created ${createdProfiles.length} attendee profiles with Elite subscriptions`);

    // Create chat rooms for each event
    console.log('💬 Creating Elite chat rooms...');

    const createdChatRooms = [];
    for (const event of createdEvents) {
      // Create Elite exclusive chat room
      const eliteRoom = await prisma.chatRoom.create({
        data: {
          eventId: event.id,
          name: `Elite Networking - ${event.title}`,
          description: 'Exclusive networking space for Elite and Elite Pro members',
          roomType: 'ELITE_EXCLUSIVE',
          isActive: true,
          maxMembers: 100,
          createdById: organizer.id
        }
      });

      // Create Elite Pro exclusive chat room
      const eliteProRoom = await prisma.chatRoom.create({
        data: {
          eventId: event.id,
          name: `Elite Pro VIP Lounge - ${event.title}`,
          description: 'VIP networking space exclusively for Elite Pro members',
          roomType: 'ELITE_PRO_EXCLUSIVE',
          isActive: true,
          maxMembers: 50,
          createdById: organizer.id
        }
      });

      createdChatRooms.push({ elite: eliteRoom, elitePro: eliteProRoom, event });
    }

    console.log(`✅ Created ${createdChatRooms.length * 2} chat rooms (Elite and Elite Pro)`);

    // Add members to chat rooms based on their tier
    console.log('👥 Adding members to chat rooms...');

    for (const roomData of createdChatRooms) {
      const eventProfiles = createdProfiles.filter(p => p.event.id === roomData.event.id);

      // Add Elite and Elite Pro members to Elite room
      const eliteMembers = eventProfiles.filter(p => p.tier === 'ELITE' || p.tier === 'ELITE_PRO');
      for (const member of eliteMembers) {
        await prisma.chatRoomMember.create({
          data: {
            chatRoomId: roomData.elite.id,
            userId: member.user.id,
            joinedAt: new Date(),
            isModerator: false,
            isMuted: false
          }
        });
      }

      // Add only Elite Pro members to Elite Pro room
      const eliteProMembers = eventProfiles.filter(p => p.tier === 'ELITE_PRO');
      for (const member of eliteProMembers) {
        await prisma.chatRoomMember.create({
          data: {
            chatRoomId: roomData.elitePro.id,
            userId: member.user.id,
            joinedAt: new Date(),
            isModerator: false,
            isMuted: false
          }
        });
      }
    }

    // Create sample messages between users
    console.log('📨 Creating sample messages...');

    const sampleMessages = [
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        content: 'Hi Marcus! I noticed your interest in fintech startups. I\'m working on an AI-powered financial analytics platform and would love to discuss potential collaboration opportunities.',
        eventIndex: 0
      },
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        content: 'Hi Sarah! That sounds fascinating. I\'d definitely be interested in learning more about your platform. Are you available for a quick call this week?',
        eventIndex: 0
      },
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        content: 'Jennifer, your background in data science is impressive! We\'re looking to implement predictive analytics in our healthcare platform. Would you be open to discussing a potential consulting opportunity?',
        eventIndex: 2
      },
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        content: 'Lisa, I saw your presentation on growth strategies. Our agency is working with several startups that could benefit from your expertise. Let\'s connect!',
        eventIndex: 1
      }
    ];

    for (const messageData of sampleMessages) {
      const sender = createdUsers.find(u => u.email === messageData.senderEmail);
      const receiver = createdUsers.find(u => u.email === messageData.receiverEmail);
      const event = createdEvents[messageData.eventIndex];

      if (sender && receiver && event) {
        const senderProfile = createdProfiles.find(p => p.user.id === sender.id && p.event.id === event.id);
        const receiverProfile = createdProfiles.find(p => p.user.id === receiver.id && p.event.id === event.id);

        if (senderProfile && receiverProfile) {
          await prisma.message.create({
            data: {
              senderId: senderProfile.profile.id,
              receiverId: receiverProfile.profile.id,
              eventId: event.id,
              content: messageData.content,
              messageType: 'TEXT',
              isRead: Math.random() > 0.5,
              readAt: Math.random() > 0.5 ? new Date() : null
            }
          });
        }
      }
    }

    // Create sample meeting requests
    console.log('🤝 Creating sample meeting requests...');

    const sampleMeetingRequests = [
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        title: 'AI Fintech Collaboration Discussion',
        description: 'Let\'s discuss potential collaboration opportunities between our AI platform and your fintech investments.',
        meetingType: 'VIRTUAL',
        eventIndex: 0
      },
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        title: 'Healthcare Analytics Consulting',
        description: 'Exploring data science consulting opportunities for our healthcare platform.',
        meetingType: 'VIRTUAL',
        eventIndex: 2
      }
    ];

    for (const meetingData of sampleMeetingRequests) {
      const sender = createdUsers.find(u => u.email === meetingData.senderEmail);
      const receiver = createdUsers.find(u => u.email === meetingData.receiverEmail);
      const event = createdEvents[meetingData.eventIndex];

      if (sender && receiver && event) {
        const senderProfile = createdProfiles.find(p => p.user.id === sender.id && p.event.id === event.id);
        const receiverProfile = createdProfiles.find(p => p.user.id === receiver.id && p.event.id === event.id);

        if (senderProfile && receiverProfile) {
          const proposedStartTime = new Date(event.startDate.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000);
          const proposedEndTime = new Date(proposedStartTime.getTime() + 60 * 60 * 1000); // 1 hour meeting

          await prisma.meetingRequest.create({
            data: {
              senderId: senderProfile.profile.id,
              receiverId: receiverProfile.profile.id,
              eventId: event.id,
              title: meetingData.title,
              description: meetingData.description,
              proposedStartTime,
              proposedEndTime,
              timezone: senderProfile.user.userData.timezone,
              meetingType: meetingData.meetingType as any,
              meetingUrl: meetingData.meetingType === 'VIRTUAL' ? 'https://meet.google.com/sample-meeting' : null,
              location: meetingData.meetingType === 'IN_PERSON' ? event.location : null,
              status: Math.random() > 0.5 ? 'PENDING' : 'ACCEPTED'
            }
          });
        }
      }
    }

    console.log('✅ Elite Communication System seeding completed successfully!');
    console.log(`📊 Summary:
    - ${createdUsers.length} sample users
    - ${createdEvents.length} sample events
    - ${createdProfiles.length} attendee profiles with Elite subscriptions
    - ${createdChatRooms.length * 2} chat rooms (Elite and Elite Pro)
    - ${sampleMessages.length} direct messages
    - ${sampleMeetingRequests.length} meeting requests`);

  } catch (error) {
    console.error('❌ Error seeding Elite Communication System:', error);
    // Don't throw the error to prevent breaking the main seeding process
  }
}
