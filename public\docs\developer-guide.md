# QuickTime Events API Developer Guide

This guide provides detailed information on how to use the QuickTime Events API effectively. It covers authentication, rate limiting, permissions, and best practices.

## Getting Started

### 1. Create an Account

Before you can use the API, you need to create an account on the QuickTime Events platform. Visit [https://your-domain.com/auth/register](https://your-domain.com/auth/register) to sign up.

### 2. Generate an API Key

Once you have an account, you can generate an API key:

1. Log in to your account
2. Navigate to Dashboard > API Keys
3. Click "Create API Key"
4. Enter a name for your API key
5. Select the permissions you need
6. Set a rate limit (requests per minute)
7. Click "Create"

**Important**: You will only see the API key once after creation. Make sure to copy it and store it securely.

### 3. Make Your First API Request

Here's a simple example of how to make a request to the API using your API key:

```javascript
// JavaScript example
fetch('https://your-domain.com/api/events/published', {
  headers: {
    'X-API-Key': 'your-api-key'
  }
})
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));
```

```python
# Python example
import requests

headers = {
    'X-API-Key': 'your-api-key'
}

response = requests.get('https://your-domain.com/api/events/published', headers=headers)
data = response.json()
print(data)
```

## Authentication

All protected API endpoints require authentication. You can authenticate using an API key.

### API Key Authentication

Include your API key in the `X-API-Key` header with every request:

```
X-API-Key: your-api-key
```

## Permissions

API keys have granular permissions that control what actions they can perform. When creating an API key, you can specify which permissions to grant.

### Available Permissions

- `read:events`: View events
- `write:events`: Create and update events
- `delete:events`: Delete events
- `read:tickets`: View tickets
- `write:tickets`: Create and update tickets
- `delete:tickets`: Delete tickets
- `read:orders`: View orders
- `write:orders`: Create and update orders
- `delete:orders`: Delete orders
- `read:users`: View user information
- `write:users`: Update user information
- `read:analytics`: View analytics data

### Permission Requirements

Each API endpoint requires specific permissions. Here are some examples:

| Endpoint | Required Permissions |
|----------|---------------------|
| `/api/events/published` | None (public) |
| `/api/eventdetails/:id` | None (public) |
| `/api/external/events` | `read:events` |
| `/api/events` | `read:events` |
| `/api/events/create` | `write:events` |
| `/api/events/delete` | `delete:events` |
| `/api/tickets` | `read:tickets` |
| `/api/orders` | `read:orders` |

### Permission Errors

If you attempt to access an endpoint without the required permissions, you will receive a `403 Forbidden` response:

```json
{
  "error": "Insufficient permissions for this endpoint"
}
```

## Rate Limiting

All API endpoints are subject to rate limiting to ensure fair usage and system stability. Rate limits are applied on a per-API key basis.

### Rate Limit Headers

The following headers are included in API responses to help you track your rate limit usage:

- `X-RateLimit-Limit`: The maximum number of requests allowed per minute
- `X-RateLimit-Remaining`: The number of requests remaining in the current rate limit window
- `X-RateLimit-Reset`: The time at which the current rate limit window resets (in Unix time)

### Rate Limit Configuration

When creating an API key, you can configure the rate limit for that key. The default rate limit is 100 requests per minute.

### Rate Limit Exceeded

If you exceed your rate limit, you will receive a `429 Too Many Requests` response:

```json
{
  "error": "Rate limit exceeded. Try again later."
}
```

### Handling Rate Limits

To handle rate limits effectively, implement exponential backoff in your code:

```javascript
// JavaScript example of exponential backoff
async function fetchWithRetry(url, options, maxRetries = 5) {
  let retries = 0;
  
  while (retries < maxRetries) {
    try {
      const response = await fetch(url, options);
      
      if (response.status === 429) {
        // Rate limit exceeded
        const resetTime = response.headers.get('X-RateLimit-Reset');
        const waitTime = resetTime ? (parseInt(resetTime) * 1000) - Date.now() : Math.pow(2, retries) * 1000;
        
        console.log(`Rate limit exceeded. Waiting ${waitTime}ms before retrying...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
        retries++;
        continue;
      }
      
      return response;
    } catch (error) {
      retries++;
      if (retries === maxRetries) throw error;
      
      const waitTime = Math.pow(2, retries) * 1000;
      console.log(`Request failed. Waiting ${waitTime}ms before retrying...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
}
```

## Common API Patterns

### Pagination

Many endpoints that return lists of items support pagination. You can use the following query parameters:

- `page`: The page number (default: 1)
- `limit`: The number of items per page (default: 10)

Example:

```
GET /api/events/published?page=2&limit=20
```

Response:

```json
{
  "events": [...],
  "pagination": {
    "total": 100,
    "pages": 5,
    "page": 2,
    "limit": 20
  }
}
```

### Filtering

Some endpoints support filtering. The available filters depend on the endpoint.

Example:

```
GET /api/events/published?category=MUSIC&type=CONCERT
```

### Error Handling

All API endpoints return consistent error responses:

```json
{
  "error": "Error message"
}
```

Common HTTP status codes:

- `200 OK`: The request was successful
- `400 Bad Request`: The request was invalid
- `401 Unauthorized`: Authentication is required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: The requested resource was not found
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: An error occurred on the server

## Best Practices

### Security

- Keep your API keys secure and never expose them in client-side code
- Use the principle of least privilege: only grant the permissions your application needs
- Rotate your API keys periodically
- Set appropriate rate limits for your application

### Performance

- Use pagination for endpoints that return lists of items
- Only request the data you need
- Cache responses when appropriate
- Implement exponential backoff for rate limiting

### Monitoring

- Monitor your API usage using the analytics dashboard
- Set up alerts for unusual activity
- Keep track of your rate limit usage

## Troubleshooting

### Common Issues

1. **Authentication Errors (401)**
   - Check that you're including the API key in the `X-API-Key` header
   - Verify that your API key is valid and has not expired

2. **Permission Errors (403)**
   - Check that your API key has the required permissions for the endpoint
   - If you need additional permissions, generate a new API key with the required permissions

3. **Rate Limit Errors (429)**
   - Implement exponential backoff in your code
   - Consider increasing the rate limit for your API key
   - Optimize your code to make fewer API requests

4. **Server Errors (500)**
   - These are usually temporary issues on the server side
   - Implement retry logic with exponential backoff
   - If the issue persists, contact support

### Getting Help

If you encounter any issues or have questions about the API, please contact our support <NAME_EMAIL>.

## API Reference

For a complete reference of all available API endpoints, see the [API Documentation](https://your-domain.com/developers).

## Changelog

### v1.0.0 (2023-04-16)

- Initial release of the API
- Added support for events, tickets, and orders
- Implemented API key authentication
- Added rate limiting
- Added granular permissions
