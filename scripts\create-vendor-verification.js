const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const crypto = require('crypto');

async function createVendorVerification() {
  try {
    // First, find a user with the VENDOR role
    const user = await prisma.user.findFirst({
      where: { role: 'VENDOR' },
      include: {
        vendorProfile: true
      }
    });

    if (!user) {
      console.log('No vendor user found. Creating a test vendor user...');
      
      // Create a test vendor user
      const newUser = await prisma.user.create({
        data: {
          name: 'Test Vendor',
          email: '<EMAIL>',
          role: 'VENDOR',
          vendorProfile: {
            create: {
              businessName: 'Test Vendor Business',
              businessType: 'Company',
              productCategories: 'Food, Beverages',
              description: 'A test vendor for verification testing'
            }
          }
        }
      });
      
      console.log('Created test vendor user:', newUser.id);
      
      // Use the newly created user
      user = newUser;
    }

    console.log('Using vendor user:', user.id, user.name, user.email);
    
    // Check if the user already has a verification
    const existingVerification = await prisma.vendorVerification.findUnique({
      where: { userId: user.id }
    });
    
    if (existingVerification) {
      console.log('User already has a verification. Deleting it...');
      await prisma.vendorVerification.delete({
        where: { id: existingVerification.id }
      });
    }
    
    // Create a new verification
    const verification = await prisma.vendorVerification.create({
      data: {
        userId: user.id,
        registrationNumber: 'REG' + Math.floor(Math.random() * 1000000),
        taxPayerIdNumber: 'TPIN' + Math.floor(Math.random() * 1000000),
        phoneNumber: '+260 97' + Math.floor(Math.random() * 10000000),
        physicalAddress: '123 Test Street',
        city: 'Lusaka',
        province: 'Lusaka',
        idDocumentType: 'NATIONAL_ID',
        idDocumentPath: '/uploads/verification/test-id.jpg',
        businessLicensePath: '/uploads/verification/test-license.jpg',
        productCategories: 'Food, Beverages',
        businessDescription: 'A test vendor for verification testing',
        status: 'PENDING'
      }
    });
    
    console.log('Created vendor verification:', verification.id);
    console.log('Status:', verification.status);
    
    return verification;
  } catch (error) {
    console.error('Error creating vendor verification:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createVendorVerification()
  .then((verification) => {
    if (verification) {
      console.log('Done! Use this ID to approve the verification:', verification.id);
    } else {
      console.log('Failed to create verification.');
    }
  })
  .catch((error) => console.error('Script failed:', error));
