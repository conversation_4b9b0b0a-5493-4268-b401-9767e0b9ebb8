'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { 
  Users, 
  MessageCircle, 
  Calendar, 
  Crown, 
  Sparkles,
  ArrowRight,
  Shield,
  Zap
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { EliteTier } from '@prisma/client';
import { ELITE_PRICING_TIERS, hasFeature } from '@/config/elite-pricing';
import { EliteCommunicationTabProps } from '@/types/elite-communication';
import AttendeeDirectory from './AttendeeDirectory';
import ChatInterface from './ChatInterface';
import ChatRoomsList from './ChatRoomsList';
import AttendeeMatching from './AttendeeMatching';
import MeetingScheduler from './MeetingScheduler';
import EliteUpgradeModal from './EliteUpgradeModal';

export default function EliteCommunicationTab({
  eventId,
  currentUser,
  userTier
}: EliteCommunicationTabProps) {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState('directory');
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [subscription, setSubscription] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [selectedRecipientId, setSelectedRecipientId] = useState<string | undefined>();
  const [selectedChatRoomId, setSelectedChatRoomId] = useState<string | undefined>();

  useEffect(() => {
    fetchSubscription();
  }, [eventId]);

  const fetchSubscription = async () => {
    try {
      const response = await fetch(`/api/elite-communication/subscription?eventId=${eventId}`);
      const data = await response.json();
      setSubscription(data.subscription);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching subscription:', error);
      setLoading(false);
    }
  };

  const handleUpgrade = () => {
    setShowUpgradeModal(true);
  };

  const handleUpgradeSuccess = () => {
    setShowUpgradeModal(false);
    fetchSubscription();
  };

  const handleStartDirectMessage = (recipientId: string) => {
    setSelectedRecipientId(recipientId);
    setSelectedChatRoomId(undefined);
    setActiveTab('messages');
  };

  const handleSelectChatRoom = (chatRoomId: string) => {
    setSelectedChatRoomId(chatRoomId);
    setSelectedRecipientId(undefined);
    setActiveTab('messages');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const currentTier = subscription?.tier || EliteTier.BASIC;
  const isActive = subscription?.isActive || false;

  return (
    <div className="space-y-6">
      {/* Elite Communication Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl p-6 text-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-white/20 rounded-lg">
              <Crown className="h-6 w-6" />
            </div>
            <div>
              <h2 className="text-2xl font-bold">Elite Communication</h2>
              <p className="text-purple-100">
                Connect and network with fellow attendees
              </p>
            </div>
          </div>
          <div className="text-right">
            <Badge 
              className={`${
                currentTier === EliteTier.ELITE_PRO 
                  ? 'bg-yellow-500 text-yellow-900' 
                  : currentTier === EliteTier.ELITE 
                    ? 'bg-purple-500 text-white' 
                    : 'bg-gray-500 text-white'
              }`}
            >
              {currentTier === EliteTier.BASIC ? 'Basic' : 
               currentTier === EliteTier.ELITE ? 'Elite' : 'Elite Pro'}
            </Badge>
            {currentTier === EliteTier.BASIC && (
              <Button 
                onClick={handleUpgrade}
                className="mt-2 bg-white text-purple-600 hover:bg-gray-100"
                size="sm"
              >
                <Sparkles className="h-4 w-4 mr-2" />
                Upgrade
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Feature Access Cards */}
      {currentTier === EliteTier.BASIC && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="border-purple-200 hover:border-purple-300 transition-colors cursor-pointer" onClick={handleUpgrade}>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-sm">
                <MessageCircle className="h-4 w-4 mr-2 text-purple-600" />
                Direct Messaging
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-3">
                Send private messages to other attendees
              </p>
              <div className="flex items-center text-purple-600 text-sm font-medium">
                <Shield className="h-4 w-4 mr-1" />
                Elite Feature
                <ArrowRight className="h-4 w-4 ml-1" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-purple-200 hover:border-purple-300 transition-colors cursor-pointer" onClick={handleUpgrade}>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-sm">
                <Calendar className="h-4 w-4 mr-2 text-purple-600" />
                Meeting Scheduling
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-3">
                Schedule one-on-one meetings with attendees
              </p>
              <div className="flex items-center text-yellow-600 text-sm font-medium">
                <Crown className="h-4 w-4 mr-1" />
                Elite Pro Feature
                <ArrowRight className="h-4 w-4 ml-1" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-purple-200 hover:border-purple-300 transition-colors cursor-pointer" onClick={handleUpgrade}>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-sm">
                <Zap className="h-4 w-4 mr-2 text-purple-600" />
                Exclusive Chat Rooms
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-3">
                Access exclusive networking chat rooms
              </p>
              <div className="flex items-center text-yellow-600 text-sm font-medium">
                <Crown className="h-4 w-4 mr-1" />
                Elite Pro Feature
                <ArrowRight className="h-4 w-4 ml-1" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Communication Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="directory" className="flex items-center">
            <Users className="h-4 w-4 mr-2" />
            Directory
          </TabsTrigger>
          <TabsTrigger
            value="messages"
            disabled={!hasFeature(currentTier, 'directMessaging')}
            className="flex items-center"
          >
            <MessageCircle className="h-4 w-4 mr-2" />
            Messages
            {!hasFeature(currentTier, 'directMessaging') && (
              <Shield className="h-3 w-3 ml-1 text-gray-400" />
            )}
          </TabsTrigger>
          <TabsTrigger
            value="matching"
            disabled={!hasFeature(currentTier, 'advancedMatching')}
            className="flex items-center"
          >
            <Sparkles className="h-4 w-4 mr-2" />
            Matching
            {!hasFeature(currentTier, 'advancedMatching') && (
              <Crown className="h-3 w-3 ml-1 text-yellow-500" />
            )}
          </TabsTrigger>
          <TabsTrigger 
            value="meetings" 
            disabled={!hasFeature(currentTier, 'meetingScheduling')}
            className="flex items-center"
          >
            <Calendar className="h-4 w-4 mr-2" />
            Meetings
            {!hasFeature(currentTier, 'meetingScheduling') && (
              <Crown className="h-3 w-3 ml-1 text-yellow-500" />
            )}
          </TabsTrigger>
          <TabsTrigger 
            value="rooms" 
            disabled={!hasFeature(currentTier, 'exclusiveChatRooms')}
            className="flex items-center"
          >
            <Zap className="h-4 w-4 mr-2" />
            Chat Rooms
            {!hasFeature(currentTier, 'exclusiveChatRooms') && (
              <Crown className="h-3 w-3 ml-1 text-yellow-500" />
            )}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="directory" className="space-y-4">
          <AttendeeDirectory
            eventId={eventId}
            currentUser={currentUser}
            userTier={currentTier}
            onStartDirectMessage={handleStartDirectMessage}
          />
        </TabsContent>

        <TabsContent value="messages" className="space-y-4">
          {hasFeature(currentTier, 'directMessaging') ? (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-1">
                <ChatRoomsList
                  eventId={eventId}
                  currentUser={currentUser}
                  userTier={currentTier}
                  onRoomSelect={handleSelectChatRoom}
                  selectedRoomId={selectedChatRoomId}
                />
              </div>
              <div className="lg:col-span-2">
                {(selectedRecipientId || selectedChatRoomId) ? (
                  <ChatInterface
                    eventId={eventId}
                    currentUser={currentUser}
                    recipientId={selectedRecipientId}
                    chatRoomId={selectedChatRoomId}
                  />
                ) : (
                  <div className="flex items-center justify-center h-[600px] bg-gray-50 rounded-xl border-2 border-dashed border-gray-300">
                    <div className="text-center">
                      <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        Select a conversation
                      </h3>
                      <p className="text-gray-600">
                        Choose a chat room or start a direct message to begin chatting.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Elite Feature Required
              </h3>
              <p className="text-gray-600 mb-6">
                Upgrade to Elite to access direct messaging with other attendees.
              </p>
              <Button onClick={handleUpgrade} className="bg-purple-600 hover:bg-purple-700">
                <Sparkles className="h-4 w-4 mr-2" />
                Upgrade to Elite
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="meetings" className="space-y-4">
          {hasFeature(currentTier, 'meetingScheduling') ? (
            <MeetingScheduler 
              eventId={eventId}
              currentUser={currentUser}
              recipientId=""
            />
          ) : (
            <div className="text-center py-12">
              <Crown className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Elite Pro Feature Required
              </h3>
              <p className="text-gray-600 mb-6">
                Upgrade to Elite Pro to schedule one-on-one meetings with attendees.
              </p>
              <Button onClick={handleUpgrade} className="bg-gradient-to-r from-purple-600 to-yellow-600 hover:from-purple-700 hover:to-yellow-700">
                <Crown className="h-4 w-4 mr-2" />
                Upgrade to Elite Pro
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="matching" className="space-y-4">
          <AttendeeMatching
            eventId={eventId}
            currentUser={currentUser}
            userTier={currentTier}
            onStartDirectMessage={handleStartDirectMessage}
            onScheduleMeeting={(recipientId) => {
              // TODO: Implement meeting scheduling
              console.log('Schedule meeting with:', recipientId);
            }}
          />
        </TabsContent>

        <TabsContent value="rooms" className="space-y-4">
          {hasFeature(currentTier, 'exclusiveChatRooms') ? (
            <ChatRoomsList
              eventId={eventId}
              currentUser={currentUser}
              userTier={currentTier}
              onRoomSelect={handleSelectChatRoom}
              selectedRoomId={selectedChatRoomId}
            />
          ) : (
            <div className="text-center py-12">
              <Crown className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Elite Pro Feature Required
              </h3>
              <p className="text-gray-600 mb-6">
                Upgrade to Elite Pro to access exclusive networking chat rooms.
              </p>
              <Button onClick={handleUpgrade} className="bg-gradient-to-r from-purple-600 to-yellow-600 hover:from-purple-700 hover:to-yellow-700">
                <Crown className="h-4 w-4 mr-2" />
                Upgrade to Elite Pro
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Upgrade Modal */}
      {showUpgradeModal && (
        <EliteUpgradeModal
          eventId={eventId}
          currentTier={currentTier}
          onClose={() => setShowUpgradeModal(false)}
          onSuccess={handleUpgradeSuccess}
        />
      )}
    </div>
  );
}
