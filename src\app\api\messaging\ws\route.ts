import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth';
import { db } from '@/lib/prisma';
import { EliteTier } from '@prisma/client';

/**
 * Enhanced WebSocket handler for real-time messaging
 * Supports authentication, messaging, typing indicators, and presence
 */

// Store active connections
const activeConnections = new Map<string, {
  ws: any;
  userId: string;
  eventId: string;
  lastSeen: Date;
  status: 'online' | 'away' | 'busy' | 'offline';
  tier: EliteTier;
}>();

// Store room memberships
const roomMemberships = new Map<string, Set<string>>(); // roomId -> Set of userIds

// Store typing indicators
const typingIndicators = new Map<string, Set<string>>(); // roomId -> Set of typing userIds

export async function GET(request: NextRequest) {
  const { socket: res } = request as any;
  
  if (!res || !res.socket || !res.socket.server) {
    return new Response('WebSockets not supported', { status: 400 });
  }
  
  const wsServer = res.socket.server;
  
  if (wsServer.messagingWebSocketServer) {
    return new Response('Messaging WebSocket server already running', { status: 200 });
  }
  
  const { Server } = await import('ws');
  const wss = new Server({ noServer: true });
  
  wsServer.messagingWebSocketServer = wss;
  
  // Handle WebSocket connections
  wss.on('connection', async (ws: any, request: any, userContext: any) => {
    console.log('Messaging WebSocket client connected');
    
    let connectionId: string | null = null;
    let userId: string | null = null;
    let eventId: string | null = null;
    
    // Send welcome message
    ws.send(JSON.stringify({
      type: 'connection_established',
      data: {
        message: 'Connected to messaging server',
        timestamp: new Date().toISOString()
      }
    }));
    
    // Handle messages from client
    ws.on('message', async (message: any) => {
      try {
        const data = JSON.parse(message.toString());
        
        switch (data.type) {
          case 'auth':
            await handleAuthentication(ws, data.data);
            break;
          case 'message':
            await handleMessage(ws, data);
            break;
          case 'typing':
            await handleTyping(ws, data);
            break;
          case 'read_receipt':
            await handleReadReceipt(ws, data);
            break;
          case 'join_room':
            await handleJoinRoom(ws, data);
            break;
          case 'leave_room':
            await handleLeaveRoom(ws, data);
            break;
          case 'user_status':
            await handleUserStatus(ws, data);
            break;
          case 'ping':
            ws.send(JSON.stringify({
              type: 'pong',
              data: { timestamp: Date.now() }
            }));
            break;
        }
      } catch (error) {
        console.error('Error handling message:', error);
        ws.send(JSON.stringify({
          type: 'error',
          data: { message: 'Error processing message' }
        }));
      }
    });
    
    // Handle authentication
    async function handleAuthentication(ws: any, authData: any) {
      try {
        userId = authData.userId;
        eventId = authData.eventId;
        
        if (!userId || !eventId) {
          ws.send(JSON.stringify({
            type: 'auth_error',
            data: { message: 'Missing userId or eventId' }
          }));
          return;
        }
        
        // Verify user has access to the event
        const subscription = await db.eliteCommunication.findFirst({
          where: {
            userId,
            eventId,
            isActive: true
          }
        });
        
        if (!subscription) {
          ws.send(JSON.stringify({
            type: 'auth_error',
            data: { message: 'No active subscription found' }
          }));
          return;
        }
        
        // Store connection
        connectionId = `${userId}-${eventId}-${Date.now()}`;
        activeConnections.set(connectionId, {
          ws,
          userId,
          eventId,
          lastSeen: new Date(),
          status: 'online',
          tier: subscription.tier
        });
        
        ws.send(JSON.stringify({
          type: 'auth_success',
          data: {
            connectionId,
            tier: subscription.tier,
            timestamp: new Date().toISOString()
          }
        }));
        
        // Broadcast user online status
        broadcastToEvent(eventId, {
          type: 'user_status',
          data: {
            userId,
            status: 'online',
            timestamp: new Date().toISOString()
          }
        }, userId);
        
      } catch (error) {
        console.error('Authentication error:', error);
        ws.send(JSON.stringify({
          type: 'auth_error',
          data: { message: 'Authentication failed' }
        }));
      }
    }
    
    // Handle client disconnection
    ws.on('close', () => {
      console.log('Messaging WebSocket client disconnected');
      
      if (connectionId && userId && eventId) {
        activeConnections.delete(connectionId);
        
        // Remove from all rooms
        roomMemberships.forEach((members, roomId) => {
          members.delete(userId);
        });
        
        // Remove typing indicators
        typingIndicators.forEach((typingUsers, roomId) => {
          typingUsers.delete(userId);
        });
        
        // Broadcast user offline status
        broadcastToEvent(eventId, {
          type: 'user_status',
          data: {
            userId,
            status: 'offline',
            timestamp: new Date().toISOString()
          }
        }, userId);
      }
    });
  });
  
  // Handle WebSocket upgrade requests
  wsServer.server.on('upgrade', async (request: any, socket: any, head: any) => {
    const url = new URL(request.url as string, 'http://localhost');
    const userId = url.searchParams.get('userId');
    const eventId = url.searchParams.get('eventId');
    const token = url.searchParams.get('token');
    
    // Basic validation
    if (!userId || !eventId) {
      socket.write('HTTP/1.1 401 Unauthorized\r\n\r\n');
      socket.destroy();
      return;
    }
    
    // Upgrade the connection to WebSocket
    wss.handleUpgrade(request, socket, head, (ws: any) => {
      wss.emit('connection', ws, request, { userId, eventId, token });
    });
  });
  
  return new Response('Messaging WebSocket server started', { status: 200 });
}

// Helper functions
async function handleMessage(ws: any, data: any) {
  const { userId, eventId, data: messageData } = data;
  
  if (!userId || !eventId) return;
  
  try {
    // Save message to database
    const message = await db.message.create({
      data: {
        senderId: userId,
        recipientId: messageData.recipientId,
        eventId,
        content: messageData.content,
        messageType: messageData.messageType || 'TEXT',
        chatRoomId: messageData.chatRoomId || null
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    });
    
    // Broadcast to recipient or room
    if (messageData.chatRoomId) {
      broadcastToRoom(messageData.chatRoomId, {
        type: 'message',
        data: message
      });
    } else {
      broadcastToUser(messageData.recipientId, {
        type: 'message',
        data: message
      });
    }
    
    // Send confirmation to sender
    ws.send(JSON.stringify({
      type: 'message_sent',
      data: { messageId: message.id }
    }));
    
  } catch (error) {
    console.error('Error handling message:', error);
    ws.send(JSON.stringify({
      type: 'error',
      data: { message: 'Failed to send message' }
    }));
  }
}

async function handleTyping(ws: any, data: any) {
  const { userId, eventId, data: typingData } = data;
  
  if (!userId || !eventId) return;
  
  const { recipientId, isTyping, chatRoomId } = typingData;
  
  if (chatRoomId) {
    // Handle room typing
    const roomTyping = typingIndicators.get(chatRoomId) || new Set();
    
    if (isTyping) {
      roomTyping.add(userId);
    } else {
      roomTyping.delete(userId);
    }
    
    typingIndicators.set(chatRoomId, roomTyping);
    
    broadcastToRoom(chatRoomId, {
      type: 'typing',
      data: {
        userId,
        isTyping,
        chatRoomId
      }
    }, userId);
  } else if (recipientId) {
    // Handle direct message typing
    broadcastToUser(recipientId, {
      type: 'typing',
      data: {
        userId,
        isTyping
      }
    });
  }
}

async function handleReadReceipt(ws: any, data: any) {
  const { userId, data: receiptData } = data;
  
  if (!userId) return;
  
  try {
    // Update message read status
    await db.message.update({
      where: { id: receiptData.messageId },
      data: { readAt: new Date() }
    });
    
    // Notify sender
    broadcastToUser(receiptData.senderId, {
      type: 'read_receipt',
      data: {
        messageId: receiptData.messageId,
        readBy: userId,
        readAt: receiptData.readAt
      }
    });
    
  } catch (error) {
    console.error('Error handling read receipt:', error);
  }
}

async function handleJoinRoom(ws: any, data: any) {
  const { userId, data: roomData } = data;
  
  if (!userId) return;
  
  const { chatRoomId } = roomData;
  
  if (!roomMemberships.has(chatRoomId)) {
    roomMemberships.set(chatRoomId, new Set());
  }
  
  roomMemberships.get(chatRoomId)!.add(userId);
  
  // Notify room members
  broadcastToRoom(chatRoomId, {
    type: 'user_joined_room',
    data: {
      userId,
      chatRoomId,
      timestamp: new Date().toISOString()
    }
  }, userId);
}

async function handleLeaveRoom(ws: any, data: any) {
  const { userId, data: roomData } = data;
  
  if (!userId) return;
  
  const { chatRoomId } = roomData;
  
  if (roomMemberships.has(chatRoomId)) {
    roomMemberships.get(chatRoomId)!.delete(userId);
  }
  
  // Remove typing indicator
  if (typingIndicators.has(chatRoomId)) {
    typingIndicators.get(chatRoomId)!.delete(userId);
  }
  
  // Notify room members
  broadcastToRoom(chatRoomId, {
    type: 'user_left_room',
    data: {
      userId,
      chatRoomId,
      timestamp: new Date().toISOString()
    }
  }, userId);
}

async function handleUserStatus(ws: any, data: any) {
  const { userId, eventId, data: statusData } = data;
  
  if (!userId || !eventId) return;
  
  // Update connection status
  for (const [connectionId, connection] of activeConnections) {
    if (connection.userId === userId && connection.eventId === eventId) {
      connection.status = statusData.status;
      connection.lastSeen = new Date();
      break;
    }
  }
  
  // Broadcast status change
  broadcastToEvent(eventId, {
    type: 'user_status',
    data: {
      userId,
      status: statusData.status,
      timestamp: new Date().toISOString()
    }
  }, userId);
}

// Broadcast functions
function broadcastToUser(userId: string, message: any) {
  for (const connection of activeConnections.values()) {
    if (connection.userId === userId && connection.ws.readyState === 1) {
      connection.ws.send(JSON.stringify(message));
    }
  }
}

function broadcastToRoom(roomId: string, message: any, excludeUserId?: string) {
  const roomMembers = roomMemberships.get(roomId);
  if (!roomMembers) return;
  
  for (const userId of roomMembers) {
    if (excludeUserId && userId === excludeUserId) continue;
    broadcastToUser(userId, message);
  }
}

function broadcastToEvent(eventId: string, message: any, excludeUserId?: string) {
  for (const connection of activeConnections.values()) {
    if (connection.eventId === eventId && 
        connection.ws.readyState === 1 &&
        (!excludeUserId || connection.userId !== excludeUserId)) {
      connection.ws.send(JSON.stringify(message));
    }
  }
}
