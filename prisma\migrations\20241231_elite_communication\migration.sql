-- CreateEnum
CREATE TYPE "EliteTier" AS ENUM ('BASIC', 'ELIT<PERSON>', 'ELITE_PRO');

-- CreateEnum
CREATE TYPE "EliteSubscriptionType" AS ENUM ('PER_EVENT', 'MONTHLY', 'ANNUAL');

-- CreateEnum
CREATE TYPE "AttendeePrivacyLevel" AS ENUM ('PUBLIC', 'ELITE_ONLY', 'CONNECTIONS_ONLY', 'HIDDEN');

-- CreateEnum
CREATE TYPE "AttendeeMessageLevel" AS ENUM ('EVERYONE', 'ELITE_ONLY', 'CONNECTIONS_ONLY', 'NONE');

-- C<PERSON><PERSON>num
CREATE TYPE "MessageType" AS ENUM ('TEXT', 'IMAGE', 'FILE', 'SYSTEM');

-- CreateEnum
CREATE TYPE "ChatRoomType" AS ENUM ('ELITE_EXCLUSIVE', 'ELITE_PRO_EXCLUSIVE', 'GENERAL');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "MeetingType" AS ENUM ('VIRTUAL', 'IN_PERSON', 'HYBRID');

-- CreateEnum
CREATE TYPE "MeetingStatus" AS ENUM ('PENDING', 'ACCEPTED', 'DECLINED', 'CANCELLED', 'COMPLETED');

-- CreateEnum
CREATE TYPE "ReportType" AS ENUM ('INAPPROPRIATE_MESSAGE', 'SPAM', 'HARASSMENT', 'FAKE_PROFILE', 'OTHER');

-- CreateEnum
CREATE TYPE "ReportStatus" AS ENUM ('PENDING', 'UNDER_REVIEW', 'RESOLVED', 'DISMISSED');

-- CreateTable
CREATE TABLE "EliteCommunication" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "eventId" TEXT NOT NULL,
    "tier" "EliteTier" NOT NULL DEFAULT 'BASIC',
    "subscriptionType" "EliteSubscriptionType" NOT NULL DEFAULT 'PER_EVENT',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "expiresAt" TIMESTAMP(3),
    "purchasePrice" DOUBLE PRECISION,
    "stripeSubscriptionId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EliteCommunication_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AttendeeProfile" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "eventId" TEXT NOT NULL,
    "displayName" TEXT,
    "bio" TEXT,
    "company" TEXT,
    "role" TEXT,
    "industry" TEXT,
    "interests" TEXT[],
    "networkingGoals" TEXT,
    "profilePhoto" TEXT,
    "linkedinUrl" TEXT,
    "twitterUrl" TEXT,
    "websiteUrl" TEXT,
    "isDiscoverable" BOOLEAN NOT NULL DEFAULT false,
    "privacyLevel" "AttendeePrivacyLevel" NOT NULL DEFAULT 'ELITE_ONLY',
    "allowMessages" "AttendeeMessageLevel" NOT NULL DEFAULT 'ELITE_ONLY',
    "allowMeetings" BOOLEAN NOT NULL DEFAULT true,
    "timezone" TEXT,
    "availableHours" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AttendeeProfile_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Message" (
    "id" TEXT NOT NULL,
    "senderId" TEXT NOT NULL,
    "receiverId" TEXT NOT NULL,
    "eventId" TEXT NOT NULL,
    "chatRoomId" TEXT,
    "content" TEXT NOT NULL,
    "messageType" "MessageType" NOT NULL DEFAULT 'TEXT',
    "fileUrl" TEXT,
    "fileName" TEXT,
    "fileSize" INTEGER,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "readAt" TIMESTAMP(3),
    "isEdited" BOOLEAN NOT NULL DEFAULT false,
    "editedAt" TIMESTAMP(3),
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,
    "deletedAt" TIMESTAMP(3),
    "replyToId" TEXT,
    "reactions" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Message_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ChatRoom" (
    "id" TEXT NOT NULL,
    "eventId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "roomType" "ChatRoomType" NOT NULL DEFAULT 'ELITE_EXCLUSIVE',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "maxMembers" INTEGER,
    "createdById" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ChatRoom_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ChatRoomMember" (
    "id" TEXT NOT NULL,
    "chatRoomId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "joinedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "lastReadAt" TIMESTAMP(3),
    "isModerator" BOOLEAN NOT NULL DEFAULT false,
    "isMuted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "ChatRoomMember_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MeetingRequest" (
    "id" TEXT NOT NULL,
    "senderId" TEXT NOT NULL,
    "receiverId" TEXT NOT NULL,
    "eventId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "proposedStartTime" TIMESTAMP(3) NOT NULL,
    "proposedEndTime" TIMESTAMP(3) NOT NULL,
    "timezone" TEXT NOT NULL,
    "meetingType" "MeetingType" NOT NULL DEFAULT 'VIRTUAL',
    "meetingUrl" TEXT,
    "location" TEXT,
    "status" "MeetingStatus" NOT NULL DEFAULT 'PENDING',
    "responseMessage" TEXT,
    "respondedAt" TIMESTAMP(3),
    "reminderSent" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MeetingRequest_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AttendeeBlock" (
    "id" TEXT NOT NULL,
    "blockerId" TEXT NOT NULL,
    "blockedUserId" TEXT NOT NULL,
    "eventId" TEXT NOT NULL,
    "reason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AttendeeBlock_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CommunicationReport" (
    "id" TEXT NOT NULL,
    "reporterId" TEXT NOT NULL,
    "reportedUserId" TEXT NOT NULL,
    "eventId" TEXT NOT NULL,
    "messageId" TEXT,
    "reportType" "ReportType" NOT NULL,
    "reason" TEXT NOT NULL,
    "description" TEXT,
    "status" "ReportStatus" NOT NULL DEFAULT 'PENDING',
    "reviewedById" TEXT,
    "reviewedAt" TIMESTAMP(3),
    "actionTaken" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CommunicationReport_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "EliteCommunication_userId_eventId_key" ON "EliteCommunication"("userId", "eventId");

-- CreateIndex
CREATE INDEX "EliteCommunication_eventId_tier_idx" ON "EliteCommunication"("eventId", "tier");

-- CreateIndex
CREATE INDEX "EliteCommunication_userId_isActive_idx" ON "EliteCommunication"("userId", "isActive");

-- CreateIndex
CREATE UNIQUE INDEX "AttendeeProfile_userId_eventId_key" ON "AttendeeProfile"("userId", "eventId");

-- CreateIndex
CREATE INDEX "AttendeeProfile_eventId_isDiscoverable_idx" ON "AttendeeProfile"("eventId", "isDiscoverable");

-- CreateIndex
CREATE INDEX "AttendeeProfile_eventId_industry_idx" ON "AttendeeProfile"("eventId", "industry");

-- CreateIndex
CREATE INDEX "AttendeeProfile_eventId_company_idx" ON "AttendeeProfile"("eventId", "company");

-- CreateIndex
CREATE INDEX "Message_eventId_createdAt_idx" ON "Message"("eventId", "createdAt");

-- CreateIndex
CREATE INDEX "Message_senderId_receiverId_idx" ON "Message"("senderId", "receiverId");

-- CreateIndex
CREATE INDEX "Message_chatRoomId_createdAt_idx" ON "Message"("chatRoomId", "createdAt");

-- CreateIndex
CREATE INDEX "Message_isDeleted_createdAt_idx" ON "Message"("isDeleted", "createdAt");

-- CreateIndex
CREATE INDEX "ChatRoom_eventId_roomType_idx" ON "ChatRoom"("eventId", "roomType");

-- CreateIndex
CREATE INDEX "ChatRoom_eventId_isActive_idx" ON "ChatRoom"("eventId", "isActive");

-- CreateIndex
CREATE UNIQUE INDEX "ChatRoomMember_chatRoomId_userId_key" ON "ChatRoomMember"("chatRoomId", "userId");

-- CreateIndex
CREATE INDEX "MeetingRequest_eventId_status_idx" ON "MeetingRequest"("eventId", "status");

-- CreateIndex
CREATE INDEX "MeetingRequest_senderId_status_idx" ON "MeetingRequest"("senderId", "status");

-- CreateIndex
CREATE INDEX "MeetingRequest_receiverId_status_idx" ON "MeetingRequest"("receiverId", "status");

-- CreateIndex
CREATE INDEX "MeetingRequest_proposedStartTime_idx" ON "MeetingRequest"("proposedStartTime");

-- CreateIndex
CREATE UNIQUE INDEX "AttendeeBlock_blockerId_blockedUserId_eventId_key" ON "AttendeeBlock"("blockerId", "blockedUserId", "eventId");

-- CreateIndex
CREATE INDEX "CommunicationReport_eventId_status_idx" ON "CommunicationReport"("eventId", "status");

-- CreateIndex
CREATE INDEX "CommunicationReport_reportedUserId_status_idx" ON "CommunicationReport"("reportedUserId", "status");

-- AddForeignKey
ALTER TABLE "EliteCommunication" ADD CONSTRAINT "EliteCommunication_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EliteCommunication" ADD CONSTRAINT "EliteCommunication_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "Event"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AttendeeProfile" ADD CONSTRAINT "AttendeeProfile_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AttendeeProfile" ADD CONSTRAINT "AttendeeProfile_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "Event"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_senderId_fkey" FOREIGN KEY ("senderId") REFERENCES "AttendeeProfile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_receiverId_fkey" FOREIGN KEY ("receiverId") REFERENCES "AttendeeProfile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "Event"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_chatRoomId_fkey" FOREIGN KEY ("chatRoomId") REFERENCES "ChatRoom"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_replyToId_fkey" FOREIGN KEY ("replyToId") REFERENCES "Message"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatRoom" ADD CONSTRAINT "ChatRoom_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "Event"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatRoom" ADD CONSTRAINT "ChatRoom_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatRoomMember" ADD CONSTRAINT "ChatRoomMember_chatRoomId_fkey" FOREIGN KEY ("chatRoomId") REFERENCES "ChatRoom"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatRoomMember" ADD CONSTRAINT "ChatRoomMember_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MeetingRequest" ADD CONSTRAINT "MeetingRequest_senderId_fkey" FOREIGN KEY ("senderId") REFERENCES "AttendeeProfile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MeetingRequest" ADD CONSTRAINT "MeetingRequest_receiverId_fkey" FOREIGN KEY ("receiverId") REFERENCES "AttendeeProfile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MeetingRequest" ADD CONSTRAINT "MeetingRequest_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "Event"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AttendeeBlock" ADD CONSTRAINT "AttendeeBlock_blockerId_fkey" FOREIGN KEY ("blockerId") REFERENCES "AttendeeProfile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AttendeeBlock" ADD CONSTRAINT "AttendeeBlock_blockedUserId_fkey" FOREIGN KEY ("blockedUserId") REFERENCES "AttendeeProfile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AttendeeBlock" ADD CONSTRAINT "AttendeeBlock_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "Event"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CommunicationReport" ADD CONSTRAINT "CommunicationReport_reporterId_fkey" FOREIGN KEY ("reporterId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CommunicationReport" ADD CONSTRAINT "CommunicationReport_reportedUserId_fkey" FOREIGN KEY ("reportedUserId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CommunicationReport" ADD CONSTRAINT "CommunicationReport_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "Event"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CommunicationReport" ADD CONSTRAINT "CommunicationReport_messageId_fkey" FOREIGN KEY ("messageId") REFERENCES "Message"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CommunicationReport" ADD CONSTRAINT "CommunicationReport_reviewedById_fkey" FOREIGN KEY ("reviewedById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
