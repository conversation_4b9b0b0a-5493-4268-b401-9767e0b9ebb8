# NFC Payment System Documentation

## Overview

The NFC Payment System is a comprehensive solution for event organizers and vendors to process payments using NFC cards. The system supports both online and offline transaction processing, card management, receipt generation, refund capabilities, and sales analytics.

## System Components

### 1. Database Models

- **NFCCard**: Stores information about NFC cards, including their UID, balance, status, and assignment to users.
- **NFCTerminalSettings**: Stores vendor-specific terminal settings like offline mode, auto-sync, etc.
- **NFCSystemSettings**: Stores event-specific system settings like currency, transaction limits, etc.
- **VendorNFCTransaction**: Records all NFC payment transactions.
- **VendorNFCTransactionItem**: Records individual items in a transaction.

### 2. API Endpoints

#### Vendor APIs

- **POST /api/vendors/nfc/transaction**: Process a new NFC payment transaction.
- **GET /api/vendors/nfc/transactions**: Get a list of transactions with filtering and pagination.
- **GET /api/vendors/nfc/transactions/:id**: Get details of a specific transaction.
- **GET /api/vendors/nfc/analytics**: Get analytics data for NFC transactions.
- **GET /api/vendor/nfc/settings**: Get vendor-specific terminal settings.
- **PATCH /api/vendor/nfc/settings**: Update vendor-specific terminal settings.

#### Organizer APIs

- **GET /api/organizer/nfc/cards**: Get a list of NFC cards for an event.
- **POST /api/organizer/nfc/cards**: Register a new NFC card.
- **GET /api/organizer/nfc/cards/:id**: Get details of a specific NFC card.
- **PATCH /api/organizer/nfc/cards/:id**: Update a specific NFC card.
- **DELETE /api/organizer/nfc/cards/:id**: Delete a specific NFC card.
- **POST /api/organizer/nfc/cards/bulk**: Perform bulk actions on NFC cards.
- **PUT /api/organizer/nfc/cards/bulk**: Bulk import NFC cards.
- **GET /api/organizer/nfc/settings**: Get event-specific system settings.
- **POST /api/organizer/nfc/settings**: Create or update event-specific system settings.

### 3. Frontend Components

- **NFCCardReader**: Component for reading NFC cards.
- **NFCTransactionProcessor**: Component for processing NFC transactions.
- **EnhancedTransactionProcessor**: Advanced component with product catalog and cart functionality.
- **TransactionReceipt**: Component for generating and printing transaction receipts.
- **TransactionHistory**: Component for displaying transaction history.
- **NFCAnalytics**: Component for displaying analytics data.

## How to Use

### For Organizers

1. **Set Up NFC System**:
   - Navigate to the Organizer Dashboard
   - Go to NFC Settings
   - Configure system settings like currency, transaction limits, etc.

2. **Manage NFC Cards**:
   - Navigate to the Card Management page
   - Register new NFC cards
   - Assign cards to attendees
   - View card transaction history
   - Manage card balances

3. **Monitor Transactions**:
   - View all transactions across all vendors
   - Filter transactions by vendor, date, status, etc.
   - Export transaction data for reporting

4. **Analyze Data**:
   - View analytics dashboards
   - Track vendor performance
   - Monitor sales by product category
   - Analyze transaction patterns

### For Vendors

1. **Set Up Terminal**:
   - Navigate to the Vendor Dashboard
   - Go to NFC Settings
   - Configure terminal settings like offline mode, auto-sync, etc.

2. **Process Transactions**:
   - Navigate to the Enhanced POS page
   - Add products to the cart
   - Scan the customer's NFC card
   - Process the transaction
   - Generate a receipt

3. **View Transaction History**:
   - View all transactions
   - Filter transactions by date, status, etc.
   - View transaction details
   - Print or email receipts

4. **Analyze Sales**:
   - View sales analytics
   - Track best-selling products
   - Monitor daily/weekly/monthly sales
   - Analyze customer behavior

## Technical Implementation

### Database Schema

```prisma
model NFCCard {
  id              String                 @id @default(cuid())
  uid             String                 @unique
  isActive        Boolean                @default(true)
  status          String                 @default("active") // active, inactive, lost
  balance         Float                  @default(0)
  assignedTo      String?
  lastUsed        DateTime?
  userId          String?
  eventId         String
  createdAt       DateTime               @default(now())
  updatedAt       DateTime               @updatedAt
  user            User?                  @relation(fields: [userId], references: [id])
  event           Event                  @relation(fields: [eventId], references: [id])
  nfcTransactions VendorNFCTransaction[]

  @@index([uid])
  @@index([isActive])
  @@index([userId])
  @@index([eventId])
  @@index([status])
}

model NFCTerminalSettings {
  id                   String    @id @default(cuid())
  vendorId             String    @unique
  terminalName         String    @default("Main Terminal")
  offlineMode          Boolean   @default(false)
  autoSync             Boolean   @default(true)
  notificationsEnabled Boolean   @default(true)
  autoPrint            Boolean   @default(false)
  deviceId             String    @unique
  lastSyncTime         DateTime?
  softwareVersion      String?   @default("1.0.0")
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt
  vendor               User      @relation(fields: [vendorId], references: [id], onDelete: Cascade)

  @@index([vendorId])
}

model NFCSystemSettings {
  id                    String    @id @default(cuid())
  eventId               String    @unique
  systemName            String
  currencySymbol        String
  defaultLanguage       String
  maxTransactionAmount  Float
  requirePinForHighValue Boolean
  highValueThreshold    Float?
  cardLockoutThreshold  Int
  offlineModeEnabled    Boolean
  maxOfflineTransactions Int?
  offlineTransactionLimit Float?
  syncInterval          Int?
  receiptEnabled        Boolean
  analyticsEnabled      Boolean
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  event                 Event     @relation(fields: [eventId], references: [id], onDelete: Cascade)
}

model VendorNFCTransaction {
  id          String                     @id @default(cuid())
  vendorId    String
  eventId     String
  cardId      String
  userId      String
  amount      Float
  currency    String                     @default("ZMW")
  status      TransactionStatus          @default(PENDING)
  reference   String?
  notes       String?
  createdAt   DateTime                   @default(now())
  processedAt DateTime?
  nfcCard     NFCCard                    @relation(fields: [cardId], references: [id])
  event       Event                      @relation(fields: [eventId], references: [id])
  user        User                       @relation(fields: [userId], references: [id])
  vendor      VendorProfile              @relation(fields: [vendorId], references: [id])
  products    VendorNFCTransactionItem[]

  @@index([vendorId])
  @@index([eventId])
  @@index([cardId])
  @@index([userId])
  @@index([status])
  @@index([createdAt])
}

model VendorNFCTransactionItem {
  id            String               @id @default(cuid())
  transactionId String
  productId     String
  quantity      Int
  unitPrice     Float
  totalPrice    Float
  createdAt     DateTime             @default(now())
  product       Product              @relation(fields: [productId], references: [id])
  transaction   VendorNFCTransaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)

  @@index([transactionId])
  @@index([productId])
}
```

### API Implementation

The API endpoints are implemented using Next.js API routes with proper authentication and validation. Each endpoint follows RESTful principles and includes comprehensive error handling.

### Frontend Implementation

The frontend components are implemented using React with TypeScript. They use the Shadcn UI component library for consistent styling and user experience.

## Security Considerations

1. **Authentication**: All API endpoints require authentication.
2. **Authorization**: Endpoints enforce role-based access control.
3. **Validation**: All input data is validated using Zod schemas.
4. **Transaction Integrity**: Database transactions are used to ensure data consistency.
5. **Offline Security**: Offline transactions have limits and require synchronization.

## Offline Mode

The system supports offline mode for vendors to continue processing transactions when internet connectivity is unavailable:

1. Transactions are stored locally in the browser's IndexedDB.
2. When connectivity is restored, transactions are synchronized with the server.
3. Conflict resolution is handled automatically.
4. Limits are enforced to prevent fraud.

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL database
- NFC-enabled devices for testing

### Installation

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables
4. Run database migrations: `npx prisma migrate dev`
5. Seed the database: `node scripts/apply-nfc-migrations.js`
6. Start the development server: `npm run dev`

### Testing

1. Navigate to the Organizer Dashboard
2. Set up NFC system settings
3. Register NFC cards
4. Navigate to the Vendor Dashboard
5. Process test transactions

## Troubleshooting

### Common Issues

1. **NFC Card Not Detected**:
   - Ensure the device supports NFC
   - Check if NFC is enabled in the browser
   - Try using a different card

2. **Transaction Failed**:
   - Check card balance
   - Verify vendor is approved for the event
   - Check product stock levels

3. **Offline Mode Not Working**:
   - Ensure offline mode is enabled in settings
   - Check browser storage permissions
   - Verify sync settings

## Support

For technical support, please contact the development <NAME_EMAIL>.
