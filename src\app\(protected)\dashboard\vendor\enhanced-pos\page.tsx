'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Loader2, ShoppingCart, Plus, Minus, Trash2 } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';

// Define product type
interface Product {
  id: string;
  name: string;
  price: number;
  stockQuantity: number;
  imagePath?: string;
  description?: string;
}

interface CartItem {
  product: Product;
  quantity: number;
}

export default function EnhancedPOSPage() {
  const { data: session } = useSession();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [cardUid, setCardUid] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [transactionComplete, setTransactionComplete] = useState(false);
  const [lastTransactionId, setLastTransactionId] = useState<string | null>(null);

  // Calculate cart totals
  const cartTotal = cart.reduce((total, item) => {
    return total + (item.product.price * item.quantity);
  }, 0);

  const cartItemCount = cart.reduce((count, item) => {
    return count + item.quantity;
  }, 0);

  // Fetch products
  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      setError(null);

      try {
        // For simplicity, we'll use sample products
        const sampleProducts: Product[] = [
          {
            id: 'sample-1',
            name: 'Event T-Shirt',
            price: 25.99,
            stockQuantity: 50,
            description: 'Official event t-shirt'
          },
          {
            id: 'sample-2',
            name: 'Event Cap',
            price: 15.99,
            stockQuantity: 30,
            description: 'Official event cap'
          },
          {
            id: 'sample-3',
            name: 'Event Poster',
            price: 9.99,
            stockQuantity: 100,
            description: 'Official event poster'
          },
          {
            id: 'sample-4',
            name: 'Water Bottle',
            price: 4.99,
            stockQuantity: 200,
            description: 'Branded water bottle'
          },
          {
            id: 'sample-5',
            name: 'Snack Pack',
            price: 7.99,
            stockQuantity: 75,
            description: 'Assorted snacks'
          },
          {
            id: 'sample-6',
            name: 'Event Wristband',
            price: 3.99,
            stockQuantity: 500,
            description: 'Official event wristband'
          }
        ];

        setProducts(sampleProducts);
      } catch (err) {
        console.error('Error fetching products:', err);
        setError('Failed to load products. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Add product to cart
  const addToCart = (product: Product) => {
    setCart(prevCart => {
      const existingItem = prevCart.find(item => item.product.id === product.id);

      if (existingItem) {
        // Don't exceed stock quantity
        if (existingItem.quantity >= product.stockQuantity) {
          toast({
            title: 'Stock limit reached',
            description: `Only ${product.stockQuantity} items available in stock`,
            variant: 'destructive',
          });
          return prevCart;
        }

        return prevCart.map(item =>
          item.product.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        return [...prevCart, { product, quantity: 1 }];
      }
    });
  };

  // Remove product from cart
  const removeFromCart = (productId: string) => {
    setCart(prevCart => {
      const existingItem = prevCart.find(item => item.product.id === productId);

      if (existingItem && existingItem.quantity > 1) {
        return prevCart.map(item =>
          item.product.id === productId
            ? { ...item, quantity: item.quantity - 1 }
            : item
        );
      } else {
        return prevCart.filter(item => item.product.id !== productId);
      }
    });
  };

  // Delete product from cart
  const deleteFromCart = (productId: string) => {
    setCart(prevCart => prevCart.filter(item => item.product.id !== productId));
  };

  // Start NFC scanning
  const startNfcScan = async () => {
    if (cart.length === 0) {
      toast({
        title: 'Empty cart',
        description: 'Please add products to the cart before scanning',
        variant: 'destructive',
      });
      return;
    }

    setIsScanning(true);

    try {
      // Check if NFC is supported
      if (!('NDEFReader' in window)) {
        toast({
          title: 'NFC Not Supported',
          description: 'Your device does not support NFC scanning. Please enter the card UID manually.',
          variant: 'destructive',
        });
        setIsScanning(false);
        return;
      }

      toast({
        title: 'NFC Scanner Active',
        description: 'Please tap the customer\'s NFC card or wristband on your device.',
      });

      // Create a new NFC reader
      const ndef = new (window as any).NDEFReader();

      // Start scanning - this must be called from a user gesture
      await ndef.scan();

      // Set up event listeners
      ndef.addEventListener("reading", ({ serialNumber }: any) => {
        // Handle the card read event
        setCardUid(serialNumber);
        setIsScanning(false);

        toast({
          title: 'NFC Card Detected',
          description: `Card ID: ${serialNumber}`,
        });

        // Automatically process the transaction after scanning
        processTransaction(serialNumber);
      });

      ndef.addEventListener("readingerror", () => {
        setIsScanning(false);
        toast({
          title: 'Error Reading NFC Card',
          description: 'There was an error reading the NFC card. Please try again or enter the UID manually.',
          variant: 'destructive',
        });
      });
    } catch (error) {
      console.error('Error setting up NFC:', error);
      setIsScanning(false);

      if ((error as Error).name === 'NotAllowedError') {
        toast({
          title: 'NFC Permission Denied',
          description: 'Please enable NFC permissions in your browser.',
          variant: 'destructive',
        });
      } else if ((error as Error).name === 'NotReadableError') {
        toast({
          title: 'NFC Hardware Error',
          description: 'Cannot connect to NFC hardware. Please check your device settings.',
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'NFC Error',
          description: 'Failed to setup NFC reader. Please enter the card UID manually.',
          variant: 'destructive',
        });
      }
    }
  };

  // Cancel NFC scanning
  const cancelNfcScan = () => {
    setIsScanning(false);
    toast({
      title: 'NFC Scanning Cancelled',
      description: 'NFC scanning has been cancelled.',
    });
  };

  // Process the transaction
  const processTransaction = async (uid: string = cardUid) => {
    if (cart.length === 0) {
      toast({
        title: 'Empty cart',
        description: 'Please add products to the cart before processing',
        variant: 'destructive',
      });
      return;
    }

    if (!uid) {
      toast({
        title: 'NFC Card Required',
        description: 'Please scan an NFC card or enter the UID manually',
        variant: 'destructive',
      });
      return;
    }

    setIsProcessing(true);
    setIsScanning(false);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Generate a random transaction ID
      const transactionId = 'TX-' + Math.random().toString(36).substring(2, 10).toUpperCase();

      setLastTransactionId(transactionId);
      setTransactionComplete(true);

      toast({
        title: 'Transaction successful',
        description: `Transaction ID: ${transactionId}`,
      });
    } catch (error) {
      console.error('Transaction error:', error);
      toast({
        title: 'Transaction failed',
        description: error instanceof Error ? error.message : 'An error occurred during the transaction',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Reset the transaction
  const resetTransaction = () => {
    setCart([]);
    setCardUid('');
    setIsScanning(false);
    setTransactionComplete(false);
    setLastTransactionId(null);
  };

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Enhanced POS System</h1>
        <p className="text-gray-600 mt-1">
          Process transactions with NFC support
        </p>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {loading ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
            <span>Loading products...</span>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Product Selection */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Products</CardTitle>
              <CardDescription>
                Select products for the customer
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {products.map(product => (
                  <Card key={product.id} className="overflow-hidden">
                    <CardContent className="p-4">
                      <h3 className="font-medium">{product.name}</h3>
                      <p className="text-sm text-gray-500">{product.description}</p>
                      <div className="flex justify-between items-center mt-2">
                        <p className="font-bold">K{product.price.toFixed(2)}</p>
                        <Badge variant="outline">Stock: {product.stockQuantity}</Badge>
                      </div>
                      <Button
                        onClick={() => addToCart(product)}
                        className="w-full mt-3"
                        size="sm"
                        disabled={
                          isProcessing ||
                          transactionComplete ||
                          product.stockQuantity === 0 ||
                          (cart.find(item => item.product.id === product.id)?.quantity || 0) >= product.stockQuantity
                        }
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Add to Cart
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Cart and Payment */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ShoppingCart className="h-5 w-5 mr-2" />
                Cart
                {cartItemCount > 0 && (
                  <Badge className="ml-2">{cartItemCount}</Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {cart.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <ShoppingCart className="h-12 w-12 mx-auto text-gray-300 mb-3" />
                  <p>Your cart is empty</p>
                  <p className="text-sm mt-1">Add products to get started</p>
                </div>
              ) : (
                <>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Product</TableHead>
                        <TableHead className="text-right">Price</TableHead>
                        <TableHead className="text-center">Qty</TableHead>
                        <TableHead className="text-right">Total</TableHead>
                        <TableHead></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {cart.map(item => (
                        <TableRow key={item.product.id}>
                          <TableCell className="font-medium">{item.product.name}</TableCell>
                          <TableCell className="text-right">K{item.product.price.toFixed(2)}</TableCell>
                          <TableCell>
                            <div className="flex items-center justify-center">
                              <Button
                                variant="outline"
                                size="icon"
                                className="h-6 w-6"
                                onClick={() => removeFromCart(item.product.id)}
                                disabled={isProcessing || transactionComplete}
                              >
                                <Minus className="h-3 w-3" />
                              </Button>
                              <span className="mx-2">{item.quantity}</span>
                              <Button
                                variant="outline"
                                size="icon"
                                className="h-6 w-6"
                                onClick={() => addToCart(item.product)}
                                disabled={
                                  isProcessing ||
                                  transactionComplete ||
                                  item.quantity >= item.product.stockQuantity
                                }
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            K{(item.product.price * item.quantity).toFixed(2)}
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                              onClick={() => deleteFromCart(item.product.id)}
                              disabled={isProcessing || transactionComplete}
                            >
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  <div className="mt-4 pt-4 border-t">
                    <div className="flex justify-between font-bold text-lg">
                      <span>Total:</span>
                      <span>K{cartTotal.toFixed(2)}</span>
                    </div>
                  </div>
                </>
              )}

              <Separator className="my-4" />

              {transactionComplete ? (
                <div className="bg-green-50 p-4 rounded-md mt-4">
                  <div>
                    <h3 className="font-medium text-green-800">Transaction Complete</h3>
                    <p className="text-sm text-green-700 mt-1">
                      Transaction ID: {lastTransactionId}
                    </p>
                  </div>
                  <Button
                    className="w-full mt-4"
                    onClick={resetTransaction}
                  >
                    New Transaction
                  </Button>
                </div>
              ) : (
                <div className="mt-4">
                  <div className="space-y-4">
                    {/* NFC Scan Button or Scanning Indicator */}
                    {isScanning ? (
                      <div className="space-y-3">
                        <div className="bg-blue-50 border border-blue-200 rounded-md p-4 text-center">
                          <div className="flex justify-center mb-2">
                            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                          </div>
                          <h3 className="font-medium text-blue-800">NFC Scanner Active</h3>
                          <p className="text-sm text-blue-600 mt-1">
                            Please tap the customer's NFC card or wristband on your device
                          </p>
                        </div>
                        <Button
                          className="w-full"
                          variant="outline"
                          onClick={cancelNfcScan}
                        >
                          Cancel Scanning
                        </Button>
                      </div>
                    ) : (
                      <Button
                        className="w-full bg-blue-600 hover:bg-blue-700"
                        onClick={startNfcScan}
                        disabled={isProcessing || cart.length === 0}
                      >
                        <ShoppingCart className="mr-2 h-4 w-4" />
                        Scan NFC Card
                      </Button>
                    )}

                    {!isScanning && (
                      <>
                        <div className="relative">
                          <div className="absolute inset-0 flex items-center">
                            <span className="w-full border-t" />
                          </div>
                          <div className="relative flex justify-center text-xs uppercase">
                            <span className="bg-white px-2 text-muted-foreground">
                              or enter manually
                            </span>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="cardUid">NFC Card UID</Label>
                          <div className="flex gap-2">
                            <Input
                              id="cardUid"
                              placeholder="Enter NFC card UID manually"
                              value={cardUid}
                              onChange={(e) => setCardUid(e.target.value)}
                              disabled={isProcessing}
                              className="flex-1"
                            />
                            <Button
                              onClick={() => processTransaction()}
                              disabled={isProcessing || cart.length === 0 || !cardUid}
                              className="whitespace-nowrap"
                            >
                              {isProcessing ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Processing...
                                </>
                              ) : (
                                'Process'
                              )}
                            </Button>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
