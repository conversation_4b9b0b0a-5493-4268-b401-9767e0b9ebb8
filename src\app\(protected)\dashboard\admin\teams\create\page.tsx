'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Skeleton } from '@/components/ui/skeleton';

export default function CreateTeamRedirectPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the correct team creation page
    router.push('/admin/teams/create');
  }, [router]);

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <Skeleton className="h-10 w-64" />
          <Skeleton className="h-4 w-96 mt-2" />
        </div>
      </div>
      <div className="text-center py-12">
        <p className="text-gray-500">Redirecting to team creation page...</p>
      </div>
    </div>
  );
}
